import { motion } from "framer-motion";
import { Camera } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface GalleryHeaderProps {
  canAddMore: boolean;
  isUploading: boolean;
  onUploadClick: () => void;
}

export default function GalleryHeader({
  canAddMore,
  isUploading,
  onUploadClick,
}: GalleryHeaderProps) {
  return (
    <motion.div
      className="flex items-center justify-between"
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.1 }}
    >
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Gallery</h1>
        <p className="text-muted-foreground mt-1">
          Showcase your business with beautiful photos
        </p>
      </div>
      <div className="flex items-center gap-3">
        <Button
          onClick={onUploadClick}
          disabled={!canAddMore || isUploading}
          className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium rounded-full"
          size="sm"
        >
          <Camera className="mr-2 h-4 w-4" />
          Add Photo
        </Button>
      </div>
    </motion.div>
  );
}
