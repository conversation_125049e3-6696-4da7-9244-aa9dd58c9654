module.exports = {

"[project]/app/auth/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00a78b43259bdfa35946a0918da66b9382dcd7b4dc":"signOutUser"},"",""] */ __turbopack_context__.s({
    "signOutUser": (()=>signOutUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function signOutUser() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { error: _error } = await supabase.auth.signOut();
        // Note: Sign out errors are typically not critical for user experience
        // The user will be redirected to login regardless
        // Explicitly clear auth cookies to ensure logout
        const cookieStore = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i).then((m)=>m.cookies());
        const cookiesToClear = [
            "sb-access-token",
            "sb-refresh-token"
        ];
        for (const cookieName of cookiesToClear){
            try {
                cookieStore.set(cookieName, "", {
                    expires: new Date(0),
                    maxAge: -1
                });
            } catch  {
            // Cookie clearing errors are not critical for sign out
            // Continue with the sign out process
            }
        }
    } catch  {
    // Even if sign out fails, redirect to login for security
    // User will be treated as logged out
    }
    // Redirect to login with a flag to prevent middleware redirect loop
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?logged_out=true");
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    signOutUser
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signOutUser, "00a78b43259bdfa35946a0918da66b9382dcd7b4dc", null);
}}),
"[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminClient": (()=>createAdminClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-rsc] (ecmascript) <locals>");
;
function createAdminClient() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
}
}}),
"[project]/lib/utils/storage-paths.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Scalable Storage Path Utilities
 *
 * This module provides utilities for generating scalable storage paths
 * that can handle billions of users efficiently using hash-based distribution.
 */ /**
 * Generate scalable user path using hash-based distribution
 *
 * @param userId - The user's UUID
 * @returns Scalable path: users/{prefix}/{midfix}/{userId}
 *
 * Example:
 * - Input: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 * - Output: "users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 */ __turbopack_context__.s({
    "PathValidator": (()=>PathValidator),
    "StorageAnalytics": (()=>StorageAnalytics),
    "getCustomAdImagePath": (()=>getCustomAdImagePath),
    "getCustomHeaderImagePath": (()=>getCustomHeaderImagePath),
    "getCustomerAvatarPath": (()=>getCustomerAvatarPath),
    "getCustomerPostImagePath": (()=>getCustomerPostImagePath),
    "getGalleryImagePath": (()=>getGalleryImagePath),
    "getPostFolderPath": (()=>getPostFolderPath),
    "getPostImagePath": (()=>getPostImagePath),
    "getProductBaseImagePath": (()=>getProductBaseImagePath),
    "getProductImagePath": (()=>getProductImagePath),
    "getProductVariantImagePath": (()=>getProductVariantImagePath),
    "getProfileImagePath": (()=>getProfileImagePath),
    "getScalableUserPath": (()=>getScalableUserPath),
    "getThemeSpecificHeaderImagePath": (()=>getThemeSpecificHeaderImagePath)
});
function getScalableUserPath(userId) {
    if (!userId || typeof userId !== 'string') {
        throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);
    }
    if (userId.length < 4) {
        throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);
    }
    const prefix = userId.substring(0, 2).toLowerCase();
    const midfix = userId.substring(2, 4).toLowerCase();
    return `users/${prefix}/${midfix}/${userId}`;
}
function getProfileImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/profile/logo_${timestamp}.webp`;
}
function getProductImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;
}
function getProductBaseImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;
}
function getProductVariantImagePath(userId, productId, variantId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;
}
function getGalleryImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/gallery/gallery_${timestamp}.webp`;
}
function getPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getPostFolderPath(userId, postId, createdAt) {
    const userPath = getScalableUserPath(userId);
    const postDate = new Date(createdAt);
    const year = postDate.getFullYear();
    const month = String(postDate.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}`;
}
function getCustomerAvatarPath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/avatar/avatar_${timestamp}.webp`;
}
function getCustomerPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getCustomAdImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/ads/custom_ad_${timestamp}.webp`;
}
function getCustomHeaderImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${timestamp}.webp`;
}
function getThemeSpecificHeaderImagePath(userId, timestamp, theme) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${theme}_${timestamp}.webp`;
}
class PathValidator {
    /**
   * Validate if a path follows the new scalable structure
   */ static isScalablePath(path) {
        return path.startsWith('users/') && path.split('/').length >= 4;
    }
    /**
   * Extract user ID from scalable path
   */ static extractUserIdFromPath(path) {
        if (!this.isScalablePath(path)) {
            return null;
        }
        const parts = path.split('/');
        return parts[3]; // users/{prefix}/{midfix}/{userId}/...
    }
    /**
   * Validate path structure integrity
   */ static validatePathStructure(userId, path) {
        const expectedUserPath = getScalableUserPath(userId);
        return path.startsWith(expectedUserPath);
    }
}
class StorageAnalytics {
    /**
   * Get storage distribution info for monitoring
   */ static getDistributionInfo(userId) {
        const prefix = userId.substring(0, 2).toLowerCase();
        const midfix = userId.substring(2, 4).toLowerCase();
        // Estimate number of users in same bucket (assuming even distribution)
        const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets
        const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users
        return {
            prefix,
            midfix,
            bucket: `${prefix}/${midfix}`,
            estimatedPeers
        };
    }
}
}}),
"[project]/lib/schemas/authSchemas.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "IndianMobileSchema": (()=>IndianMobileSchema),
    "NewPasswordConfirmationSchema": (()=>NewPasswordConfirmationSchema),
    "PasswordComplexitySchema": (()=>PasswordComplexitySchema),
    "PasswordConfirmationSchema": (()=>PasswordConfirmationSchema),
    "SingleNewPasswordSchema": (()=>SingleNewPasswordSchema),
    "SinglePasswordSchema": (()=>SinglePasswordSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
const PasswordComplexitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(6, {
    message: "Password must be at least 6 characters"
}).regex(/[A-Z]/, {
    message: "Password must contain at least one capital letter"
}).regex(/[a-z]/, {
    message: "Password must contain at least one lowercase letter."
}) // Added lowercase check for consistency
.regex(/[0-9]/, {
    message: "Password must contain at least one number"
}).regex(/[^A-Za-z0-9]/, {
    message: "Password must contain at least one symbol"
});
const IndianMobileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(10, {
    message: "Mobile number must be at least 10 digits"
}).max(10, {
    message: "Mobile number must be exactly 10 digits"
}).regex(/^\d{10}$/, {
    message: "Please enter a valid 10-digit mobile number"
});
const PasswordConfirmationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    password: PasswordComplexitySchema,
    confirmPassword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}).refine((data)=>data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: [
        "confirmPassword"
    ]
});
const NewPasswordConfirmationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    newPassword: PasswordComplexitySchema,
    confirmPassword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}).refine((data)=>data.newPassword === data.confirmPassword, {
    message: "Passwords do not match.",
    path: [
        "confirmPassword"
    ]
});
const SinglePasswordSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    password: PasswordComplexitySchema
});
const SingleNewPasswordSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    password: PasswordComplexitySchema
});
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00ab37fdd32a3f1c0c6e7c93bb4aa0ff21e15e406b":"checkDeleteAccountVerificationOptions","00dfe803aff9d8d25e084d9a6308a55508f64fa926":"deleteCustomerAccount","00fdacdd07757dbcf036da6c4c8196a99011b00235":"sendDeleteAccountOTP","40ee9c84d5b8fec27991395586b2b303c9b15a7da5":"verifyDeleteAccountPassword","6020ad6d97dac089977bf4950f6387c7820894fb1d":"verifyDeleteAccountOTP","607c8c60009ebdefd543caeda75c68f57b39761f22":"updateCustomerEmail","60a0f0d7673472711bcf5d745034f216e9743862e6":"verifyEmailOTP","60bcf908d98036dc36ecb2e02892a415d912237e27":"linkCustomerEmail","60e9b11bd7b2db97e3c5aead25b07ef2124231c287":"updateCustomerPassword"},"",""] */ __turbopack_context__.s({
    "checkDeleteAccountVerificationOptions": (()=>checkDeleteAccountVerificationOptions),
    "deleteCustomerAccount": (()=>deleteCustomerAccount),
    "linkCustomerEmail": (()=>linkCustomerEmail),
    "sendDeleteAccountOTP": (()=>sendDeleteAccountOTP),
    "updateCustomerEmail": (()=>updateCustomerEmail),
    "updateCustomerPassword": (()=>updateCustomerPassword),
    "verifyDeleteAccountOTP": (()=>verifyDeleteAccountOTP),
    "verifyDeleteAccountPassword": (()=>verifyDeleteAccountPassword),
    "verifyEmailOTP": (()=>verifyEmailOTP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/storage-paths.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/schemas/authSchemas.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
// --- Update Email ---
const UpdateEmailSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    newEmail: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().email('Invalid email address.')
});
async function updateCustomerEmail(_prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = UpdateEmailSchema.safeParse({
        newEmail: formData.get('newEmail')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { newEmail } = validatedFields.data;
    // Check if the new email is the same as the current one
    if (newEmail === user.email) {
        return {
            message: 'New email is the same as the current email.',
            success: false
        };
    }
    try {
        // Update email in Supabase Auth
        // This typically sends a confirmation email to both old and new addresses
        const { error: updateError } = await supabase.auth.updateUser({
            email: newEmail
        });
        if (updateError) {
            // Handle specific Supabase auth error codes
            let errorMessage = 'Failed to update email address.';
            switch(updateError.code){
                case 'email_exists':
                    errorMessage = 'This email address is already registered with another account.';
                    break;
                case 'invalid_email':
                    errorMessage = 'Please enter a valid email address.';
                    break;
                case 'email_change_confirm_limit':
                    errorMessage = 'Too many email change requests. Please wait before trying again.';
                    break;
                case 'over_email_send_rate_limit':
                    errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';
                    break;
                case 'email_not_confirmed':
                    errorMessage = 'Please confirm your current email address before changing it.';
                    break;
                case 'same_email':
                    errorMessage = 'The new email address is the same as your current email.';
                    break;
                default:
                    errorMessage = 'Unable to update email address. Please try again later.';
            }
            return {
                message: errorMessage,
                success: false
            };
        }
        // Revalidate relevant paths if needed, though email change might require user action (confirmation)
        // revalidatePath('/dashboard/customer/settings');
        // revalidatePath('/dashboard/customer/profile'); // Email is shown here
        return {
            message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',
            success: true
        };
    } catch (_error) {
        return {
            message: 'An unexpected error occurred while updating email.',
            success: false
        };
    }
}
// --- Link Email ---
const LinkEmailSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().email('Invalid email address.')
});
async function linkCustomerEmail(_prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = LinkEmailSchema.safeParse({
        email: formData.get('email')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { email } = validatedFields.data;
    try {
        // Check if user already has an email (email update) or not (email linking)
        const isEmailUpdate = !!user.email;
        if (isEmailUpdate) {
            // User already has email - use email change flow
            const { error: authUpdateError } = await supabase.auth.updateUser({
                email: email
            });
            if (authUpdateError) {
                // Handle specific Supabase auth error codes
                let errorMessage = 'Failed to update email address.';
                switch(authUpdateError.code){
                    case 'email_exists':
                        errorMessage = 'This email address is already registered with another account.';
                        break;
                    case 'invalid_email':
                        errorMessage = 'Please enter a valid email address.';
                        break;
                    case 'email_change_confirm_limit':
                        errorMessage = 'Too many email change requests. Please wait before trying again.';
                        break;
                    case 'over_email_send_rate_limit':
                        errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';
                        break;
                    case 'email_not_confirmed':
                        errorMessage = 'Please confirm your current email address before changing it.';
                        break;
                    case 'same_email':
                        errorMessage = 'The new email address is the same as your current email.';
                        break;
                    default:
                        errorMessage = 'Unable to update email address. Please try again later.';
                }
                return {
                    message: errorMessage,
                    success: false
                };
            }
            // Note: customer_profiles table will be automatically updated via database trigger
            return {
                message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',
                success: true
            };
        } else {
            // User doesn't have email - directly link the email without OTP verification
            // Supabase will automatically handle duplicate validation
            const { error: updateError } = await supabase.auth.updateUser({
                email: email
            });
            if (updateError) {
                let errorMessage = 'Failed to link email address.';
                switch(updateError.code){
                    case 'email_exists':
                        errorMessage = 'This email address is already registered with another account.';
                        break;
                    case 'invalid_email':
                        errorMessage = 'Please enter a valid email address.';
                        break;
                    case 'email_change_confirm_limit':
                        errorMessage = 'Too many email requests. Please wait before trying again.';
                        break;
                    case 'over_email_send_rate_limit':
                        errorMessage = 'Email rate limit exceeded. Please wait before trying again.';
                        break;
                    default:
                        errorMessage = 'Unable to link email address. Please try again later.';
                }
                return {
                    message: errorMessage,
                    success: false
                };
            }
            // Note: customer_profiles table will be automatically updated via database trigger
            return {
                message: 'Email address linked successfully!',
                success: true
            };
        }
    } catch (_error) {
        return {
            message: 'An unexpected error occurred while linking email.',
            success: false
        };
    }
}
// --- Verify Email OTP ---
const VerifyEmailOTPSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().email('Invalid email address.'),
    otp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.')
});
async function verifyEmailOTP(_prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = VerifyEmailOTPSchema.safeParse({
        email: formData.get('email'),
        otp: formData.get('otp')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { email, otp } = validatedFields.data;
    try {
        // Verify the OTP
        const { error: verifyError } = await supabase.auth.verifyOtp({
            email: email,
            token: otp,
            type: 'email'
        });
        if (verifyError) {
            let errorMessage = 'Failed to verify code.';
            switch(verifyError.code){
                case 'invalid_otp':
                case 'expired_otp':
                    errorMessage = 'Invalid or expired verification code. Please try again.';
                    break;
                case 'too_many_requests':
                    errorMessage = 'Too many verification attempts. Please wait before trying again.';
                    break;
                default:
                    errorMessage = 'Unable to verify code. Please try again.';
            }
            return {
                message: errorMessage,
                success: false
            };
        }
        // If OTP verification successful, update the user's email
        const { error: updateError } = await supabase.auth.updateUser({
            email: email
        });
        if (updateError) {
            return {
                message: 'Verification successful but failed to link email. Please contact support.',
                success: false
            };
        }
        // Note: customer_profiles table will be automatically updated via database trigger
        return {
            message: 'Email address linked successfully!',
            success: true
        };
    } catch (_error) {
        return {
            message: 'An unexpected error occurred while verifying code.',
            success: false
        };
    }
}
// --- Update Password ---
const UpdatePasswordSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    currentPassword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(1, 'Current password is required.'),
    newPassword: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PasswordComplexitySchema"]
});
async function updateCustomerPassword(_prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user || !user.email) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = UpdatePasswordSchema.safeParse({
        currentPassword: formData.get('currentPassword'),
        newPassword: formData.get('newPassword')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { currentPassword, newPassword } = validatedFields.data;
    // IMPORTANT: Verify the current password first before attempting update.
    // Supabase doesn't directly expose a "verify password" endpoint.
    // The recommended way is to try signing in with the current password.
    // This is a crucial security step.
    const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: currentPassword
    });
    if (signInError) {
        // Handle specific Supabase auth error codes for sign-in
        let errorMessage = 'Failed to verify current password.';
        const fieldErrors = {};
        switch(signInError.code){
            case 'invalid_credentials':
            case 'email_not_confirmed':
                errorMessage = 'Incorrect current password.';
                fieldErrors.currentPassword = [
                    'Incorrect current password.'
                ];
                break;
            case 'too_many_requests':
                errorMessage = 'Too many failed attempts. Please wait before trying again.';
                break;
            case 'user_not_found':
                errorMessage = 'Account not found. Please contact support.';
                break;
            default:
                errorMessage = 'Unable to verify current password. Please try again.';
        }
        return {
            message: errorMessage,
            errors: fieldErrors,
            success: false
        };
    }
    // If sign-in was successful (current password is correct), proceed to update
    try {
        const { error: updateError } = await supabase.auth.updateUser({
            password: newPassword
        });
        if (updateError) {
            // Handle specific Supabase auth error codes for password update
            let errorMessage = 'Failed to update password.';
            switch(updateError.code){
                case 'weak_password':
                    errorMessage = 'Password is too weak. Please choose a stronger password.';
                    break;
                case 'same_password':
                    errorMessage = 'New password must be different from your current password.';
                    break;
                case 'password_too_short':
                    errorMessage = 'Password must be at least 6 characters long.';
                    break;
                case 'too_many_requests':
                    errorMessage = 'Too many password change requests. Please wait before trying again.';
                    break;
                default:
                    errorMessage = 'Unable to update password. Please try again later.';
            }
            return {
                message: errorMessage,
                success: false
            };
        }
        // Password updated successfully
        return {
            message: 'Password updated successfully!',
            success: true
        };
    } catch (_error) {
        return {
            message: 'An unexpected error occurred while updating password.',
            success: false
        };
    }
}
async function checkDeleteAccountVerificationOptions() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            success: false,
            hasEmail: false,
            hasPhone: false,
            message: "Authentication required."
        };
    }
    const hasEmail = !!(user.email && user.email.trim() !== '');
    const hasPhone = !!(user.phone && user.phone.trim() !== '');
    return {
        success: true,
        hasEmail,
        hasPhone
    };
}
async function sendDeleteAccountOTP() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user || !user.email) {
        return {
            success: false,
            message: "Authentication required or no email found."
        };
    }
    try {
        const { error } = await supabase.auth.signInWithOtp({
            email: user.email,
            options: {
                shouldCreateUser: false
            }
        });
        if (error) {
            // Handle rate limit errors specifically
            if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {
                return {
                    success: false,
                    message: "Email rate limit exceeded. Please try again later.",
                    isConfigurationError: true
                };
            }
            return {
                success: false,
                message: error.message || "Failed to send verification code."
            };
        }
        return {
            success: true,
            message: "Verification code sent to your email address.",
            email: user.email
        };
    } catch (_error) {
        return {
            success: false,
            message: "An unexpected error occurred while sending verification code."
        };
    }
}
async function verifyDeleteAccountOTP(email, otp) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { error } = await supabase.auth.verifyOtp({
            email: email,
            token: otp,
            type: 'email'
        });
        if (error) {
            let errorMessage = 'Failed to verify code.';
            switch(error.code){
                case 'invalid_otp':
                case 'expired_otp':
                    errorMessage = 'Invalid or expired verification code. Please try again.';
                    break;
                case 'too_many_requests':
                    errorMessage = 'Too many verification attempts. Please wait before trying again.';
                    break;
                default:
                    errorMessage = 'Unable to verify code. Please try again.';
            }
            return {
                success: false,
                message: errorMessage
            };
        }
        return {
            success: true,
            message: "Verification successful."
        };
    } catch (_error) {
        return {
            success: false,
            message: "An unexpected error occurred during verification."
        };
    }
}
async function verifyDeleteAccountPassword(password) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user || !user.phone) {
        return {
            success: false,
            message: "Authentication required or no phone found."
        };
    }
    try {
        // Verify current password by attempting to sign in
        const { error } = await supabase.auth.signInWithPassword({
            phone: user.phone,
            password: password
        });
        if (error) {
            return {
                success: false,
                message: "Invalid password. Please try again."
            };
        }
        return {
            success: true,
            message: "Password verified successfully."
        };
    } catch (_error) {
        return {
            success: false,
            message: "An unexpected error occurred during password verification."
        };
    }
}
async function deleteCustomerAccount() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    // Check for and clean up any storage data using hash-based structure
    try {
        // Use admin client for storage operations to bypass RLS
        const adminSupabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
        const bucketName = "customers"; // Correct bucket name (plural)
        const userStoragePath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getScalableUserPath"])(user.id);
        // Recursive function to delete all files and folders
        const deleteRecursively = async (path)=>{
            const { data: items, error: listError } = await adminSupabase.storage.from(bucketName).list(path);
            if (listError) {
                console.error(`Error listing files in ${path}:`, listError);
                return;
            }
            if (!items || items.length === 0) {
                return;
            }
            // Separate files and folders
            const files = [];
            const folders = [];
            for (const item of items){
                const fullPath = path ? `${path}/${item.name}` : item.name;
                if (item.metadata === null) {
                    // This is a folder
                    folders.push(fullPath);
                } else {
                    // This is a file
                    files.push(fullPath);
                }
            }
            // Delete all files in the current directory
            if (files.length > 0) {
                const { error: deleteError } = await adminSupabase.storage.from(bucketName).remove(files);
                if (deleteError && deleteError.message !== "The resource was not found") {
                    console.error(`Error deleting files in ${path}:`, deleteError);
                } else {
                    console.log(`Successfully deleted ${files.length} files in ${path}`);
                }
            }
            // Recursively delete folders
            for (const folder of folders){
                await deleteRecursively(folder);
            }
        };
        // Start the recursive deletion from the user's root folder
        await deleteRecursively(userStoragePath);
        console.log('Successfully cleaned up customer storage data');
    } catch (storageError) {
        // Log but continue with deletion
        console.error('Error checking/cleaning customer storage:', storageError);
    }
    // Use the admin client to delete the user and profile
    try {
        const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
        // Delete from customer_profiles table (CASCADE will handle related data)
        console.log('Deleting customer profile...');
        const { error: deleteProfileError } = await supabaseAdmin.from('customer_profiles').delete().eq('id', user.id);
        if (deleteProfileError) {
            console.error('Error deleting customer profile:', deleteProfileError);
            return {
                message: `Failed to delete customer profile: ${deleteProfileError.message}`,
                success: false
            };
        }
        console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');
        // Sign out the user locally first (while the session is still valid)
        await supabase.auth.signOut();
        // Then delete the user using the admin client
        // Using hard delete (shouldSoftDelete=false) to completely remove the user
        const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);
        if (deleteUserError) {
            console.error('Error deleting user account:', deleteUserError);
            return {
                message: `Failed to delete account: ${deleteUserError.message}`,
                success: false
            };
        }
        // Revalidate paths if needed
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/", "layout"); // Revalidate root layout
        return {
            message: 'Account deleted successfully.',
            success: true
        };
    } catch (error) {
        console.error('Unexpected error during account deletion:', error);
        return {
            message: 'An unexpected error occurred during account deletion.',
            success: false
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    updateCustomerEmail,
    linkCustomerEmail,
    verifyEmailOTP,
    updateCustomerPassword,
    checkDeleteAccountVerificationOptions,
    sendDeleteAccountOTP,
    verifyDeleteAccountOTP,
    verifyDeleteAccountPassword,
    deleteCustomerAccount
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomerEmail, "607c8c60009ebdefd543caeda75c68f57b39761f22", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(linkCustomerEmail, "60bcf908d98036dc36ecb2e02892a415d912237e27", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(verifyEmailOTP, "60a0f0d7673472711bcf5d745034f216e9743862e6", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomerPassword, "60e9b11bd7b2db97e3c5aead25b07ef2124231c287", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(checkDeleteAccountVerificationOptions, "00ab37fdd32a3f1c0c6e7c93bb4aa0ff21e15e406b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(sendDeleteAccountOTP, "00fdacdd07757dbcf036da6c4c8196a99011b00235", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(verifyDeleteAccountOTP, "6020ad6d97dac089977bf4950f6387c7820894fb1d", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(verifyDeleteAccountPassword, "40ee9c84d5b8fec27991395586b2b303c9b15a7da5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteCustomerAccount, "00dfe803aff9d8d25e084d9a6308a55508f64fa926", null);
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00a78b43259bdfa35946a0918da66b9382dcd7b4dc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["signOutUser"]),
    "00ab37fdd32a3f1c0c6e7c93bb4aa0ff21e15e406b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["checkDeleteAccountVerificationOptions"]),
    "00dfe803aff9d8d25e084d9a6308a55508f64fa926": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deleteCustomerAccount"]),
    "00fdacdd07757dbcf036da6c4c8196a99011b00235": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendDeleteAccountOTP"]),
    "40ee9c84d5b8fec27991395586b2b303c9b15a7da5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifyDeleteAccountPassword"]),
    "6020ad6d97dac089977bf4950f6387c7820894fb1d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifyDeleteAccountOTP"]),
    "60a0f0d7673472711bcf5d745034f216e9743862e6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifyEmailOTP"]),
    "60bcf908d98036dc36ecb2e02892a415d912237e27": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["linkCustomerEmail"]),
    "60e9b11bd7b2db97e3c5aead25b07ef2124231c287": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateCustomerPassword"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00a78b43259bdfa35946a0918da66b9382dcd7b4dc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00a78b43259bdfa35946a0918da66b9382dcd7b4dc"]),
    "00ab37fdd32a3f1c0c6e7c93bb4aa0ff21e15e406b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00ab37fdd32a3f1c0c6e7c93bb4aa0ff21e15e406b"]),
    "00dfe803aff9d8d25e084d9a6308a55508f64fa926": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00dfe803aff9d8d25e084d9a6308a55508f64fa926"]),
    "00fdacdd07757dbcf036da6c4c8196a99011b00235": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00fdacdd07757dbcf036da6c4c8196a99011b00235"]),
    "40ee9c84d5b8fec27991395586b2b303c9b15a7da5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40ee9c84d5b8fec27991395586b2b303c9b15a7da5"]),
    "6020ad6d97dac089977bf4950f6387c7820894fb1d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["6020ad6d97dac089977bf4950f6387c7820894fb1d"]),
    "60a0f0d7673472711bcf5d745034f216e9743862e6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60a0f0d7673472711bcf5d745034f216e9743862e6"]),
    "60bcf908d98036dc36ecb2e02892a415d912237e27": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60bcf908d98036dc36ecb2e02892a415d912237e27"]),
    "60e9b11bd7b2db97e3c5aead25b07ef2124231c287": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60e9b11bd7b2db97e3c5aead25b07ef2124231c287"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx <module evaluation>", "default");
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx", "default");
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$SettingsPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$SettingsPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$SettingsPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/lib/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cleanPhoneFromAuth": (()=>cleanPhoneFromAuth),
    "cn": (()=>cn),
    "formatAddress": (()=>formatAddress),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "formatIndianNumberShort": (()=>formatIndianNumberShort),
    "maskEmail": (()=>maskEmail),
    "maskPhoneNumber": (()=>maskPhoneNumber),
    "toTitleCase": (()=>toTitleCase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-rsc] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function cleanPhoneFromAuth(phone) {
    if (!phone) return null;
    let processedPhone = phone.trim();
    // Remove +91 prefix if present
    if (processedPhone.startsWith('+91')) {
        processedPhone = processedPhone.substring(3);
    } else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {
        processedPhone = processedPhone.substring(2);
    }
    // Validate it's a 10-digit number
    if (/^\d{10}$/.test(processedPhone)) {
        return processedPhone;
    }
    return null; // Invalid format
}
function maskPhoneNumber(phone) {
    if (!phone || phone.length < 4) {
        return "Invalid Phone"; // Or return empty string or original if preferred
    }
    const firstTwo = phone.substring(0, 2);
    const lastTwo = phone.substring(phone.length - 2);
    const maskedPart = "*".repeat(phone.length - 4);
    return `${firstTwo}${maskedPart}${lastTwo}`;
}
function maskEmail(email) {
    if (!email || !email.includes("@")) {
        return "Invalid Email"; // Or return empty string or original
    }
    const parts = email.split("@");
    const username = parts[0];
    const domain = parts[1];
    if (username.length <= 2 || domain.length <= 2 || !domain.includes(".")) {
        return "Email Hidden"; // Simple mask for very short/invalid emails
    }
    const maskedUsername = username.substring(0, 2) + "*".repeat(username.length - 2);
    const domainParts = domain.split(".");
    const domainName = domainParts[0];
    const domainTld = domainParts.slice(1).join("."); // Handle multiple parts like .co.uk
    const maskedDomainName = domainName.substring(0, 2) + "*".repeat(domainName.length - 2);
    return `${maskedUsername}@${maskedDomainName}.${domainTld}`;
}
function formatIndianNumberShort(num) {
    if (num === null || num === undefined || isNaN(num)) return "0";
    const absNum = Math.abs(num);
    // Indian units and their values
    const units = [
        {
            value: 1e5,
            symbol: "L"
        },
        {
            value: 1e7,
            symbol: "Cr"
        },
        {
            value: 1e9,
            symbol: "Ar"
        },
        {
            value: 1e11,
            symbol: "Khar"
        },
        {
            value: 1e13,
            symbol: "Neel"
        },
        {
            value: 1e15,
            symbol: "Padma"
        },
        {
            value: 1e17,
            symbol: "Shankh"
        }
    ];
    // For thousands (K), use western style for sub-lakh
    if (absNum < 1e5) {
        if (absNum >= 1e3) {
            return (num / 1e3).toFixed(1).replace(/\.0$/, "") + "K";
        }
        return num.toString();
    }
    // Find the largest unit that fits
    for(let i = units.length - 1; i >= 0; i--){
        if (absNum >= units[i].value) {
            return (num / units[i].value).toFixed(1).replace(/\.0$/, "") + units[i].symbol;
        }
    }
    // Fallback (should not reach here)
    return num.toString();
}
function formatAddress(data) {
    const addressParts = [
        data.address_line,
        data.locality,
        data.city,
        data.state,
        data.pincode
    ].filter(Boolean);
    return addressParts.join(", ") || "Address not available";
}
function formatDate(date, includeTime = false) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
        return "Invalid date";
    }
    const options = {
        year: "numeric",
        month: "long",
        day: "numeric",
        timeZone: "Asia/Kolkata"
    };
    if (includeTime) {
        options.hour = "2-digit";
        options.minute = "2-digit";
        options.hour12 = true;
    }
    return date.toLocaleString("en-IN", options);
}
function formatCurrency(amount, currency = "INR") {
    if (amount === null || amount === undefined || isNaN(amount)) {
        return "Invalid amount";
    }
    try {
        return new Intl.NumberFormat("en-IN", {
            style: "currency",
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(amount);
    } catch  {
        // Catch any error without using the error variable
        // Fallback in case of invalid currency code
        return `${currency} ${amount.toFixed(2)}`;
    }
}
function toTitleCase(text) {
    if (!text) return "";
    return text.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase());
}
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CustomerSettingsPage),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$SettingsPageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const metadata = {
    title: 'Account Settings - Dukancard',
    description: 'Manage your Dukancard customer account settings.',
    robots: 'noindex, nofollow'
};
async function CustomerSettingsPage() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/login?message=Authentication required');
    }
    // Settings page is exempt from address validation
    // Users can access settings even without complete address
    // Clean phone number from auth format
    const cleanedPhone = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cleanPhoneFromAuth"])(user?.phone);
    // Determine user registration type
    const isGoogleUser = user.app_metadata?.provider === "google";
    const hasEmail = !!user.email;
    const hasPhone = !!cleanedPhone;
    // Determine registration method
    let registrationType = 'email'; // default
    if (isGoogleUser) {
        registrationType = 'google';
    } else if (hasPhone && !hasEmail) {
        registrationType = 'phone';
    } else if (hasEmail) {
        registrationType = 'email';
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$SettingsPageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
        currentEmail: user?.email,
        currentPhone: cleanedPhone,
        registrationType: registrationType
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/page.tsx",
        lineNumber: 49,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_e9e6ec5f._.js.map