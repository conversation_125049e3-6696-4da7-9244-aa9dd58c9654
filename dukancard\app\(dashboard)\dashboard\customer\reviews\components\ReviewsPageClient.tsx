"use client";

import { Star } from "lucide-react";
import EnhancedReviewListClient from "./EnhancedReviewListClient";
import { formatIndianNumberShort } from "@/lib/utils";

interface ReviewsPageClientProps {
  reviewsCount: number;
}

export default function ReviewsPageClient({ reviewsCount }: ReviewsPageClientProps) {
  return (
    <div className="space-y-6">
      {/* Header Section with proper layout */}
      <div className="flex items-center gap-4">
        <div className="p-3 rounded-xl bg-muted hidden sm:block">
          <Star className="w-6 h-6 text-amber-600 dark:text-amber-400" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-foreground">
            Your Reviews
          </h1>
          <p className="text-muted-foreground mt-1">
            {formatIndianNumberShort(reviewsCount)} {reviewsCount === 1 ? 'review' : 'reviews'} you&apos;ve written for businesses
          </p>
        </div>
      </div>

      {/* Content Section */}
      <div className="space-y-6">
        {/* Enhanced Review List - now handles its own data fetching */}
        <EnhancedReviewListClient />
      </div>
    </div>
  );
}
