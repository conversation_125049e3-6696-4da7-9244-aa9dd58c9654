import React, { useState, useMemo, useRef } from "react";
import { View, Text, ScrollView, Alert, ActivityIndicator } from "react-native";
import { User, Save } from "lucide-react-native";
import ProfileForm, { ProfileFormRef } from "@/src/components/profile/ProfileForm";
import AvatarUpload from "./AvatarUpload";
import AddressForm, { AddressFormRef } from "@/src/components/profile/AddressForm";
import ProfileRequirementDialog from "./ProfileRequirementDialog";
import { Button } from "@/src/components/ui/Button";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { useTheme } from "@/src/hooks/useTheme";
import { profileStyles } from "@/styles/dashboard/customer/profile";
import { responsiveFontSize } from "@/lib/theme/colors";
import { updateCustomerProfile, updateCustomerAddress } from "@/backend/supabase/services/common/profileService";
import { useAuth } from "@/src/contexts/AuthContext";

interface ProfilePageClientProps {
  initialName: string | null;
  initialAvatarUrl?: string | null;
  initialAddressData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  } | null;
  hasCompleteAddress?: boolean;
}

export default function ProfilePageClient({
  initialName,
  initialAvatarUrl,
  initialAddressData,
  hasCompleteAddress = false,
}: ProfilePageClientProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(initialAvatarUrl || undefined);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const theme = useTheme();
  const styles = profileStyles(theme);

  // Refs to access form data from child components
  const profileFormRef = useRef<ProfileFormRef>(null);
  const addressFormRef = useRef<AddressFormRef>(null);

  // Unified submit function
  const handleUnifiedSubmit = async () => {
    if (!user?.id) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    // Get data from both forms
    const profileData = profileFormRef.current?.getFormData();
    const addressData = addressFormRef.current?.getFormData();

    // Validate required fields
    if (!profileData?.name?.trim()) {
      Alert.alert('Validation Error', 'Name is required');
      return;
    }

    // Address validation - only required fields
    if (addressData && (
      !addressData.pincode?.trim() ||
      !addressData.city?.trim() ||
      !addressData.state?.trim() ||
      !addressData.locality?.trim()
    )) {
      Alert.alert('Validation Error', 'Please complete all required address fields (pincode, city, state, locality)');
      return;
    }

    setIsLoading(true);
    try {
      // Update profile (name)
      const profileResult = await updateCustomerProfile({
        name: profileData.name.trim(),
      });

      if (!profileResult.success) {
        throw new Error(profileResult.error || 'Failed to update profile');
      }

      // Update address if provided
      if (addressData) {
        const addressResult = await updateCustomerAddress({
          address: addressData.address?.trim() || undefined,
          pincode: addressData.pincode.trim(),
          city: addressData.city.trim(),
          state: addressData.state.trim(),
          locality: addressData.locality.trim(),
        });

        if (!addressResult.success) {
          throw new Error(addressResult.error || 'Failed to update address');
        }
      }

      Alert.alert('Success', 'Profile updated successfully!');
    } catch (error) {
      console.error('Profile update error:', error);
      Alert.alert('Error', error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Profile Requirement Dialog */}
      <ProfileRequirementDialog
        hasCompleteAddress={hasCompleteAddress}
      />

      {/* Main Profile Card */}
      <View style={[styles.card, { backgroundColor: theme.colors.card }]}>
        {/* Header */}
        <View style={styles.cardHeader}>
          <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '1A' }]}>
            <User size={responsiveFontSize(20)} color={theme.colors.primary} />
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={[styles.cardTitle, { color: theme.colors.textPrimary }]}>
              Profile Information
            </Text>
            <Text style={[styles.cardSubtitle, { color: theme.colors.textSecondary }]}>
              Update your personal details
            </Text>
          </View>
        </View>

        {/* Avatar Upload */}
        <View style={styles.avatarContainer}>
          <AvatarUpload
            initialAvatarUrl={avatarUrl}
            userName={initialName}
            onUpdateAvatar={(url: string) => setAvatarUrl(url)}
          />
        </View>

        {/* Profile Form */}
        <View style={styles.formContainer}>
          <ProfileForm
            ref={profileFormRef}
            initialName={initialName}
            hideSubmitButton={true}
          />
        </View>
      </View>

      {/* Address Form Section */}
      <View style={[styles.card, { backgroundColor: theme.colors.card }]}>
        <AddressForm
          ref={addressFormRef}
          initialData={initialAddressData || undefined}
          hideSubmitButton={true}
        />
      </View>

      {/* Unified Submit Button */}
      <View style={[styles.card, { backgroundColor: theme.colors.card, marginTop: theme.spacing.md }]}>
        <Button
          title={isLoading ? 'Updating...' : 'Save Profile'}
          onPress={handleUnifiedSubmit}
          disabled={isLoading}
          variant="primary"
          icon={
            isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Save size={20} color="#FFFFFF" />
            )
          }
          style={{
            backgroundColor: '#D4AF37', // Brand color
            borderRadius: 8,
            paddingVertical: 14,
            paddingHorizontal: 20,
            // Remove any shadow/glow effects
            shadowOpacity: 0,
            elevation: 0,
          }}
        />
      </View>
    </ScrollView>
  );
}
