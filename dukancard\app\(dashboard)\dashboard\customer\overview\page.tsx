import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { Metadata } from "next";
import CustomerDashboardClient from "../components/CustomerDashboardClient";
import { requireCompleteProfile } from "@/lib/actions/customerProfiles/addressValidation";

export const metadata: Metadata = {
  title: "Customer Dashboard Overview - Dukancard",
  robots: "noindex, nofollow",
};

export default async function CustomerOverviewPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/login?message=Authentication required");
  }

  // Check if customer has complete address
  await requireCompleteProfile(user.id);

  // Fetch customer profile data
  const { data: profile, error: profileError } = await supabase
    .from("customer_profiles")
    .select("name, email")
    .eq("id", user.id)
    .single();

  // Handle profile fetch error gracefully - show dashboard but maybe with default name
  if (profileError) {
    console.error("Error fetching customer profile:", profileError?.message);
    // Don't redirect, allow dashboard access but profile might be null
  }

  const customerName = profile?.name || "Valued Customer";

  // Fetch review count
  const { count: reviewCount, error: reviewError } = await supabase
    .from('ratings_reviews')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id);

  if (reviewError) {
    console.error("Error fetching review count:", reviewError);
  }

  // Fetch subscription count
  const { count: subscriptionCount, error: subscriptionError } = await supabase
    .from('subscriptions')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id);

  if (subscriptionError) {
    console.error("Error fetching subscription count:", subscriptionError);
  }

  // Fetch likes count
  const { count: likesCount, error: likesError } = await supabase
    .from('likes')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id);

  if (likesError) {
    console.error("Error fetching likes count:", likesError);
  }

  return (
    <CustomerDashboardClient
      customerName={customerName}
      userId={user.id}
      initialReviewCount={reviewCount || 0}
      initialSubscriptionCount={subscriptionCount || 0}
      initialLikesCount={likesCount || 0}
    />
  );
}
