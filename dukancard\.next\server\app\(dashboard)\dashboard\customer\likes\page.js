const CHUNK_PUBLIC_PATH = "server/app/(dashboard)/dashboard/customer/likes/page.js";
const runtime = require("../../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_c336f25b._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/app_1f3630ef._.js");
runtime.loadChunk("server/chunks/ssr/app_0a91cb44._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__9e32a0de._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@supabase_node-fetch_lib_index_5ea9b7b2.js");
runtime.loadChunk("server/chunks/ssr/node_modules_6520af37._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__7f3de6e7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_headers_fdaa6c4e.js");
runtime.loadChunk("server/chunks/ssr/_05554fce._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_7e704d29._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/(dashboard)/dashboard/customer/likes/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/(dashboard)/dashboard/customer/likes/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_1 => \"[project]/app/opengraph-image.png.mjs { IMAGE => \\\"[project]/app/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_2 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/(dashboard)/dashboard/customer/likes/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/(dashboard)/dashboard/customer/likes/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_1 => \"[project]/app/opengraph-image.png.mjs { IMAGE => \\\"[project]/app/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_2 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/(dashboard)/dashboard/customer/likes/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
