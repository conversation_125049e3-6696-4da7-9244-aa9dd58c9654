{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/admin.ts"], "sourcesContent": ["import { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * Creates a Supabase admin client with the service role key.\r\n * This client has admin privileges and should only be used on the server.\r\n * Never expose your service_role key in the browser.\r\n */\r\nexport function createAdminClient() {\r\n  return createSupabaseClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,gFAExB,QAAQ,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\n// Reusable base schema for password complexity\r\nexport const PasswordComplexitySchema = z\r\n  .string()\r\n  .min(6, { message: \"Password must be at least 6 characters\" })\r\n  .regex(/[A-Z]/, { message: \"Password must contain at least one capital letter\" })\r\n  .regex(/[a-z]/, { message: \"Password must contain at least one lowercase letter.\" }) // Added lowercase check for consistency\r\n  .regex(/[0-9]/, { message: \"Password must contain at least one number\" })\r\n  .regex(/[^A-Za-z0-9]/, { message: \"Password must contain at least one symbol\" });\r\n\r\n// Reusable schema for mobile number validation\r\nexport const IndianMobileSchema = z\r\n  .string()\r\n  .min(10, { message: \"Mobile number must be at least 10 digits\" })\r\n  .max(10, { message: \"Mobile number must be exactly 10 digits\" })\r\n  .regex(/^\\d{10}$/, {\r\n    message: \"Please enter a valid 10-digit mobile number\"\r\n  });\r\n\r\n// Schema for forms requiring password confirmation (e.g., registration, change password)\r\nexport const PasswordConfirmationSchema = z\r\n  .object({\r\n    password: PasswordComplexitySchema,\r\n    confirmPassword: z.string(),\r\n  })\r\n  .refine((data) => data.password === data.confirmPassword, {\r\n    message: \"Passwords don't match\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\n// Schema for forms requiring new password confirmation (used in settings/reset)\r\n// Renaming fields for clarity in those contexts\r\nexport const NewPasswordConfirmationSchema = z\r\n  .object({\r\n    newPassword: PasswordComplexitySchema,\r\n    confirmPassword: z.string(),\r\n  })\r\n  .refine((data) => data.newPassword === data.confirmPassword, {\r\n    message: \"Passwords do not match.\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\n// Schema for just the password field (e.g., for login or single field validation)\r\nexport const SinglePasswordSchema = z.object({\r\n    password: PasswordComplexitySchema,\r\n});\r\n\r\n// Schema for just the new password field (used in reset password action)\r\nexport const SingleNewPasswordSchema = z.object({\r\n    password: PasswordComplexitySchema, // Action receives it as 'password'\r\n});\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG;IAAE,SAAS;AAAyC,GAC3D,KAAK,CAAC,SAAS;IAAE,SAAS;AAAoD,GAC9E,KAAK,CAAC,SAAS;IAAE,SAAS;AAAuD,GAAG,wCAAwC;CAC5H,KAAK,CAAC,SAAS;IAAE,SAAS;AAA4C,GACtE,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAGzE,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAChC,MAAM,GACN,GAAG,CAAC,IAAI;IAAE,SAAS;AAA2C,GAC9D,GAAG,CAAC,IAAI;IAAE,SAAS;AAA0C,GAC7D,KAAK,CAAC,YAAY;IACjB,SAAS;AACX;AAGK,MAAM,6BAA6B,oIAAA,CAAA,IAAC,CACxC,MAAM,CAAC;IACN,UAAU;IACV,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAIK,MAAM,gCAAgC,oIAAA,CAAA,IAAC,CAC3C,MAAM,CAAC;IACN,aAAa;IACb,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC3D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGK,MAAM,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU;AACd;AAGO,MAAM,0BAA0B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,UAAU;AACd", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { z } from 'zod';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { IndianMobileSchema } from '@/lib/schemas/authSchemas';\r\n\r\n// Define the schema for profile updates\r\nconst ProfileSchema = z.object({\r\n  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),\r\n  // Add other fields here if needed in the future\r\n});\r\n\r\n// Define the schema for phone updates\r\nconst PhoneSchema = z.object({\r\n  phone: IndianMobileSchema,\r\n});\r\n\r\n// Define the schema for address updates with proper validation\r\nconst AddressSchema = z.object({\r\n  address: z\r\n    .string()\r\n    .max(100, { message: \"Address cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  pincode: z\r\n    .string()\r\n    .min(1, { message: \"Pincode is required\" })\r\n    .regex(/^\\d{6}$/, { message: \"Must be a valid 6-digit pincode\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"City cannot be empty\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"State cannot be empty\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"Locality cannot be empty\" }),\r\n});\r\n\r\n// Define the schema for email updates\r\nconst EmailSchema = z.object({\r\n  email: z.string().email({ message: \"Please enter a valid email address\" }),\r\n});\r\n\r\n// Define the schema for mobile updates\r\nconst MobileSchema = z.object({\r\n  mobile: IndianMobileSchema,\r\n});\r\n\r\n// Define the unified schema for profile and address updates\r\nconst UnifiedProfileSchema = z.object({\r\n  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),\r\n  address: z\r\n    .string()\r\n    .max(100, { message: \"Address cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  pincode: z\r\n    .string()\r\n    .min(1, { message: \"Pincode is required\" })\r\n    .regex(/^\\d{6}$/, { message: \"Must be a valid 6-digit pincode\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  city: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  state: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  locality: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n});\r\n\r\nexport type ProfileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    name?: string[];\r\n    // Add other fields here\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type PhoneFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    phone?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type AddressFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    address?: string[];\r\n    pincode?: string[];\r\n    city?: string[];\r\n    state?: string[];\r\n    locality?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type EmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type MobileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    mobile?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type UnifiedProfileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    name?: string[];\r\n    address?: string[];\r\n    pincode?: string[];\r\n    city?: string[];\r\n    state?: string[];\r\n    locality?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerProfile(\r\n  prevState: ProfileFormState,\r\n  formData: FormData\r\n): Promise<ProfileFormState> {\r\n  const supabase = await createClient(); // Added await\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = ProfileSchema.safeParse({\r\n    name: formData.get('name'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { name } = validatedFields.data;\r\n\r\n  try {\r\n    // Update name in auth.users table (full_name in user_metadata)\r\n    // The database trigger will automatically sync this to customer_profiles table\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      data: { full_name: name }\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth user metadata:', authUpdateError);\r\n      return { message: `Auth Error: ${authUpdateError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate the profile page and potentially the layout to reflect name change\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer/layout'); // To update sidebar/header name\r\n\r\n    return { message: 'Profile updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating profile:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerAddress(\r\n  prevState: AddressFormState,\r\n  formData: FormData\r\n): Promise<AddressFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = AddressSchema.safeParse({\r\n    address: formData.get('address'),\r\n    pincode: formData.get('pincode'),\r\n    city: formData.get('city'),\r\n    state: formData.get('state'),\r\n    locality: formData.get('locality'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { address, pincode, city, state, locality } = validatedFields.data;\r\n\r\n  try {\r\n    const { error: updateError } = await supabase\r\n      .from('customer_profiles')\r\n      .update({\r\n        address: address || null,\r\n        pincode,\r\n        city,\r\n        state,\r\n        locality\r\n      })\r\n      .eq('id', user.id);\r\n\r\n    if (updateError) {\r\n      console.error('Error updating customer address:', updateError);\r\n      return { message: `Database Error: ${updateError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return { message: 'Address updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating address:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerPhone(\r\n  prevState: PhoneFormState,\r\n  formData: FormData\r\n): Promise<PhoneFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = PhoneSchema.safeParse({\r\n    phone: formData.get('phone'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { phone } = validatedFields.data;\r\n\r\n  try {\r\n    // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n    // Update phone in customer_profiles table\r\n    const { error: updateError } = await supabase\r\n      .from('customer_profiles')\r\n      .update({ phone: phone })\r\n      .eq('id', user.id);\r\n\r\n    if (updateError) {\r\n      console.error('Error updating customer phone:', updateError);\r\n      return { message: `Database Error: ${updateError.message}`, success: false };\r\n    }\r\n\r\n    // Update phone in Supabase auth.users table to maintain user ID consistency\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${phone}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.warn('Failed to update auth phone field:', authUpdateError.message);\r\n      // Don't fail the operation for this, just log the warning\r\n      // The customer_profiles table is updated successfully\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return { message: 'Phone number updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating phone:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerEmail(\r\n  prevState: EmailFormState,\r\n  formData: FormData\r\n): Promise<EmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = EmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user registered with Google OAuth (matching settings page logic)\r\n    const isGoogleLogin = user.app_metadata?.provider === 'google';\r\n\r\n    // Check if the user has email/password authentication\r\n    let hasEmailAuth = false;\r\n\r\n    if (isGoogleLogin && user.email) {\r\n      try {\r\n        // Use admin client to check user identities\r\n        const adminClient = createAdminClient();\r\n        const { data: authData } = await adminClient.auth.admin.getUserById(user.id);\r\n\r\n        // Check if the user has email/password authentication\r\n        if (authData?.user?.identities) {\r\n          hasEmailAuth = authData.user.identities.some(\r\n            (identity) => identity.provider === \"email\"\r\n          );\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking user auth methods:\", error);\r\n        hasEmailAuth = false;\r\n      }\r\n    }\r\n\r\n    // Only disable email changes if they're using Google and don't have email auth\r\n    const shouldDisableEmailChange = isGoogleLogin && !hasEmailAuth;\r\n\r\n    if (shouldDisableEmailChange) {\r\n      return {\r\n        message: 'Email cannot be changed for Google accounts. Your email is linked to your Google account.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Check if email is the same as current\r\n    if (user.email === email) {\r\n      return {\r\n        message: 'Email address is the same as current.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Update email in Supabase auth.users table\r\n    // This is the primary source of truth for email\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth email:', authUpdateError);\r\n\r\n      // Provide user-friendly error messages\r\n      let errorMessage = 'Failed to update email address.';\r\n      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {\r\n        errorMessage = 'This email address is already in use by another account.';\r\n      } else if (authUpdateError.message.includes('check constraint')) {\r\n        errorMessage = 'Invalid email format provided.';\r\n      } else if (authUpdateError.message.includes('rate limit')) {\r\n        errorMessage = 'Too many requests. Please try again later.';\r\n      }\r\n\r\n      return {\r\n        message: errorMessage,\r\n        success: false\r\n      };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: 'Email address updated successfully! You may need to verify the new email address.',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating email:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerMobile(\r\n  prevState: MobileFormState,\r\n  formData: FormData\r\n): Promise<MobileFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = MobileSchema.safeParse({\r\n    mobile: formData.get('mobile'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { mobile } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if mobile is the same as current\r\n    const currentMobile = user.phone ? user.phone.replace(/^\\+91/, '') : '';\r\n    if (mobile === currentMobile) {\r\n      return {\r\n        message: 'Mobile number is the same as current.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Note: Mobile uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n    // Update mobile in Supabase auth.users table\r\n    // This is the primary source of truth for mobile\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${mobile}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth mobile:', authUpdateError);\r\n\r\n      // Provide user-friendly error messages\r\n      let errorMessage = 'Failed to update mobile number.';\r\n      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {\r\n        errorMessage = 'This mobile number is already in use by another account.';\r\n      } else if (authUpdateError.message.includes('check constraint')) {\r\n        errorMessage = 'Invalid mobile number format provided.';\r\n      } else if (authUpdateError.message.includes('rate limit')) {\r\n        errorMessage = 'Too many requests. Please try again later.';\r\n      }\r\n\r\n      return {\r\n        message: errorMessage,\r\n        success: false\r\n      };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: 'Mobile number updated successfully!',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating mobile:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerProfileAndAddress(\r\n  prevState: UnifiedProfileFormState,\r\n  formData: FormData\r\n): Promise<UnifiedProfileFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UnifiedProfileSchema.safeParse({\r\n    name: formData.get('name'),\r\n    address: formData.get('address'),\r\n    pincode: formData.get('pincode'),\r\n    city: formData.get('city'),\r\n    state: formData.get('state'),\r\n    locality: formData.get('locality'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { name, address, pincode, city, state, locality } = validatedFields.data;\r\n\r\n  // Validate that if any address field is provided, required fields are present\r\n  const hasAnyAddressField = pincode || city || state || locality;\r\n  if (hasAnyAddressField) {\r\n    if (!pincode || !city || !state || !locality) {\r\n      return {\r\n        message: 'If providing address information, pincode, city, state, and locality are required.',\r\n        success: false,\r\n      };\r\n    }\r\n  }\r\n\r\n  try {\r\n    // Update name in auth.users table (full_name in user_metadata)\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      data: { full_name: name }\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth user metadata:', authUpdateError);\r\n      return { message: `Auth Error: ${authUpdateError.message}`, success: false };\r\n    }\r\n\r\n    // Update address in customer_profiles table if address data is provided\r\n    if (hasAnyAddressField) {\r\n      const { error: updateError } = await supabase\r\n        .from('customer_profiles')\r\n        .update({\r\n          address: address || null,\r\n          pincode,\r\n          city,\r\n          state,\r\n          locality\r\n        })\r\n        .eq('id', user.id);\r\n\r\n      if (updateError) {\r\n        console.error('Error updating customer address:', updateError);\r\n        return { message: `Database Error: ${updateError.message}`, success: false };\r\n      }\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer/layout');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: hasAnyAddressField\r\n        ? 'Profile and address updated successfully!'\r\n        : 'Profile updated successfully!',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating profile and address:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,wCAAwC;AACxC,MAAM,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,KAAK;AAE3D;AAEA,sCAAsC;AACtC,MAAM,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,6HAAA,CAAA,qBAAkB;AAC3B;AAEA,+DAA+D;AAC/D,MAAM,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,SAAS,oIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,KAAK;QAAE,SAAS;IAAwC,GAC5D,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,SAAS,oIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAsB,GACxC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAkC;IACjE,MAAM,oIAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAmB,GACrC,MAAM,CAAC,CAAC,MAAQ,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG;QAAE,SAAS;IAAuB;IAC5E,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,MAAM,CAAC,CAAC,MAAQ,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG;QAAE,SAAS;IAAwB;IAC7E,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,MAAM,CAAC,CAAC,MAAQ,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG;QAAE,SAAS;IAA2B;AAClF;AAEA,sCAAsC;AACtC,MAAM,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC1E;AAEA,uCAAuC;AACvC,MAAM,eAAe,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,QAAQ,6HAAA,CAAA,qBAAkB;AAC5B;AAEA,4DAA4D;AAC5D,MAAM,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,KAAK;IACzD,SAAS,oIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,KAAK;QAAE,SAAS;IAAwC,GAC5D,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,SAAS,oIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAsB,GACxC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAkC,GAC9D,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,MAAM,oIAAA,CAAA,IAAC,CACJ,MAAM,GACN,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;AAClB;AA4DO,eAAe,sBACpB,SAA2B,EAC3B,QAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,cAAc;IAErD,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAqB,SAAS;QAAM;IACxD;IAEA,MAAM,kBAAkB,cAAc,SAAS,CAAC;QAC9C,MAAM,SAAS,GAAG,CAAC;IACrB;IAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;QACX;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAgB,IAAI;IAErC,IAAI;QACF,+DAA+D;QAC/D,+EAA+E;QAC/E,MAAM,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;YAChE,MAAM;gBAAE,WAAW;YAAK;QAC1B;QAEA,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;gBAAE,SAAS,CAAC,YAAY,EAAE,gBAAgB,OAAO,EAAE;gBAAE,SAAS;YAAM;QAC7E;QAEA,gFAAgF;QAChF,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,+BAA+B,gCAAgC;QAE9E,OAAO;YAAE,SAAS;YAAiC,SAAS;QAAK;IACnE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAiC,SAAS;QAAM;IACpE;AACF;AAEO,eAAe,sBACpB,SAA2B,EAC3B,QAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAqB,SAAS;QAAM;IACxD;IAEA,MAAM,kBAAkB,cAAc,SAAS,CAAC;QAC9C,SAAS,SAAS,GAAG,CAAC;QACtB,SAAS,SAAS,GAAG,CAAC;QACtB,MAAM,SAAS,GAAG,CAAC;QACnB,OAAO,SAAS,GAAG,CAAC;QACpB,UAAU,SAAS,GAAG,CAAC;IACzB;IAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;QACX;IACF;IAEA,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;IAExE,IAAI;QACF,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;YACN,SAAS,WAAW;YACpB;YACA;YACA;YACA;QACF,GACC,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,SAAS,CAAC,gBAAgB,EAAE,YAAY,OAAO,EAAE;gBAAE,SAAS;YAAM;QAC7E;QAEA,4BAA4B;QAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAiC,SAAS;QAAK;IACnE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAiC,SAAS;QAAM;IACpE;AACF;AAEO,eAAe,oBACpB,SAAyB,EACzB,QAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAqB,SAAS;QAAM;IACxD;IAEA,MAAM,kBAAkB,YAAY,SAAS,CAAC;QAC5C,OAAO,SAAS,GAAG,CAAC;IACtB;IAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;QACX;IACF;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,gBAAgB,IAAI;IAEtC,IAAI;QACF,kGAAkG;QAElG,0CAA0C;QAC1C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;YAAE,OAAO;QAAM,GACtB,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBAAE,SAAS,CAAC,gBAAgB,EAAE,YAAY,OAAO,EAAE;gBAAE,SAAS;YAAM;QAC7E;QAEA,4EAA4E;QAC5E,MAAM,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;YAChE,OAAO,CAAC,GAAG,EAAE,OAAO;QACtB;QAEA,IAAI,iBAAiB;YACnB,QAAQ,IAAI,CAAC,sCAAsC,gBAAgB,OAAO;QAC1E,0DAA0D;QAC1D,sDAAsD;QACxD;QAEA,4BAA4B;QAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAsC,SAAS;QAAK;IACxE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAiC,SAAS;QAAM;IACpE;AACF;AAEO,eAAe,oBACpB,SAAyB,EACzB,QAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAqB,SAAS;QAAM;IACxD;IAEA,MAAM,kBAAkB,YAAY,SAAS,CAAC;QAC5C,OAAO,SAAS,GAAG,CAAC;IACtB;IAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;QACX;IACF;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,gBAAgB,IAAI;IAEtC,IAAI;QACF,4EAA4E;QAC5E,MAAM,gBAAgB,KAAK,YAAY,EAAE,aAAa;QAEtD,sDAAsD;QACtD,IAAI,eAAe;QAEnB,IAAI,iBAAiB,KAAK,KAAK,EAAE;YAC/B,IAAI;gBACF,4CAA4C;gBAC5C,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;gBACpC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,YAAY,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE;gBAE3E,sDAAsD;gBACtD,IAAI,UAAU,MAAM,YAAY;oBAC9B,eAAe,SAAS,IAAI,CAAC,UAAU,CAAC,IAAI,CAC1C,CAAC,WAAa,SAAS,QAAQ,KAAK;gBAExC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,eAAe;YACjB;QACF;QAEA,+EAA+E;QAC/E,MAAM,2BAA2B,iBAAiB,CAAC;QAEnD,IAAI,0BAA0B;YAC5B,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,wCAAwC;QACxC,IAAI,KAAK,KAAK,KAAK,OAAO;YACxB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,4CAA4C;QAC5C,gDAAgD;QAChD,MAAM,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;YAChE,OAAO;QACT;QAEA,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAE5C,uCAAuC;YACvC,IAAI,eAAe;YACnB,IAAI,gBAAgB,OAAO,CAAC,QAAQ,CAAC,mDAAmD;gBACtF,eAAe;YACjB,OAAO,IAAI,gBAAgB,OAAO,CAAC,QAAQ,CAAC,qBAAqB;gBAC/D,eAAe;YACjB,OAAO,IAAI,gBAAgB,OAAO,CAAC,QAAQ,CAAC,eAAe;gBACzD,eAAe;YACjB;YAEA,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,mFAAmF;QAEnF,4BAA4B;QAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAiC,SAAS;QAAM;IACpE;AACF;AAEO,eAAe,qBACpB,SAA0B,EAC1B,QAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAqB,SAAS;QAAM;IACxD;IAEA,MAAM,kBAAkB,aAAa,SAAS,CAAC;QAC7C,QAAQ,SAAS,GAAG,CAAC;IACvB;IAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;QACX;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG,gBAAgB,IAAI;IAEvC,IAAI;QACF,yCAAyC;QACzC,MAAM,gBAAgB,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,SAAS,MAAM;QACrE,IAAI,WAAW,eAAe;YAC5B,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,mGAAmG;QAEnG,6CAA6C;QAC7C,iDAAiD;QACjD,MAAM,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;YAChE,OAAO,CAAC,GAAG,EAAE,QAAQ;QACvB;QAEA,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,uCAAuC;YACvC,IAAI,eAAe;YACnB,IAAI,gBAAgB,OAAO,CAAC,QAAQ,CAAC,mDAAmD;gBACtF,eAAe;YACjB,OAAO,IAAI,gBAAgB,OAAO,CAAC,QAAQ,CAAC,qBAAqB;gBAC/D,eAAe;YACjB,OAAO,IAAI,gBAAgB,OAAO,CAAC,QAAQ,CAAC,eAAe;gBACzD,eAAe;YACjB;YAEA,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,mFAAmF;QAEnF,4BAA4B;QAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YAAE,SAAS;YAAiC,SAAS;QAAM;IACpE;AACF;AAEO,eAAe,gCACpB,SAAkC,EAClC,QAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAqB,SAAS;QAAM;IACxD;IAEA,MAAM,kBAAkB,qBAAqB,SAAS,CAAC;QACrD,MAAM,SAAS,GAAG,CAAC;QACnB,SAAS,SAAS,GAAG,CAAC;QACtB,SAAS,SAAS,GAAG,CAAC;QACtB,MAAM,SAAS,GAAG,CAAC;QACnB,OAAO,SAAS,GAAG,CAAC;QACpB,UAAU,SAAS,GAAG,CAAC;IACzB;IAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,QAAQ,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YACnD,SAAS;QACX;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;IAE9E,8EAA8E;IAC9E,MAAM,qBAAqB,WAAW,QAAQ,SAAS;IACvD,IAAI,oBAAoB;QACtB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU;YAC5C,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;IACF;IAEA,IAAI;QACF,+DAA+D;QAC/D,MAAM,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;YAChE,MAAM;gBAAE,WAAW;YAAK;QAC1B;QAEA,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;gBAAE,SAAS,CAAC,YAAY,EAAE,gBAAgB,OAAO,EAAE;gBAAE,SAAS;YAAM;QAC7E;QAEA,wEAAwE;QACxE,IAAI,oBAAoB;YACtB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;gBACN,SAAS,WAAW;gBACpB;gBACA;gBACA;gBACA;YACF,GACC,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,OAAO;oBAAE,SAAS,CAAC,gBAAgB,EAAE,YAAY,OAAO,EAAE;oBAAE,SAAS;gBAAM;YAC7E;QACF;QAEA,4BAA4B;QAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YACL,SAAS,qBACL,8CACA;YACJ,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO;YAAE,SAAS;YAAiC,SAAS;QAAM;IACpE;AACF;;;IA/csB;IAoDA;IA6DA;IAiEA;IAiHA;IAkFA;;AArXA,+OAAA;AAoDA,+OAAA;AA6DA,+OAAA;AAiEA,+OAAA;AAiHA,+OAAA;AAkFA,+OAAA", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/location.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\n\r\n// --- Pincode Lookup Action ---\r\nexport async function getPincodeDetails(pincode: string): Promise<{\r\n  data?: {\r\n    city: string;\r\n    state: string;\r\n    localities: string[];\r\n  };\r\n  city?: string;\r\n  state?: string;\r\n  localities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!pincode || !/^\\d{6}$/.test(pincode)) {\r\n    return { error: \"Invalid Pincode format.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n  try {\r\n    // First get city and state from pincodes table\r\n    const { data: pincodeData, error: pincodeError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"OfficeName, DivisionName, StateName\")\r\n      .eq(\"Pincode\", pincode) // Updated column name to match database\r\n      .order(\"OfficeName\");\r\n\r\n    if (pincodeError) {\r\n      console.error(\"Pincode Fetch Error:\", pincodeError);\r\n      return { error: \"Database error fetching pincode details.\" };\r\n    }\r\n\r\n    if (!pincodeData || pincodeData.length === 0) {\r\n      return { error: \"Pincode not found.\" };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = pincodeData[0].StateName;\r\n\r\n    // Use DivisionName as the city (already cleaned)\r\n    const city = pincodeData[0].DivisionName;\r\n\r\n    // Get unique localities from post office names\r\n    const localities = [\r\n      ...new Set(pincodeData.map((item) => item.OfficeName)),\r\n    ] as string[];\r\n\r\n    return {\r\n      data: { city, state, localities },\r\n      city,\r\n      state,\r\n      localities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"Pincode Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during pincode lookup.\" };\r\n  }\r\n}\r\n// --- End Pincode Lookup ---\r\n\r\n// --- City Lookup Action ---\r\nexport async function getCityDetails(city: string): Promise<{\r\n  data?: {\r\n    pincodes: string[];\r\n    state: string;\r\n    localities: string[];\r\n  };\r\n  pincodes?: string[];\r\n  state?: string;\r\n  localities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!city || city.length < 2) {\r\n    return { error: \"City name must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n  try {\r\n    // Get pincodes and state for the city - DivisionName is the city column\r\n    const { data: cityData, error: cityError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"Pincode, OfficeName, StateName, DivisionName\")\r\n      .ilike(\"DivisionName\", `%${city}%`)\r\n      .order(\"Pincode\");\r\n\r\n    if (cityError) {\r\n      console.error(\"City Fetch Error:\", cityError);\r\n      return { error: \"Database error fetching city details.\" };\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      return { error: \"City not found.\" };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = cityData[0].StateName;\r\n\r\n    // Get unique pincodes\r\n    const pincodes = [...new Set(cityData.map((item) => item.Pincode))] as string[];\r\n\r\n    // Get unique localities from post office names\r\n    const localities = [\r\n      ...new Set(cityData.map((item) => item.OfficeName)),\r\n    ] as string[];\r\n\r\n    return {\r\n      data: { pincodes, state, localities },\r\n      pincodes,\r\n      state,\r\n      localities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"City Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during city lookup.\" };\r\n  }\r\n}\r\n// --- End City Lookup ---\r\n\r\n// --- City Autocomplete Action ---\r\n/**\r\n * Get city suggestions based on a search query\r\n *\r\n * This function uses the Supabase PostgreSQL function 'get_distinct_cities' to fetch unique city names.\r\n * The PostgreSQL function is defined as:\r\n *\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION get_distinct_cities(search_query TEXT, result_limit INTEGER)\r\n * RETURNS TABLE(city TEXT) AS $$\r\n * BEGIN\r\n *   RETURN QUERY\r\n *   SELECT DISTINCT \"DivisionName\" as city\r\n *   FROM pincodes\r\n *   WHERE \"DivisionName\" ILIKE search_query\r\n *   ORDER BY \"DivisionName\"\r\n *   LIMIT result_limit;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n * ```\r\n *\r\n * @param query The search query (minimum 2 characters)\r\n * @returns Array of up to 5 unique city suggestions\r\n */\r\nexport async function getCitySuggestions(query: string): Promise<{\r\n  data?: {\r\n    cities: string[];\r\n  };\r\n  cities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!query || query.length < 2) {\r\n    return { error: \"Query must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabaseAdmin = createAdminClient();\r\n  try {\r\n    // Use the PostgreSQL function to get distinct cities (up to 5)\r\n    const { data: cityData, error: cityError } = await supabaseAdmin\r\n      .rpc('get_distinct_cities', {\r\n        search_query: `%${query}%`,\r\n        result_limit: 5\r\n      });\r\n\r\n    if (cityError) {\r\n      console.error(\"City Suggestions Error:\", cityError);\r\n\r\n      // Fallback to regular query if RPC fails\r\n      try {\r\n        // Use a regular query as fallback\r\n        const { data: fallbackData, error: fallbackError } = await supabaseAdmin\r\n          .from(\"pincodes\")\r\n          .select(\"DivisionName\")\r\n          .ilike(\"DivisionName\", `%${query}%`)\r\n          .order(\"DivisionName\")\r\n          .limit(100);\r\n\r\n        if (fallbackError) {\r\n          throw fallbackError;\r\n        }\r\n\r\n        if (!fallbackData || fallbackData.length === 0) {\r\n          return { data: { cities: [] }, cities: [] };\r\n        }\r\n\r\n        // Get unique cities and format them\r\n        const cities = [...new Set(fallbackData.map((item) =>\r\n          item.DivisionName.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n        ))] as string[];\r\n\r\n        const topCities = cities.slice(0, 5);\r\n\r\n        return {\r\n          data: { cities: topCities },\r\n          cities: topCities\r\n        };\r\n      } catch (fallbackErr) {\r\n        console.error(\"Fallback City Query Error:\", fallbackErr);\r\n        return { error: \"Database error fetching city suggestions.\" };\r\n      }\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      return { data: { cities: [] }, cities: [] };\r\n    }\r\n\r\n    // Format the city names to Title Case\r\n    const cities = cityData.map((item: { city: string }) =>\r\n      item.city.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n    );\r\n\r\n    return {\r\n      data: { cities },\r\n      cities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"City Suggestions Exception:\", e);\r\n    return { error: \"An unexpected error occurred while fetching city suggestions.\" };\r\n  }\r\n}\r\n// --- End City Autocomplete ---\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;;;;AAGO,eAAe,kBAAkB,OAAe;IAWrD,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,CAAC,UAAU;QACxC,OAAO;YAAE,OAAO;QAA0B;IAC5C;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,IAAI;QACF,+CAA+C;QAC/C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,YACL,MAAM,CAAC,uCACP,EAAE,CAAC,WAAW,SAAS,wCAAwC;SAC/D,KAAK,CAAC;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBAAE,OAAO;YAA2C;QAC7D;QAEA,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;YAC5C,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,+DAA+D;QAC/D,MAAM,QAAQ,WAAW,CAAC,EAAE,CAAC,SAAS;QAEtC,iDAAiD;QACjD,MAAM,OAAO,WAAW,CAAC,EAAE,CAAC,YAAY;QAExC,+CAA+C;QAC/C,MAAM,aAAa;eACd,IAAI,IAAI,YAAY,GAAG,CAAC,CAAC,OAAS,KAAK,UAAU;SACrD;QAED,OAAO;YACL,MAAM;gBAAE;gBAAM;gBAAO;YAAW;YAChC;YACA;YACA;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,OAAO;QAAsD;IACxE;AACF;AAIO,eAAe,eAAe,IAAY;IAW/C,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;QAC5B,OAAO;YAAE,OAAO;QAA2C;IAC7D;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,IAAI;QACF,wEAAwE;QACxE,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,YACL,MAAM,CAAC,gDACP,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EACjC,KAAK,CAAC;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAwC;QAC1D;QAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,OAAO;gBAAE,OAAO;YAAkB;QACpC;QAEA,+DAA+D;QAC/D,MAAM,QAAQ,QAAQ,CAAC,EAAE,CAAC,SAAS;QAEnC,sBAAsB;QACtB,MAAM,WAAW;eAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO;SAAG;QAEnE,+CAA+C;QAC/C,MAAM,aAAa;eACd,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,OAAS,KAAK,UAAU;SAClD;QAED,OAAO;YACL,MAAM;gBAAE;gBAAU;gBAAO;YAAW;YACpC;YACA;YACA;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,OAAO;QAAmD;IACrE;AACF;AA2BO,eAAe,mBAAmB,KAAa;IAOpD,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO;YAAE,OAAO;QAAuC;IACzD;IAEA,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;IACtC,IAAI;QACF,+DAA+D;QAC/D,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAChD,GAAG,CAAC,uBAAuB;YAC1B,cAAc,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC1B,cAAc;QAChB;QAEF,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,yCAAyC;YACzC,IAAI;gBACF,kCAAkC;gBAClC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,cACxD,IAAI,CAAC,YACL,MAAM,CAAC,gBACP,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAClC,KAAK,CAAC,gBACN,KAAK,CAAC;gBAET,IAAI,eAAe;oBACjB,MAAM;gBACR;gBAEA,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;oBAC9C,OAAO;wBAAE,MAAM;4BAAE,QAAQ,EAAE;wBAAC;wBAAG,QAAQ,EAAE;oBAAC;gBAC5C;gBAEA,oCAAoC;gBACpC,MAAM,SAAS;uBAAI,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC,OAC3C,KAAK,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAiB,KAAK,WAAW;iBAClF;gBAEH,MAAM,YAAY,OAAO,KAAK,CAAC,GAAG;gBAElC,OAAO;oBACL,MAAM;wBAAE,QAAQ;oBAAU;oBAC1B,QAAQ;gBACV;YACF,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,OAAO;oBAAE,OAAO;gBAA4C;YAC9D;QACF;QAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,OAAO;gBAAE,MAAM;oBAAE,QAAQ,EAAE;gBAAC;gBAAG,QAAQ,EAAE;YAAC;QAC5C;QAEA,sCAAsC;QACtC,MAAM,SAAS,SAAS,GAAG,CAAC,CAAC,OAC3B,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAiB,KAAK,WAAW;QAG7E,OAAO;YACL,MAAM;gBAAE;YAAO;YACf;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,OAAO;QAAgE;IAClF;AACF,EACA,gCAAgC;;;IAvNV;IA0DA;IAiFA;;AA3IA,+OAAA;AA0DA,+OAAA;AAiFA,+OAAA", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/storage-paths.ts"], "sourcesContent": ["/**\r\n * Scalable Storage Path Utilities\r\n *\r\n * This module provides utilities for generating scalable storage paths\r\n * that can handle billions of users efficiently using hash-based distribution.\r\n */\r\n\r\n/**\r\n * Generate scalable user path using hash-based distribution\r\n *\r\n * @param userId - The user's UUID\r\n * @returns Scalable path: users/{prefix}/{midfix}/{userId}\r\n *\r\n * Example:\r\n * - Input: \"a1b2c3d4-e5f6-7890-abcd-ef1234567890\"\r\n * - Output: \"users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890\"\r\n */\r\nexport function getScalableUserPath(userId: string): string {\r\n  if (!userId || typeof userId !== 'string') {\r\n    throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);\r\n  }\r\n\r\n  if (userId.length < 4) {\r\n    throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);\r\n  }\r\n\r\n  const prefix = userId.substring(0, 2).toLowerCase();\r\n  const midfix = userId.substring(2, 4).toLowerCase();\r\n\r\n  return `users/${prefix}/${midfix}/${userId}`;\r\n}\r\n\r\n/**\r\n * Generate profile image path\r\n */\r\nexport function getProfileImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/profile/logo_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate product image path (legacy - for backward compatibility)\r\n * @deprecated Use getProductBaseImagePath or getProductVariantImagePath instead\r\n */\r\nexport function getProductImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate base product image path\r\n */\r\nexport function getProductBaseImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate product variant image path\r\n */\r\nexport function getProductVariantImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  variantId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate gallery image path\r\n */\r\nexport function getGalleryImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/gallery/gallery_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate post image path\r\n */\r\nexport function getPostImagePath(\r\n  userId: string,\r\n  postId: string,\r\n  imageIndex: number,\r\n  timestamp: number,\r\n  createdAt?: string\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n\r\n  // Use post creation date if provided, otherwise use current date (for backward compatibility)\r\n  const dateToUse = createdAt ? new Date(createdAt) : new Date();\r\n  const year = dateToUse.getFullYear();\r\n  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate post folder path for deletion\r\n */\r\nexport function getPostFolderPath(userId: string, postId: string, createdAt: string): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  const postDate = new Date(createdAt);\r\n  const year = postDate.getFullYear();\r\n  const month = String(postDate.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}`;\r\n}\r\n\r\n/**\r\n * Generate customer avatar image path\r\n */\r\nexport function getCustomerAvatarPath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/avatar/avatar_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate customer post image path\r\n */\r\nexport function getCustomerPostImagePath(\r\n  userId: string,\r\n  postId: string,\r\n  imageIndex: number,\r\n  timestamp: number,\r\n  createdAt?: string\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n\r\n  // Use post creation date if provided, otherwise use current date (for backward compatibility)\r\n  const dateToUse = createdAt ? new Date(createdAt) : new Date();\r\n  const year = dateToUse.getFullYear();\r\n  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate custom ad image path\r\n */\r\nexport function getCustomAdImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/ads/custom_ad_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate custom header image path\r\n */\r\nexport function getCustomHeaderImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/branding/header_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate theme-specific custom header image path\r\n */\r\nexport function getThemeSpecificHeaderImagePath(\r\n  userId: string,\r\n  timestamp: number,\r\n  theme: 'light' | 'dark'\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/branding/header_${theme}_${timestamp}.webp`;\r\n}\r\n\r\n// Legacy utilities removed since migration is complete\r\n\r\n/**\r\n * Path validation utilities\r\n */\r\nexport class PathValidator {\r\n  /**\r\n   * Validate if a path follows the new scalable structure\r\n   */\r\n  static isScalablePath(path: string): boolean {\r\n    return path.startsWith('users/') && path.split('/').length >= 4;\r\n  }\r\n\r\n  /**\r\n   * Extract user ID from scalable path\r\n   */\r\n  static extractUserIdFromPath(path: string): string | null {\r\n    if (!this.isScalablePath(path)) {\r\n      return null;\r\n    }\r\n\r\n    const parts = path.split('/');\r\n    return parts[3]; // users/{prefix}/{midfix}/{userId}/...\r\n  }\r\n\r\n  /**\r\n   * Validate path structure integrity\r\n   */\r\n  static validatePathStructure(userId: string, path: string): boolean {\r\n    const expectedUserPath = getScalableUserPath(userId);\r\n    return path.startsWith(expectedUserPath);\r\n  }\r\n}\r\n\r\n/**\r\n * Storage analytics utilities\r\n */\r\nexport class StorageAnalytics {\r\n  /**\r\n   * Get storage distribution info for monitoring\r\n   */\r\n  static getDistributionInfo(userId: string): {\r\n    prefix: string;\r\n    midfix: string;\r\n    bucket: string;\r\n    estimatedPeers: number;\r\n  } {\r\n    const prefix = userId.substring(0, 2).toLowerCase();\r\n    const midfix = userId.substring(2, 4).toLowerCase();\r\n\r\n    // Estimate number of users in same bucket (assuming even distribution)\r\n    const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets\r\n    const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users\r\n\r\n    return {\r\n      prefix,\r\n      midfix,\r\n      bucket: `${prefix}/${midfix}`,\r\n      estimatedPeers\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;;;;;;;;CASC;;;;;;;;;;;;;;;;;AACM,SAAS,oBAAoB,MAAc;IAChD,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,OAAO,OAAO,SAAS,EAAE,QAAQ;IAC3F;IAEA,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,MAAM,IAAI,MAAM,CAAC,yDAAyD,EAAE,QAAQ;IACtF;IAEA,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;IACjD,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;IAEjD,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ;AAC9C;AAKO,SAAS,oBAAoB,MAAc,EAAE,SAAiB;IACnE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,cAAc,EAAE,UAAU,KAAK,CAAC;AACrD;AAMO,SAAS,oBACd,MAAc,EACd,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAClF;AAKO,SAAS,wBACd,MAAc,EACd,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,YAAY,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AACvF;AAKO,SAAS,2BACd,MAAc,EACd,SAAiB,EACjB,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,CAAC,EAAE,UAAU,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC/F;AAKO,SAAS,oBAAoB,MAAc,EAAE,SAAiB;IACnE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,UAAU,KAAK,CAAC;AACxD;AAKO,SAAS,iBACd,MAAc,EACd,MAAc,EACd,UAAkB,EAClB,SAAiB,EACjB,SAAkB;IAElB,MAAM,WAAW,oBAAoB;IAErC,8FAA8F;IAC9F,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,IAAI;IACxD,MAAM,OAAO,UAAU,WAAW;IAClC,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE3D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC7F;AAKO,SAAS,kBAAkB,MAAc,EAAE,MAAc,EAAE,SAAiB;IACjF,MAAM,WAAW,oBAAoB;IACrC,MAAM,WAAW,IAAI,KAAK;IAC1B,MAAM,OAAO,SAAS,WAAW;IACjC,MAAM,QAAQ,OAAO,SAAS,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE1D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ;AACvD;AAKO,SAAS,sBAAsB,MAAc,EAAE,SAAiB;IACrE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,eAAe,EAAE,UAAU,KAAK,CAAC;AACtD;AAKO,SAAS,yBACd,MAAc,EACd,MAAc,EACd,UAAkB,EAClB,SAAiB,EACjB,SAAkB;IAElB,MAAM,WAAW,oBAAoB;IAErC,8FAA8F;IAC9F,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,IAAI;IACxD,MAAM,OAAO,UAAU,WAAW;IAClC,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE3D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC7F;AAKO,SAAS,qBAAqB,MAAc,EAAE,SAAiB;IACpE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,eAAe,EAAE,UAAU,KAAK,CAAC;AACtD;AAKO,SAAS,yBAAyB,MAAc,EAAE,SAAiB;IACxE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,UAAU,KAAK,CAAC;AACxD;AAKO,SAAS,gCACd,MAAc,EACd,SAAiB,EACjB,KAAuB;IAEvB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,MAAM,CAAC,EAAE,UAAU,KAAK,CAAC;AACjE;AAOO,MAAM;IACX;;GAEC,GACD,OAAO,eAAe,IAAY,EAAW;QAC3C,OAAO,KAAK,UAAU,CAAC,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,IAAI;IAChE;IAEA;;GAEC,GACD,OAAO,sBAAsB,IAAY,EAAiB;QACxD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO;YAC9B,OAAO;QACT;QAEA,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,OAAO,KAAK,CAAC,EAAE,EAAE,uCAAuC;IAC1D;IAEA;;GAEC,GACD,OAAO,sBAAsB,MAAc,EAAE,IAAY,EAAW;QAClE,MAAM,mBAAmB,oBAAoB;QAC7C,OAAO,KAAK,UAAU,CAAC;IACzB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,oBAAoB,MAAc,EAKvC;QACA,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;QACjD,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;QAEjD,uEAAuE;QACvE,MAAM,eAAe,KAAK,KAAK,KAAK,IAAI,iBAAiB;QACzD,MAAM,iBAAiB,KAAK,KAAK,CAAC,UAAU,eAAe,wBAAwB;QAEnF,OAAO;YACL;YACA;YACA,QAAQ,GAAG,OAAO,CAAC,EAAE,QAAQ;YAC7B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/avatar-actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { getCustomerAvatarPath } from '@/lib/utils/storage-paths';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// Action to upload avatar and return public URL\r\nexport async function uploadAvatarAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"avatarFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No avatar file provided.\" };\r\n  }\r\n\r\n  const allowedTypes = [\"image/png\", \"image/jpeg\", \"image/gif\", \"image/webp\"];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation (15MB limit)\r\n  if (file.size > 15 * 1024 * 1024) {\r\n    return { success: false, error: \"File size must be less than 15MB.\" };\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    const bucketName = \"customers\"; // Plural form - matches the bucket name\r\n    const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000); // Add random milliseconds for extra uniqueness\r\n    const fullPath = getCustomerAvatarPath(userId, timestamp);\r\n\r\n    // Upload the processed image\r\n    const { error: uploadError } = await supabase.storage\r\n      .from(bucketName)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Avatar Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload avatar: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // No need to add timestamp to URL as we already have it in the filename\r\n    const { data: urlData } = supabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n\r\n// Action to specifically update only the avatar URL\r\nexport async function updateAvatarUrl(\r\n  avatarUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"customer_profiles\")\r\n    .update({ avatar_url: avatarUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Avatar URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update avatar URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  // Revalidate paths to update the UI\r\n  revalidatePath('/dashboard/customer');\r\n  revalidatePath('/dashboard/customer/profile');\r\n\r\n  return { success: true };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;;;;;AAGO,eAAe,sBACpB,QAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IACA,MAAM,SAAS,KAAK,EAAE;IAEtB,MAAM,OAAO,SAAS,GAAG,CAAC;IAC1B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAA2B;IAC7D;IAEA,MAAM,eAAe;QAAC;QAAa;QAAc;QAAa;KAAa;IAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqB;IACvD;IAEA,gDAAgD;IAChD,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;QAChC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,IAAI;QACF,4DAA4D;QAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QAErD,MAAM,aAAa,aAAa,wCAAwC;QACxE,MAAM,YAAY,IAAI,OAAO,OAAO,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,+CAA+C;QAC1H,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;QAE/C,6BAA6B;QAC7B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CAClD,IAAI,CAAC,YACL,MAAM,CAAC,UAAU,YAAY;YAC5B,aAAa,KAAK,IAAI;YACtB,QAAQ;QACV;QAEF,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,yBAAyB,EAAE,YAAY,OAAO,EAAE;YAC1D;QACF;QAEA,wEAAwE;QACxE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,SAAS,OAAO,CACvC,IAAI,CAAC,YACL,YAAY,CAAC;QAEhB,IAAI,CAAC,SAAS,WAAW;YACvB,QAAQ,KAAK,CACX,kFACA;YAEF,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,KAAK,QAAQ,SAAS;QAAC;IACjD,EAAE,OAAO,iBAAiB;QACxB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqC;IACvE;AACF;AAGO,eAAe,gBACpB,SAAiB;IAEjB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;QAAE,YAAY;QAAW,YAAY,IAAI,OAAO,WAAW;IAAG,GACrE,EAAE,CAAC,MAAM,KAAK,EAAE;IAEnB,IAAI,aAAa;QACf,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YACL,SAAS;YACT,OAAO,CAAC,6BAA6B,EAAE,YAAY,OAAO,EAAE;QAC9D;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QAAE,SAAS;IAAK;AACzB;;;IA7GsB;IA8EA;;AA9EA,+OAAA;AA8EA,+OAAA", "debugId": null}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/customer/profile/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '00a78b43259bdfa35946a0918da66b9382dcd7b4dc'} from 'ACTIONS_MODULE0'\nexport {updateCustomerProfileAndAddress as '60e2048999976108d182f440e74ddcd263930eb412'} from 'ACTIONS_MODULE1'\nexport {updateCustomerProfile as '60606984ed763a79a21c8467f3859b77a5c30c66eb'} from 'ACTIONS_MODULE1'\nexport {updateCustomerAddress as '601efcd933277679be074bdf16199352e0f1ee1dd3'} from 'ACTIONS_MODULE1'\nexport {getPincodeDetails as '40ab9ff6341449bb46121f282a1e253cc89e3417db'} from 'ACTIONS_MODULE2'\nexport {uploadAvatarAndGetUrl as '400412f1eb89dd7bc7c1bba76428244b575e3acba6'} from 'ACTIONS_MODULE3'\nexport {updateAvatarUrl as '40ebef699b6761cfd539429fec1e4d0a90ae48b158'} from 'ACTIONS_MODULE3'\n"], "names": [], "mappings": ";AAAA;AACA;AAGA;AACA", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/ProfilePageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+U,GAC5W,6GACA", "debugId": null}}, {"offset": {"line": 1214, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/ProfilePageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2T,GACxV,yFACA", "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/addressValidation.ts"], "sourcesContent": ["/**\r\n * Customer address validation utility\r\n * Checks if customer has complete address information\r\n */\r\n\r\nexport interface CustomerAddressData {\r\n  pincode?: string | null;\r\n  state?: string | null;\r\n  city?: string | null;\r\n  locality?: string | null;\r\n  // address is optional as per requirements\r\n  address?: string | null;\r\n}\r\n\r\nexport interface CustomerProfileData extends CustomerAddressData {\r\n  name?: string | null;\r\n}\r\n\r\n/**\r\n * Validates if customer address is complete\r\n * Address field is optional, but pincode, state, city, and locality are required\r\n */\r\nexport function isCustomerAddressComplete(addressData: CustomerAddressData): boolean {\r\n  const { pincode, state, city, locality } = addressData;\r\n\r\n  // Check if required fields are present and not empty\r\n  return !!(\r\n    pincode && pincode.trim() !== '' &&\r\n    state && state.trim() !== '' &&\r\n    city && city.trim() !== '' &&\r\n    locality && locality.trim() !== ''\r\n  );\r\n}\r\n\r\n/**\r\n * Gets missing address fields for customer\r\n */\r\nexport function getMissingAddressFields(addressData: CustomerAddressData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  if (!addressData.pincode || addressData.pincode.trim() === '') {\r\n    missing.push('pincode');\r\n  }\r\n  if (!addressData.state || addressData.state.trim() === '') {\r\n    missing.push('state');\r\n  }\r\n  if (!addressData.city || addressData.city.trim() === '') {\r\n    missing.push('city');\r\n  }\r\n  if (!addressData.locality || addressData.locality.trim() === '') {\r\n    missing.push('locality');\r\n  }\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing address fields\r\n */\r\nexport function getAddressValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n\r\n/**\r\n * Validates if customer name is complete\r\n */\r\nexport function isCustomerNameComplete(name?: string | null): boolean {\r\n  return !!(name && name.trim() !== '');\r\n}\r\n\r\n/**\r\n * Validates if customer profile is complete (both name and address)\r\n */\r\nexport function isCustomerProfileComplete(profileData: CustomerProfileData): boolean {\r\n  return isCustomerNameComplete(profileData.name) && isCustomerAddressComplete(profileData);\r\n}\r\n\r\n/**\r\n * Gets missing profile fields for customer (name + address)\r\n */\r\nexport function getMissingProfileFields(profileData: CustomerProfileData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  // Check name\r\n  if (!isCustomerNameComplete(profileData.name)) {\r\n    missing.push('name');\r\n  }\r\n\r\n  // Check address fields\r\n  const missingAddressFields = getMissingAddressFields(profileData);\r\n  missing.push(...missingAddressFields);\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing profile fields (name + address)\r\n */\r\nexport function getProfileValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'name': return 'Name';\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAmBM,SAAS,0BAA0B,WAAgC;IACxE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;IAE3C,qDAAqD;IACrD,OAAO,CAAC,CAAC,CACP,WAAW,QAAQ,IAAI,OAAO,MAC9B,SAAS,MAAM,IAAI,OAAO,MAC1B,QAAQ,KAAK,IAAI,OAAO,MACxB,YAAY,SAAS,IAAI,OAAO,EAClC;AACF;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,IAAI,CAAC,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,IAAI,OAAO,IAAI;QAC7D,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,KAAK,CAAC,IAAI,OAAO,IAAI;QACzD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,OAAO,IAAI;QACvD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,IAAI,OAAO,IAAI;QAC/D,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF;AAKO,SAAS,uBAAuB,IAAoB;IACzD,OAAO,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,OAAO,EAAE;AACtC;AAKO,SAAS,0BAA0B,WAAgC;IACxE,OAAO,uBAAuB,YAAY,IAAI,KAAK,0BAA0B;AAC/E;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,aAAa;IACb,IAAI,CAAC,uBAAuB,YAAY,IAAI,GAAG;QAC7C,QAAQ,IAAI,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,uBAAuB,wBAAwB;IACrD,QAAQ,IAAI,IAAI;IAEhB,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF", "debugId": null}}, {"offset": {"line": 1350, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/page.tsx"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { redirect } from 'next/navigation';\r\nimport { Metadata } from 'next';\r\nimport ProfilePageClient from './components/ProfilePageClient';\r\nimport { isCustomerAddressComplete } from '@/lib/utils/addressValidation';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'My Profile - Dukancard',\r\n  description: 'Manage your Dukancard customer profile.',\r\n  robots: 'noindex, nofollow',\r\n};\r\n\r\nexport default async function CustomerProfilePage() {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    redirect('/login');\r\n  }\r\n\r\n  // Fetch customer profile data from customer_profiles table using user.id\r\n  const { data: profile, error: profileError } = await supabase\r\n    .from('customer_profiles')\r\n    .select('avatar_url, address, pincode, city, state, locality')\r\n    .eq('id', user.id)\r\n    .maybeSingle(); // Use maybeSingle to handle potential null profile\r\n\r\n  // Get name from auth.users table (full_name column)\r\n  let initialName: string | null = null;\r\n  if (user.user_metadata?.full_name) {\r\n    initialName = user.user_metadata.full_name;\r\n  } else if (user.user_metadata?.name) {\r\n    initialName = user.user_metadata.name;\r\n  }\r\n\r\n  let initialAvatarUrl: string | null = null;\r\n  let initialAddressData = null;\r\n  let hasCompleteAddress = false;\r\n\r\n  if (profileError) {\r\n    console.error('Error fetching customer profile:', profileError);\r\n    // Handle error appropriately, maybe show a message to the user\r\n    // For now, we'll proceed with null values, the components can handle it\r\n  } else {\r\n    initialAvatarUrl = profile?.avatar_url || null; // Use fetched avatar_url or null\r\n    initialAddressData = {\r\n      address: profile?.address,\r\n      pincode: profile?.pincode,\r\n      city: profile?.city,\r\n      state: profile?.state,\r\n      locality: profile?.locality,\r\n    };\r\n\r\n    // Check if address is complete\r\n    hasCompleteAddress = isCustomerAddressComplete(initialAddressData);\r\n  }\r\n\r\n  return (\r\n    <ProfilePageClient\r\n      initialName={initialName}\r\n      initialAvatarUrl={initialAvatarUrl}\r\n      initialAddressData={initialAddressData}\r\n      hasCompleteAddress={hasCompleteAddress}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAEA;AACA;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,QAAQ;AACV;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,yEAAyE;IACzE,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,qBACL,MAAM,CAAC,uDACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW,IAAI,mDAAmD;IAErE,oDAAoD;IACpD,IAAI,cAA6B;IACjC,IAAI,KAAK,aAAa,EAAE,WAAW;QACjC,cAAc,KAAK,aAAa,CAAC,SAAS;IAC5C,OAAO,IAAI,KAAK,aAAa,EAAE,MAAM;QACnC,cAAc,KAAK,aAAa,CAAC,IAAI;IACvC;IAEA,IAAI,mBAAkC;IACtC,IAAI,qBAAqB;IACzB,IAAI,qBAAqB;IAEzB,IAAI,cAAc;QAChB,QAAQ,KAAK,CAAC,oCAAoC;IAClD,+DAA+D;IAC/D,wEAAwE;IAC1E,OAAO;QACL,mBAAmB,SAAS,cAAc,MAAM,iCAAiC;QACjF,qBAAqB;YACnB,SAAS,SAAS;YAClB,SAAS,SAAS;YAClB,MAAM,SAAS;YACf,OAAO,SAAS;YAChB,UAAU,SAAS;QACrB;QAEA,+BAA+B;QAC/B,qBAAqB,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE;IACjD;IAEA,qBACE,8OAAC,4LAAA,CAAA,UAAiB;QAChB,aAAa;QACb,kBAAkB;QAClB,oBAAoB;QACpB,oBAAoB;;;;;;AAG1B", "debugId": null}}]}