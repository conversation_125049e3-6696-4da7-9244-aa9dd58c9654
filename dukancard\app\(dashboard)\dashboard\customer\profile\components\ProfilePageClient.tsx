"use client";

import { useState } from "react";
import { User } from "lucide-react";
import AvatarUpload from "./AvatarUpload";
import ProfileRequirementDialog from "./ProfileRequirementDialog";
import UnifiedProfileForm from "./UnifiedProfileForm";

interface ProfilePageClientProps {
  initialName: string | null;
  initialAvatarUrl?: string | null;
  initialAddressData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  } | null;
  hasCompleteAddress?: boolean;
}

export default function ProfilePageClient({
  initialName,
  initialAvatarUrl,
  initialAddressData,
  hasCompleteAddress = false,
}: ProfilePageClientProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(initialAvatarUrl || undefined);

  return (
    <>
      {/* Profile Requirement Dialog */}
      <ProfileRequirementDialog
        hasCompleteAddress={hasCompleteAddress}
      />

      <div className="space-y-8">
        {/* Header Section */}
        <div className="flex items-center gap-4">
          <div className="p-3 rounded-xl bg-muted hidden sm:block">
            <User className="w-6 h-6 text-foreground" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">
              Profile Information
            </h1>
            <p className="text-muted-foreground mt-1">
              Update your personal details and address
            </p>
          </div>
        </div>

        {/* Content Section */}
        <div className="space-y-8">
          {/* Avatar Upload Section */}
          <div className="flex justify-center">
            <AvatarUpload
              initialAvatarUrl={avatarUrl}
              userName={initialName}
              onUpdateAvatar={(url) => setAvatarUrl(url)}
            />
          </div>

          {/* Unified Profile and Address Form */}
          <UnifiedProfileForm
            initialName={initialName}
            initialAddressData={initialAddressData}
            avatarUrl={avatarUrl}
          />
        </div>
      </div>
    </>
  );
}
