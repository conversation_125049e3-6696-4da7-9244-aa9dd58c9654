'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Compass, Bell } from 'lucide-react';
import SubscriptionCard, { SubscriptionData } from './SubscriptionCard';

interface SubscriptionListProps {
  initialSubscriptions: SubscriptionData[];
  onUnsubscribeSuccess?: (_subscriptionId: string) => void;
  showUnsubscribe?: boolean;
  variant?: 'default' | 'compact';
  emptyMessage?: string;
  emptyDescription?: string;
  showDiscoverButton?: boolean;
}

export default function SubscriptionList({
  initialSubscriptions,
  onUnsubscribeSuccess,
  showUnsubscribe = true,
  variant = 'default',
  emptyMessage = "No subscriptions found.",
  emptyDescription = "Subscribe to profiles to see them here.",
  showDiscoverButton = false
}: SubscriptionListProps) {
  const [subscriptions, setSubscriptions] = useState(initialSubscriptions);

  // Update subscriptions when initialSubscriptions changes
  useEffect(() => {
    setSubscriptions(initialSubscriptions);
  }, [initialSubscriptions]);

  const handleUnsubscribeSuccess = (subscriptionIdToRemove: string) => {
    setSubscriptions(currentSubscriptions =>
      currentSubscriptions.filter(sub => sub.id !== subscriptionIdToRemove)
    );

    // Call parent callback if provided
    if (onUnsubscribeSuccess) {
      onUnsubscribeSuccess(subscriptionIdToRemove);
    }
  };

  if (subscriptions.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="mb-4">
            <div className="w-16 h-16 mx-auto bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center">
              <Bell className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <h3 className="text-lg font-medium text-neutral-800 dark:text-neutral-100 mb-2">
            {emptyMessage}
          </h3>
          <p className="text-neutral-500 dark:text-neutral-400 mb-6">
            {emptyDescription}
          </p>
          {showDiscoverButton && (
            <Button asChild variant="outline" className="gap-2">
              <Link href="/discover" target="_blank" rel="noopener noreferrer">
                <Compass className="w-4 h-4" />
                Discover Businesses
              </Link>
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {subscriptions.map((sub, _index) => {
        const profile = sub.profile;

        if (!profile) {
          return null; // Skip items with missing profiles
        }

        return (
          <SubscriptionCard
            key={sub.id}
            subscriptionId={sub.id}
            profile={profile}
            onUnsubscribeSuccess={showUnsubscribe ? handleUnsubscribeSuccess : undefined}
            showUnsubscribe={showUnsubscribe}
            variant={variant}
          />
        );
      })}
    </div>
  );
}
