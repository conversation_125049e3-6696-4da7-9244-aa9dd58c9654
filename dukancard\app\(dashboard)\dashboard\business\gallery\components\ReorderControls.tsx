import { motion } from "framer-motion";
import { Info, RotateCcw, Save, Loader2, GripVertical } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";


interface ReorderControlsProps {
  isReordering: boolean;
  hasUnsavedChanges: boolean;
  isSavingOrder: boolean;
  onSaveOrder: () => void;
  onResetOrder: () => void;
}

export default function ReorderControls({
  isReordering,
  hasUnsavedChanges,
  isSavingOrder,
  onSaveOrder,
  onResetOrder,
}: ReorderControlsProps) {
  return (
    <div className="space-y-3">
      <div className={cn(
        "flex items-center gap-2 text-sm rounded-lg p-3 transition-all duration-200",
        isReordering
          ? "bg-amber-500/20 text-amber-700 dark:text-amber-300 border border-amber-500/30"
          : "bg-muted/30 text-muted-foreground"
      )}>
        <GripVertical className="h-4 w-4" />
        <span>
          {isReordering
            ? "Reordering images... Drop to place in new position."
            : "Drag the grip handle (⋮⋮) on images to reorder them. Changes will be saved when you click \"Save Order\"."}
        </span>
      </div>

      {/* Save/Reset Controls */}
      {hasUnsavedChanges && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center gap-3 p-3 bg-amber-500/10 border border-amber-500/20 rounded-lg"
        >
          <div className="flex items-center gap-2 text-sm text-amber-700 dark:text-amber-300 font-medium">
            <Info className="h-4 w-4" />
            <span>You have unsaved changes</span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onResetOrder}
              disabled={isSavingOrder}
              className="text-xs border-amber-500/30 hover:bg-amber-500/10"
            >
              <RotateCcw className="mr-1 h-3 w-3" />
              Reset
            </Button>
            <Button
              onClick={onSaveOrder}
              disabled={isSavingOrder}
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-xs"
              size="sm"
            >
              {isSavingOrder ? (
                <>
                  <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-1 h-3 w-3" />
                  Save Order
                </>
              )}
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  );
}
