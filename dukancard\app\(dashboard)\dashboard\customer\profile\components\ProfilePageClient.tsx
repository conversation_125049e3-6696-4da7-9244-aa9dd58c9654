"use client";

import { useState } from "react";
import { User } from "lucide-react";
import { ProfileForm } from "../ProfileForm";
import AvatarUpload from "./AvatarUpload";
import AddressForm from "./AddressForm";
import ProfileRequirementDialog from "./ProfileRequirementDialog";

interface ProfilePageClientProps {
  initialName: string | null;
  initialAvatarUrl?: string | null;
  initialAddressData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  } | null;
  hasCompleteAddress?: boolean;
}

export default function ProfilePageClient({
  initialName,
  initialAvatarUrl,
  initialAddressData,
  hasCompleteAddress = false,
}: ProfilePageClientProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(initialAvatarUrl || undefined);

  return (
    <>
      {/* Profile Requirement Dialog */}
      <ProfileRequirementDialog
        hasCompleteAddress={hasCompleteAddress}
      />

      <div className="space-y-8">
        {/* Header Section */}
        <div className="flex items-center gap-4">
          <div className="p-3 rounded-xl bg-muted hidden sm:block">
            <User className="w-6 h-6 text-foreground" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">
              Profile Information
            </h1>
            <p className="text-muted-foreground mt-1">
              Update your personal details and address
            </p>
          </div>
        </div>

        {/* Content Section */}
        <div className="space-y-8">
          {/* Profile Information Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Avatar Upload */}
            <div className="flex flex-col items-center">
              <AvatarUpload
                initialAvatarUrl={avatarUrl}
                userName={initialName}
                onUpdateAvatar={(url) => setAvatarUrl(url)}
              />
            </div>

            {/* Profile Form */}
            <div>
              <ProfileForm initialName={initialName} />
            </div>
          </div>

          {/* Address Form Section */}
          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">
              Address Information
            </h2>
            <AddressForm initialData={initialAddressData || undefined} />
          </div>
        </div>
      </div>
    </>
  );
}
