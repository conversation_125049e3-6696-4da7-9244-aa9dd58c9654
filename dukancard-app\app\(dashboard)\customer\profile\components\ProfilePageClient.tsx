import React, { useState, useMemo } from "react";
import { View, Text, ScrollView } from "react-native";
import { User } from "lucide-react-native";
import UnifiedProfileForm from "./UnifiedProfileForm";
import ProfileRequirementDialog from "./ProfileRequirementDialog";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { useTheme } from "@/src/hooks/useTheme";
import { profileStyles } from "@/styles/dashboard/customer/profile";
import { responsiveFontSize } from "@/lib/theme/colors";

interface ProfilePageClientProps {
  initialName: string | null;
  initialAvatarUrl?: string | null;
  initialAddressData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  } | null;
  hasCompleteAddress?: boolean;
}

export default function ProfilePageClient({
  initialName,
  initialAvatarUrl,
  initialAddressData,
  hasCompleteAddress = false,
}: ProfilePageClientProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(initialAvatarUrl || undefined);
  const theme = useTheme();
  const styles = profileStyles(theme);

  return (
    <ScrollView style={styles.container}>
      {/* Profile Requirement Dialog */}
      <ProfileRequirementDialog
        hasCompleteAddress={hasCompleteAddress}
      />

      {/* Main Profile Card */}
      <View style={[styles.card, { backgroundColor: theme.colors.card }]}>
        {/* Header */}
        <View style={styles.cardHeader}>
          <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '1A' }]}>
            <User size={responsiveFontSize(20)} color={theme.colors.primary} />
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={[styles.cardTitle, { color: theme.colors.textPrimary }]}>
              Profile Information
            </Text>
            <Text style={[styles.cardSubtitle, { color: theme.colors.textSecondary }]}>
              Update your personal details
            </Text>
          </View>
        </View>

        {/* Avatar Upload */}
        <View style={styles.avatarContainer}>
          <AvatarUpload
            initialAvatarUrl={avatarUrl}
            userName={initialName}
            onUpdateAvatar={(url: string) => setAvatarUrl(url)}
          />
        </View>

        {/* Profile Form */}
        <View style={styles.formContainer}>
          <ProfileForm initialName={initialName} />
        </View>
      </View>

      {/* Address Form Section */}
      <View style={[styles.card, { backgroundColor: theme.colors.card }]}>
        <AddressForm initialData={initialAddressData || undefined} />
      </View>
    </ScrollView>
  );
}
