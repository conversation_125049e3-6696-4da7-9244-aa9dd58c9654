module.exports = {

"[project]/components/ui/label.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
function Label({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "label",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/label.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/components/ui/form.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Form": (()=>Form),
    "FormControl": (()=>FormControl),
    "FormDescription": (()=>FormDescription),
    "FormField": (()=>FormField),
    "FormItem": (()=>FormItem),
    "FormLabel": (()=>FormLabel),
    "FormMessage": (()=>FormMessage),
    "useFormField": (()=>useFormField)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/label.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
const Form = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormProvider"];
const FormFieldContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])({});
const FormField = ({ ...props })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(FormFieldContext.Provider, {
        value: {
            name: props.name
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Controller"], {
            ...props
        }, void 0, false, {
            fileName: "[project]/components/ui/form.tsx",
            lineNumber: 40,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
};
const useFormField = ()=>{
    const fieldContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(FormFieldContext);
    const itemContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(FormItemContext);
    const { getFieldState } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFormContext"])();
    const formState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFormState"])({
        name: fieldContext.name
    });
    const fieldState = getFieldState(fieldContext.name, formState);
    if (!fieldContext) {
        throw new Error("useFormField should be used within <FormField>");
    }
    const { id } = itemContext;
    return {
        id,
        name: fieldContext.name,
        formItemId: `${id}-form-item`,
        formDescriptionId: `${id}-form-item-description`,
        formMessageId: `${id}-form-item-message`,
        ...fieldState
    };
};
const FormItemContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])({});
function FormItem({ className, ...props }) {
    const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItemContext.Provider, {
        value: {
            id
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            "data-slot": "form-item",
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("grid gap-2", className),
            ...props
        }, void 0, false, {
            fileName: "[project]/components/ui/form.tsx",
            lineNumber: 81,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 80,
        columnNumber: 5
    }, this);
}
function FormLabel({ className, ...props }) {
    const { error, formItemId } = useFormField();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
        "data-slot": "form-label",
        "data-error": !!error,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("data-[error=true]:text-destructive", className),
        htmlFor: formItemId,
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 97,
        columnNumber: 5
    }, this);
}
function FormControl({ ...props }) {
    const { error, formItemId, formDescriptionId, formMessageId } = useFormField();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Slot"], {
        "data-slot": "form-control",
        id: formItemId,
        "aria-describedby": !error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`,
        "aria-invalid": !!error,
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 111,
        columnNumber: 5
    }, this);
}
function FormDescription({ className, ...props }) {
    const { formDescriptionId } = useFormField();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        "data-slot": "form-description",
        id: formDescriptionId,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 129,
        columnNumber: 5
    }, this);
}
function FormMessage({ className, ...props }) {
    const { error, formMessageId } = useFormField();
    const body = error ? String(error?.message ?? "") : props.children;
    if (!body) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        "data-slot": "form-message",
        id: formMessageId,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-destructive text-sm", className),
        ...props,
        children: body
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 147,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/data:a7ebef [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"60e9b11bd7b2db97e3c5aead25b07ef2124231c287":"updateCustomerPassword"},"app/(dashboard)/dashboard/customer/settings/actions.ts",""] */ __turbopack_context__.s({
    "updateCustomerPassword": (()=>updateCustomerPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var updateCustomerPassword = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("60e9b11bd7b2db97e3c5aead25b07ef2124231c287", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "updateCustomerPassword"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/lib/schemas/authSchemas.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "IndianMobileSchema": (()=>IndianMobileSchema),
    "NewPasswordConfirmationSchema": (()=>NewPasswordConfirmationSchema),
    "PasswordComplexitySchema": (()=>PasswordComplexitySchema),
    "PasswordConfirmationSchema": (()=>PasswordConfirmationSchema),
    "SingleNewPasswordSchema": (()=>SingleNewPasswordSchema),
    "SinglePasswordSchema": (()=>SinglePasswordSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-ssr] (ecmascript)");
;
const PasswordComplexitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().min(6, {
    message: "Password must be at least 6 characters"
}).regex(/[A-Z]/, {
    message: "Password must contain at least one capital letter"
}).regex(/[a-z]/, {
    message: "Password must contain at least one lowercase letter."
}) // Added lowercase check for consistency
.regex(/[0-9]/, {
    message: "Password must contain at least one number"
}).regex(/[^A-Za-z0-9]/, {
    message: "Password must contain at least one symbol"
});
const IndianMobileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().min(10, {
    message: "Mobile number must be at least 10 digits"
}).max(10, {
    message: "Mobile number must be exactly 10 digits"
}).regex(/^\d{10}$/, {
    message: "Please enter a valid 10-digit mobile number"
});
const PasswordConfirmationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    password: PasswordComplexitySchema,
    confirmPassword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string()
}).refine((data)=>data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: [
        "confirmPassword"
    ]
});
const NewPasswordConfirmationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    newPassword: PasswordComplexitySchema,
    confirmPassword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string()
}).refine((data)=>data.newPassword === data.confirmPassword, {
    message: "Passwords do not match.",
    path: [
        "confirmPassword"
    ]
});
const SinglePasswordSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    password: PasswordComplexitySchema
});
const SingleNewPasswordSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    password: PasswordComplexitySchema
});
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PasswordUpdateSection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$key$2d$round$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__KeyRound$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/key-round.js [app-ssr] (ecmascript) <export default as KeyRound>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/info.js [app-ssr] (ecmascript) <export default as Info>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$a7ebef__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/data:a7ebef [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/schemas/authSchemas.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
// Password schema
const PasswordSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    currentPassword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().min(1, "Current password is required"),
    newPassword: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PasswordComplexitySchema"],
    confirmPassword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().min(1, "Please confirm your password")
}).refine((data)=>data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: [
        "confirmPassword"
    ]
});
function PasswordUpdateSection({ registrationType }) {
    const isGoogleLogin = registrationType === 'google';
    const [isPending, startTransition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTransition"])();
    // Password Form
    const passwordForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(PasswordSchema),
        defaultValues: {
            currentPassword: "",
            newPassword: "",
            confirmPassword: ""
        }
    });
    // Handle password update
    const onPasswordSubmit = (data)=>{
        startTransition(async ()=>{
            try {
                // Create FormData object for the server action
                const formData = new FormData();
                formData.append('currentPassword', data.currentPassword);
                formData.append('newPassword', data.newPassword);
                // Call the server action with the required parameters
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$a7ebef__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["updateCustomerPassword"])({
                    message: null,
                    success: false
                }, formData);
                if (result.success) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Password updated successfully!");
                    passwordForm.reset();
                } else {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(result.message || "Failed to update password");
                    // Set server-side field errors into react-hook-form state
                    if (result.errors?.currentPassword) {
                        passwordForm.setError('currentPassword', {
                            type: 'server',
                            message: result.errors.currentPassword.join(', ')
                        });
                    }
                    if (result.errors?.newPassword) {
                        passwordForm.setError('newPassword', {
                            type: 'server',
                            message: result.errors.newPassword.join(', ')
                        });
                    }
                }
            } catch (error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("An unexpected error occurred");
                console.error(error);
            }
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.4,
            delay: 0.1
        },
        className: "rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 transition-all duration-300 hover:shadow-lg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-2 rounded-lg bg-primary/10 text-primary self-start",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$key$2d$round$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__KeyRound$3e$__["KeyRound"], {
                            className: "w-4 sm:w-5 h-4 sm:h-5"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                            lineNumber: 106,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100",
                                children: "Password"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                lineNumber: 109,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-neutral-500 dark:text-neutral-400 mt-0.5",
                                children: "Update your password"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                lineNumber: 112,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                        lineNumber: 108,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                lineNumber: 104,
                columnNumber: 7
            }, this),
            isGoogleLogin ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg border border-neutral-200 dark:border-neutral-700",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"], {
                            className: "w-4 h-4 text-amber-500"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                            lineNumber: 121,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-neutral-700 dark:text-neutral-300",
                            children: "You signed up with Google. Password management is handled by your Google account."
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                            lineNumber: 122,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                    lineNumber: 120,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                lineNumber: 119,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
                ...passwordForm,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: passwordForm.handleSubmit(onPasswordSubmit),
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormField"], {
                                control: passwordForm.control,
                                name: "currentPassword",
                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormItem"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormLabel"], {
                                                className: "text-sm text-neutral-700 dark:text-neutral-300",
                                                children: "Current Password"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 136,
                                                columnNumber: 21
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormControl"], {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                    type: "password",
                                                    placeholder: "••••••••",
                                                    ...field,
                                                    disabled: isPending,
                                                    className: "bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                    lineNumber: 140,
                                                    columnNumber: 23
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 139,
                                                columnNumber: 21
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormMessage"], {
                                                className: "text-xs text-red-500"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 148,
                                                columnNumber: 21
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                        lineNumber: 135,
                                        columnNumber: 19
                                    }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                lineNumber: 131,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormField"], {
                                control: passwordForm.control,
                                name: "newPassword",
                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormItem"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormLabel"], {
                                                className: "text-sm text-neutral-700 dark:text-neutral-300",
                                                children: "New Password"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 157,
                                                columnNumber: 21
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormControl"], {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                    type: "password",
                                                    placeholder: "••••••••",
                                                    ...field,
                                                    disabled: isPending,
                                                    className: "bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                    lineNumber: 161,
                                                    columnNumber: 23
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 160,
                                                columnNumber: 21
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormDescription"], {
                                                className: "text-xs text-neutral-500 dark:text-neutral-400",
                                                children: "Must contain: 6+ chars, 1 uppercase letter, 1 lowercase letter, 1 number, 1 symbol"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 169,
                                                columnNumber: 21
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormMessage"], {
                                                className: "text-xs text-red-500"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 172,
                                                columnNumber: 21
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                        lineNumber: 156,
                                        columnNumber: 19
                                    }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                lineNumber: 152,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormField"], {
                                control: passwordForm.control,
                                name: "confirmPassword",
                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormItem"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormLabel"], {
                                                className: "text-sm text-neutral-700 dark:text-neutral-300",
                                                children: "Confirm Password"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 181,
                                                columnNumber: 21
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormControl"], {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                    type: "password",
                                                    placeholder: "••••••••",
                                                    ...field,
                                                    disabled: isPending,
                                                    className: "bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                    lineNumber: 185,
                                                    columnNumber: 23
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 184,
                                                columnNumber: 21
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormMessage"], {
                                                className: "text-xs text-red-500"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                lineNumber: 193,
                                                columnNumber: 21
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                        lineNumber: 180,
                                        columnNumber: 19
                                    }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                lineNumber: 176,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-4 sm:mt-6 flex justify-end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative group",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300",
                                            style: {
                                                boxShadow: `inset 0 0 20px rgba(147, 51, 234, 0.2), 0 0 20px rgba(147, 51, 234, 0.3)`
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                            lineNumber: 200,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute -top-1 -right-1 w-6 h-6 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                            lineNumber: 208,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute -bottom-1 -left-1 w-4 h-4 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                            lineNumber: 209,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            type: "submit",
                                            disabled: isPending,
                                            variant: "outline",
                                            size: "sm",
                                            className: `
                      relative overflow-hidden rounded-xl p-3
                      bg-white dark:bg-black
                      border border-purple-200/50 dark:border-purple-700/50
                      shadow-purple-500/40 shadow-lg
                      hover:shadow-xl hover:shadow-purple-500/40
                      transition-all duration-300
                      text-purple-500 dark:text-purple-400
                      hover:bg-purple-500/5 dark:hover:bg-purple-500/10
                      text-xs sm:text-sm h-auto
                      ${isPending ? 'cursor-not-allowed opacity-80' : ''}
                    `,
                                            children: [
                                                isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                    className: "mr-2 h-4 w-4 animate-spin"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                                    lineNumber: 230,
                                                    columnNumber: 23
                                                }, this),
                                                "Change Password"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                            lineNumber: 211,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                    lineNumber: 198,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                                lineNumber: 197,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                        lineNumber: 130,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                    lineNumber: 129,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
                lineNumber: 128,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx",
        lineNumber: 98,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/data:87e6c4 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00dfe803aff9d8d25e084d9a6308a55508f64fa926":"deleteCustomerAccount"},"app/(dashboard)/dashboard/customer/settings/actions.ts",""] */ __turbopack_context__.s({
    "deleteCustomerAccount": (()=>deleteCustomerAccount)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var deleteCustomerAccount = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("00dfe803aff9d8d25e084d9a6308a55508f64fa926", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "deleteCustomerAccount"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/data:5c7f80 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00ab37fdd32a3f1c0c6e7c93bb4aa0ff21e15e406b":"checkDeleteAccountVerificationOptions"},"app/(dashboard)/dashboard/customer/settings/actions.ts",""] */ __turbopack_context__.s({
    "checkDeleteAccountVerificationOptions": (()=>checkDeleteAccountVerificationOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var checkDeleteAccountVerificationOptions = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("00ab37fdd32a3f1c0c6e7c93bb4aa0ff21e15e406b", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "checkDeleteAccountVerificationOptions"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/data:3829b0 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00fdacdd07757dbcf036da6c4c8196a99011b00235":"sendDeleteAccountOTP"},"app/(dashboard)/dashboard/customer/settings/actions.ts",""] */ __turbopack_context__.s({
    "sendDeleteAccountOTP": (()=>sendDeleteAccountOTP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var sendDeleteAccountOTP = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("00fdacdd07757dbcf036da6c4c8196a99011b00235", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "sendDeleteAccountOTP"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/data:678533 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"6020ad6d97dac089977bf4950f6387c7820894fb1d":"verifyDeleteAccountOTP"},"app/(dashboard)/dashboard/customer/settings/actions.ts",""] */ __turbopack_context__.s({
    "verifyDeleteAccountOTP": (()=>verifyDeleteAccountOTP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var verifyDeleteAccountOTP = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("6020ad6d97dac089977bf4950f6387c7820894fb1d", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "verifyDeleteAccountOTP"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/data:48810d [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40ee9c84d5b8fec27991395586b2b303c9b15a7da5":"verifyDeleteAccountPassword"},"app/(dashboard)/dashboard/customer/settings/actions.ts",""] */ __turbopack_context__.s({
    "verifyDeleteAccountPassword": (()=>verifyDeleteAccountPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var verifyDeleteAccountPassword = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("40ee9c84d5b8fec27991395586b2b303c9b15a7da5", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "verifyDeleteAccountPassword"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/components/ui/dialog.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Dialog": (()=>Dialog),
    "DialogClose": (()=>DialogClose),
    "DialogContent": (()=>DialogContent),
    "DialogDescription": (()=>DialogDescription),
    "DialogFooter": (()=>DialogFooter),
    "DialogHeader": (()=>DialogHeader),
    "DialogOverlay": (()=>DialogOverlay),
    "DialogPortal": (()=>DialogPortal),
    "DialogTitle": (()=>DialogTitle),
    "DialogTrigger": (()=>DialogTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-dialog/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__XIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as XIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
function Dialog({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "dialog",
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 12,
        columnNumber: 10
    }, this);
}
function DialogTrigger({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Trigger"], {
        "data-slot": "dialog-trigger",
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 18,
        columnNumber: 10
    }, this);
}
function DialogPortal({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Portal"], {
        "data-slot": "dialog-portal",
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 24,
        columnNumber: 10
    }, this);
}
function DialogClose({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Close"], {
        "data-slot": "dialog-close",
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 30,
        columnNumber: 10
    }, this);
}
function DialogOverlay({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Overlay"], {
        "data-slot": "dialog-overlay",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
function DialogContent({ className, children, hideClose = false, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(DialogPortal, {
        "data-slot": "dialog-portal",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(DialogOverlay, {}, void 0, false, {
                fileName: "[project]/components/ui/dialog.tsx",
                lineNumber: 61,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Content"], {
                "data-slot": "dialog-content",
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg", className),
                ...props,
                children: [
                    children,
                    !hideClose && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Close"], {
                        className: "ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__XIcon$3e$__["XIcon"], {}, void 0, false, {
                                fileName: "[project]/components/ui/dialog.tsx",
                                lineNumber: 73,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "sr-only",
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/components/ui/dialog.tsx",
                                lineNumber: 74,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ui/dialog.tsx",
                        lineNumber: 72,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/ui/dialog.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 60,
        columnNumber: 5
    }, this);
}
function DialogHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "dialog-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex flex-col gap-2 text-center sm:text-left", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 84,
        columnNumber: 5
    }, this);
}
function DialogFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "dialog-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 94,
        columnNumber: 5
    }, this);
}
function DialogTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Title"], {
        "data-slot": "dialog-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-lg leading-none font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 110,
        columnNumber: 5
    }, this);
}
function DialogDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Description"], {
        "data-slot": "dialog-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/dialog.tsx",
        lineNumber: 123,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/components/ui/input-otp.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InputOTP": (()=>InputOTP),
    "InputOTPGroup": (()=>InputOTPGroup),
    "InputOTPSeparator": (()=>InputOTPSeparator),
    "InputOTPSlot": (()=>InputOTPSlot)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$input$2d$otp$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/input-otp/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MinusIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/minus.js [app-ssr] (ecmascript) <export default as MinusIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
function InputOTP({ className, containerClassName, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$input$2d$otp$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OTPInput"], {
        "data-slot": "input-otp",
        containerClassName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center gap-2 has-disabled:opacity-50", containerClassName),
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("disabled:cursor-not-allowed", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/input-otp.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
}
function InputOTPGroup({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "input-otp-group",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/input-otp.tsx",
        lineNumber: 31,
        columnNumber: 5
    }, this);
}
function InputOTPSlot({ index, className, ...props }) {
    const inputOTPContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$input$2d$otp$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OTPInputContext"]);
    const { char, hasFakeCaret, isActive } = inputOTPContext?.slots[index] ?? {};
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "input-otp-slot",
        "data-active": isActive,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]", className),
        ...props,
        children: [
            char,
            hasFakeCaret && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "pointer-events-none absolute inset-0 flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-caret-blink bg-foreground h-4 w-px duration-1000"
                }, void 0, false, {
                    fileName: "[project]/components/ui/input-otp.tsx",
                    lineNumber: 62,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/ui/input-otp.tsx",
                lineNumber: 61,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/input-otp.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this);
}
function InputOTPSeparator({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "input-otp-separator",
        role: "separator",
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MinusIcon$3e$__["MinusIcon"], {}, void 0, false, {
            fileName: "[project]/components/ui/input-otp.tsx",
            lineNumber: 72,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/input-otp.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DeleteAccountSection": (()=>DeleteAccountSection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$87e6c4__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/data:87e6c4 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$5c7f80__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/data:5c7f80 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$3829b0__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/data:3829b0 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$678533__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/data:678533 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$48810d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/data:48810d [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/dialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input-otp.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-ssr] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-ssr] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/mail.js [app-ssr] (ecmascript) <export default as Mail>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/smartphone.js [app-ssr] (ecmascript) <export default as Smartphone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-x.js [app-ssr] (ecmascript) <export default as XCircle>");
'use client';
;
;
;
;
;
;
;
;
;
;
;
function DeleteAccountSection() {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [_isPending, startTransition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTransition"])();
    const [deleteConfirmText, setDeleteConfirmText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [isDeleting, setIsDeleting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isDialogOpen, setIsDialogOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Enhanced security state
    const [verificationStep, setVerificationStep] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('initial');
    const [hasEmail, setHasEmail] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasPhone, setHasPhone] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [userEmail, setUserEmail] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [_selectedMethod, setSelectedMethod] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [otp, setOtp] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [password, setPassword] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [isVerifying, setIsVerifying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isSendingOTP, setIsSendingOTP] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isVerified, setIsVerified] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isCheckingOptions, setIsCheckingOptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Check verification options when dialog opens
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isDialogOpen && verificationStep === 'initial') {
            checkVerificationOptions();
        }
    }, [
        isDialogOpen,
        verificationStep
    ]);
    const checkVerificationOptions = async ()=>{
        setIsCheckingOptions(true);
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$5c7f80__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["checkDeleteAccountVerificationOptions"])();
            if (result.success) {
                setHasEmail(result.hasEmail);
                setHasPhone(result.hasPhone);
                // Determine next step based on available options
                if (result.hasEmail && result.hasPhone) {
                    setVerificationStep('choose-method');
                } else if (result.hasEmail) {
                    setSelectedMethod('email');
                    setVerificationStep('email-otp');
                } else if (result.hasPhone) {
                    setSelectedMethod('password');
                    setVerificationStep('password');
                } else {
                    // No email or phone - proceed with just DELETE confirmation
                    setVerificationStep('final-confirm');
                }
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(result.message || 'Failed to check verification options');
            }
        } catch (_error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('An error occurred while checking verification options');
        } finally{
            setIsCheckingOptions(false);
        }
    };
    const handleMethodSelection = (method)=>{
        setSelectedMethod(method);
        if (method === 'email') {
            setVerificationStep('email-otp');
        } else {
            setVerificationStep('password');
        }
    };
    const handleSendOTP = async ()=>{
        setIsSendingOTP(true);
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$3829b0__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["sendDeleteAccountOTP"])();
            if (result.success) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(result.message);
                if (result.email) {
                    setUserEmail(result.email);
                }
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(result.message);
                if (result.isConfigurationError) {
                    // Don't proceed if configuration error
                    return;
                }
            }
        } catch (_error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Failed to send verification code');
        } finally{
            setIsSendingOTP(false);
        }
    };
    const handleVerifyOTP = async ()=>{
        if (otp.length !== 6) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Please enter a valid 6-digit code');
            return;
        }
        setIsVerifying(true);
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$678533__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["verifyDeleteAccountOTP"])(userEmail, otp);
            if (result.success) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(result.message);
                setIsVerified(true);
                setVerificationStep('final-confirm');
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(result.message);
            }
        } catch (_error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Failed to verify code');
        } finally{
            setIsVerifying(false);
        }
    };
    const handleVerifyPassword = async ()=>{
        if (!password.trim()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Please enter your password');
            return;
        }
        setIsVerifying(true);
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$48810d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["verifyDeleteAccountPassword"])(password);
            if (result.success) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(result.message);
                setIsVerified(true);
                setVerificationStep('final-confirm');
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(result.message);
            }
        } catch (_error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Failed to verify password');
        } finally{
            setIsVerifying(false);
        }
    };
    // Handle final account deletion
    const handleDeleteAccount = (e)=>{
        // Prevent the default action which would close the dialog
        e.preventDefault();
        if (deleteConfirmText !== "DELETE") {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Please type "DELETE" to confirm');
            return;
        }
        // Check if verification is required and completed
        if ((hasEmail || hasPhone) && !isVerified) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Please complete verification first');
            return;
        }
        setIsDeleting(true);
        startTransition(async ()=>{
            try {
                // Show a toast before making the request
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Processing account deletion...");
                // Wrap the deletion in a try-catch to handle potential errors
                try {
                    const _result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$data$3a$87e6c4__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["deleteCustomerAccount"])();
                    // If we get here, the account was successfully deleted
                    // The server action already signs out the user, so we just need to redirect
                    resetDialogState();
                    router.push("/");
                } catch (error) {
                    // Check if the error is because the response is undefined (which happens after successful deletion)
                    if (error instanceof Error && error.message.includes("Cannot read properties of undefined")) {
                        // This likely means the account was deleted successfully but we lost the connection
                        // because the user was signed out
                        resetDialogState();
                        router.push("/");
                    } else {
                        // This is a real error
                        console.error("Error in account deletion:", error);
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to delete account");
                        setIsDeleting(false);
                    }
                }
            } catch (outerError) {
                // Handle any other errors that might occur
                console.error("Unexpected error during account deletion:", outerError);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("An unexpected error occurred");
                setIsDeleting(false);
            }
        });
    };
    const resetDialogState = ()=>{
        setDeleteConfirmText("");
        setIsDialogOpen(false);
        setVerificationStep('initial');
        setSelectedMethod(null);
        setOtp('');
        setPassword('');
        setIsVerified(false);
        setUserEmail('');
        setIsCheckingOptions(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.4,
            delay: 0.2
        },
        className: "rounded-lg border border-red-200 dark:border-red-800/30 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 transition-all duration-300 hover:shadow-lg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-red-100 dark:border-red-800/30",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-2 rounded-lg bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 self-start",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                            className: "w-4 sm:w-5 h-4 sm:h-5"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                            lineNumber: 243,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                        lineNumber: 242,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-base sm:text-lg font-semibold text-red-600 dark:text-red-400",
                                children: "Delete Account"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                lineNumber: 246,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-red-500/70 dark:text-red-400/70 mt-0.5",
                                children: "Permanently delete your account and all associated data"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                lineNumber: 249,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                        lineNumber: 245,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                lineNumber: 241,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30 mb-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start gap-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                            className: "w-5 h-5 text-red-500 mt-0.5"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                            lineNumber: 257,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm font-medium text-red-700 dark:text-red-400",
                                    children: "Warning: This action cannot be undone"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 259,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs text-red-600/80 dark:text-red-400/80 mt-1",
                                    children: "Deleting your account will permanently remove all your data, including your saved cards, likes, reviews, and subscriptions. You will not be able to recover this information."
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 262,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                            lineNumber: 258,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                    lineNumber: 256,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                lineNumber: 255,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                whileHover: {
                    scale: 1.02
                },
                whileTap: {
                    scale: 0.98
                },
                className: "w-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    variant: "destructive",
                    disabled: isDeleting,
                    onClick: ()=>setIsDialogOpen(true),
                    className: `
            relative overflow-hidden
            bg-gradient-to-r from-red-500 to-red-600
            hover:from-red-600 hover:to-red-700
            text-white font-medium
            shadow-lg hover:shadow-xl
            transition-all duration-300
            before:absolute before:inset-0
            before:bg-gradient-to-r before:from-red-400 before:to-red-500
            before:opacity-0 hover:before:opacity-20
            before:transition-opacity before:duration-300
            ${isDeleting ? 'cursor-not-allowed opacity-80' : ''}
          `,
                    style: {
                        boxShadow: isDeleting ? '0 4px 20px rgba(239, 68, 68, 0.3)' : '0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)'
                    },
                    type: "button",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                            className: "h-4 w-4 mr-2"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                            lineNumber: 299,
                            columnNumber: 11
                        }, this),
                        "Delete Account"
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                    lineNumber: 275,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                lineNumber: 270,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Dialog"], {
                open: isDialogOpen,
                onOpenChange: (_open)=>{
                    // Only allow closing the dialog if we're not in the deleting state
                    if (!isDeleting && !isVerifying && !isSendingOTP) {
                        resetDialogState();
                    }
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogContent"], {
                    className: "sm:max-w-md",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogHeader"], {
                            className: "text-center pb-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            scale: 0
                                        },
                                        animate: {
                                            scale: 1
                                        },
                                        transition: {
                                            delay: 0.1,
                                            type: "spring",
                                            stiffness: 200
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                            className: "h-8 w-8 text-red-500"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 318,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                        lineNumber: 313,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 312,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogTitle"], {
                                    className: "text-xl font-semibold text-gray-900 dark:text-gray-100",
                                    children: verificationStep === 'choose-method' ? 'Verify Your Identity' : verificationStep === 'email-otp' ? 'Email Verification' : verificationStep === 'password' ? 'Password Verification' : 'Delete your account?'
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 321,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogDescription"], {
                                    className: "text-gray-500 dark:text-gray-400 mt-2",
                                    children: verificationStep === 'choose-method' ? 'Choose how you want to verify your identity before deleting your account.' : verificationStep === 'email-otp' ? 'We\'ve sent a verification code to your email address.' : verificationStep === 'password' ? 'Please enter your current password to verify your identity.' : 'This action cannot be undone. All your data will be permanently removed.'
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 327,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                            lineNumber: 311,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-4",
                            children: [
                                isCheckingOptions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col items-center justify-center py-8 space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                            className: "h-8 w-8 animate-spin text-neutral-500"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 339,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-neutral-600 dark:text-neutral-400",
                                            children: "Checking verification options..."
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 340,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 338,
                                    columnNumber: 15
                                }, this),
                                !isCheckingOptions && verificationStep === 'choose-method' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-neutral-600 dark:text-neutral-400 mb-4",
                                            children: "For security, please verify your identity before proceeding:"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 349,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                    onClick: ()=>handleMethodSelection('email'),
                                                    variant: "outline",
                                                    className: "w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__["Mail"], {
                                                            className: "h-5 w-5 text-blue-500"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                            lineNumber: 359,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-left",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "font-medium",
                                                                    children: "Email Verification"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 361,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-xs text-neutral-500",
                                                                    children: "Send OTP to your email"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 362,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                            lineNumber: 360,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                    lineNumber: 354,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                    onClick: ()=>handleMethodSelection('password'),
                                                    variant: "outline",
                                                    className: "w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__["Smartphone"], {
                                                            className: "h-5 w-5 text-green-500"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                            lineNumber: 371,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-left",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "font-medium",
                                                                    children: "Password Verification"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 373,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-xs text-neutral-500",
                                                                    children: "Enter your current password"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 374,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                            lineNumber: 372,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                    lineNumber: 366,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 353,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 348,
                                    columnNumber: 15
                                }, this),
                                !isCheckingOptions && verificationStep === 'email-otp' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                onClick: handleSendOTP,
                                                disabled: isSendingOTP,
                                                variant: "outline",
                                                className: "mb-4",
                                                children: isSendingOTP ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                            className: "h-4 w-4 animate-spin mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                            lineNumber: 393,
                                                            columnNumber: 25
                                                        }, this),
                                                        "Sending..."
                                                    ]
                                                }, void 0, true) : 'Send Verification Code'
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                lineNumber: 385,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 384,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    className: "text-sm font-medium text-neutral-700 dark:text-neutral-300",
                                                    children: "Enter 6-digit verification code:"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                    lineNumber: 403,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex justify-center",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTP"], {
                                                        maxLength: 6,
                                                        value: otp,
                                                        onChange: setOtp,
                                                        className: "gap-2",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPGroup"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                                    index: 0,
                                                                    className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 414,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                                    index: 1,
                                                                    className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 415,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                                    index: 2,
                                                                    className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 416,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                                    index: 3,
                                                                    className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 417,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                                    index: 4,
                                                                    className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 418,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                                    index: 5,
                                                                    className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                    lineNumber: 419,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                            lineNumber: 413,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                        lineNumber: 407,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                    lineNumber: 406,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 402,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            onClick: handleVerifyOTP,
                                            disabled: otp.length !== 6 || isVerifying,
                                            className: "w-full",
                                            children: isVerifying ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "h-4 w-4 animate-spin mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                        lineNumber: 432,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Verifying..."
                                                ]
                                            }, void 0, true) : 'Verify Code'
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 425,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 383,
                                    columnNumber: 15
                                }, this),
                                !isCheckingOptions && verificationStep === 'password' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    className: "text-sm font-medium text-neutral-700 dark:text-neutral-300",
                                                    children: "Enter your current password:"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                    lineNumber: 446,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                    type: "password",
                                                    value: password,
                                                    onChange: (e)=>setPassword(e.target.value),
                                                    placeholder: "Current password",
                                                    className: "bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                    lineNumber: 449,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 445,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            onClick: handleVerifyPassword,
                                            disabled: !password.trim() || isVerifying,
                                            className: "w-full",
                                            children: isVerifying ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "h-4 w-4 animate-spin mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                        lineNumber: 465,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Verifying..."
                                                ]
                                            }, void 0, true) : 'Verify Password'
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 458,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 444,
                                    columnNumber: 15
                                }, this),
                                !isCheckingOptions && verificationStep === 'final-confirm' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-red-50 dark:bg-red-950/20 rounded-lg p-4 border border-red-100 dark:border-red-800/30",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-start gap-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                                        className: "h-5 w-5 text-red-500 mt-0.5 flex-shrink-0"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                        lineNumber: 480,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm font-medium text-red-700 dark:text-red-400",
                                                                children: "This will permanently delete:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                lineNumber: 482,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                className: "text-xs text-red-600/80 dark:text-red-400/80 mt-1 space-y-0.5",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                        children: "• Your saved business cards"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                        lineNumber: 486,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                        children: "• Your likes and subscriptions"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                        lineNumber: 487,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                        children: "• Your reviews and ratings"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                        lineNumber: 488,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                        children: "• Your account information"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                        lineNumber: 489,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                                lineNumber: 485,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                        lineNumber: 481,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                lineNumber: 479,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 478,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2",
                                                    children: [
                                                        "Type ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "font-bold text-red-500 dark:text-red-400",
                                                            children: "DELETE"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                            lineNumber: 497,
                                                            columnNumber: 26
                                                        }, this),
                                                        " to confirm:"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                    lineNumber: 496,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                    value: deleteConfirmText,
                                                    onChange: (e)=>setDeleteConfirmText(e.target.value),
                                                    placeholder: "DELETE",
                                                    className: "bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 focus-visible:ring-red-500/30",
                                                    disabled: isDeleting
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                    lineNumber: 499,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 495,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                            lineNumber: 335,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DialogFooter"], {
                            className: "flex flex-col-reverse sm:flex-row gap-3 pt-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "button",
                                    variant: "outline",
                                    onClick: ()=>{
                                        resetDialogState();
                                    },
                                    disabled: isDeleting || isVerifying || isSendingOTP || isCheckingOptions,
                                    className: "flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__["XCircle"], {
                                            className: "mr-2 h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 521,
                                            columnNumber: 15
                                        }, this),
                                        "Cancel"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 512,
                                    columnNumber: 13
                                }, this),
                                !isCheckingOptions && verificationStep === 'final-confirm' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                    whileHover: {
                                        scale: 1.02
                                    },
                                    whileTap: {
                                        scale: 0.98
                                    },
                                    className: "flex-1",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                        type: "button",
                                        onClick: handleDeleteAccount,
                                        disabled: deleteConfirmText !== "DELETE" || isDeleting,
                                        className: `
                    w-full relative overflow-hidden
                    bg-gradient-to-r from-red-500 to-red-600
                    hover:from-red-600 hover:to-red-700
                    text-white font-medium
                    shadow-lg hover:shadow-xl
                    transition-all duration-300
                    before:absolute before:inset-0
                    before:bg-gradient-to-r before:from-red-400 before:to-red-500
                    before:opacity-0 hover:before:opacity-20
                    before:transition-opacity before:duration-300
                    ${deleteConfirmText !== "DELETE" || isDeleting ? 'cursor-not-allowed opacity-80' : ''}
                  `,
                                        style: {
                                            boxShadow: deleteConfirmText !== "DELETE" || isDeleting ? '0 4px 20px rgba(239, 68, 68, 0.3)' : '0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)'
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                                            mode: "wait",
                                            children: isDeleting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                                initial: {
                                                    opacity: 0,
                                                    x: -10
                                                },
                                                animate: {
                                                    opacity: 1,
                                                    x: 0
                                                },
                                                exit: {
                                                    opacity: 0,
                                                    x: 10
                                                },
                                                className: "flex items-center justify-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "h-4 w-4 mr-2 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                        lineNumber: 564,
                                                        columnNumber: 25
                                                    }, this),
                                                    "Deleting..."
                                                ]
                                            }, "deleting", true, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                lineNumber: 557,
                                                columnNumber: 23
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                                initial: {
                                                    opacity: 0,
                                                    x: -10
                                                },
                                                animate: {
                                                    opacity: 1,
                                                    x: 0
                                                },
                                                exit: {
                                                    opacity: 0,
                                                    x: 10
                                                },
                                                className: "flex items-center justify-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                        className: "h-4 w-4 mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                        lineNumber: 575,
                                                        columnNumber: 25
                                                    }, this),
                                                    "Delete Account"
                                                ]
                                            }, "delete", true, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                                lineNumber: 568,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                            lineNumber: 555,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                        lineNumber: 532,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                                    lineNumber: 527,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                            lineNumber: 511,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                    lineNumber: 310,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
                lineNumber: 304,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx",
        lineNumber: 235,
        columnNumber: 5
    }, this);
}
}}),
"[project]/components/ui/card.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": (()=>Card),
    "CardAction": (()=>CardAction),
    "CardContent": (()=>CardContent),
    "CardDescription": (()=>CardDescription),
    "CardFooter": (()=>CardFooter),
    "CardHeader": (()=>CardHeader),
    "CardTitle": (()=>CardTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
;
;
function Card({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
function CardHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
function CardTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("leading-none font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
function CardDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
function CardAction({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-action",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("col-start-2 row-span-2 row-start-1 self-start justify-self-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
function CardContent({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-content",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("px-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
}
function CardFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center px-6 [.border-t]:pt-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/data:2a8c84 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"60bcf908d98036dc36ecb2e02892a415d912237e27":"linkCustomerEmail"},"app/(dashboard)/dashboard/customer/settings/actions.ts",""] */ __turbopack_context__.s({
    "linkCustomerEmail": (()=>linkCustomerEmail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var linkCustomerEmail = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("60bcf908d98036dc36ecb2e02892a415d912237e27", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "linkCustomerEmail"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/data:4bda0a [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"60a0f0d7673472711bcf5d745034f216e9743862e6":"verifyEmailOTP"},"app/(dashboard)/dashboard/customer/settings/actions.ts",""] */ __turbopack_context__.s({
    "verifyEmailOTP": (()=>verifyEmailOTP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var verifyEmailOTP = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("60a0f0d7673472711bcf5d745034f216e9743862e6", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "verifyEmailOTP"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/components/LinkEmailSection.tsx [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const e = new Error(`Could not parse module '[project]/app/(dashboard)/dashboard/customer/settings/components/LinkEmailSection.tsx'

Unexpected token `Card`. Expected jsx identifier`);
e.code = 'MODULE_UNPARSEABLE';
throw e;}}),
"[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LinkPhoneSection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/phone.js [app-ssr] (ecmascript) <export default as Phone>");
"use client";
;
;
;
function LinkPhoneSection({ currentPhone }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
        className: "border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                className: "pb-4 border-b border-neutral-100 dark:border-neutral-800",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                        className: "flex items-center gap-2 text-lg font-semibold text-neutral-800 dark:text-neutral-100",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-2 rounded-lg bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                                    lineNumber: 21,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                                lineNumber: 20,
                                columnNumber: 11
                            }, this),
                            "Phone Number"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                        lineNumber: 19,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-neutral-600 dark:text-neutral-400 mt-2",
                        children: currentPhone ? "Your current phone number linked to this account." : "No phone number is currently linked to your account."
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                        lineNumber: 25,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "pt-4",
                children: currentPhone ? // Show current phone number (read-only)
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "text-sm font-medium text-neutral-700 dark:text-neutral-300",
                                children: "Current Phone Number"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                                lineNumber: 37,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm text-neutral-600 dark:text-neutral-400",
                                    children: currentPhone
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                                    lineNumber: 41,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                                lineNumber: 40,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-neutral-500 dark:text-neutral-400 mt-1",
                                children: "Phone number changes are not currently supported. Contact support if you need to update your number."
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                                lineNumber: 43,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                        lineNumber: 36,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                    lineNumber: 35,
                    columnNumber: 11
                }, this) : // No phone number linked
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center p-6 rounded-lg bg-neutral-50 dark:bg-neutral-900/50 border border-neutral-200 dark:border-neutral-700",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-3 rounded-full bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400 mx-auto mb-3 w-fit",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"], {
                                className: "w-6 h-6"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                                lineNumber: 52,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                            lineNumber: 51,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-2",
                            children: "No Phone Number"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                            lineNumber: 54,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-neutral-600 dark:text-neutral-400 max-w-sm mx-auto",
                            children: "No phone number is currently linked to your account. Phone number linking is not available at this time."
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                            lineNumber: 57,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                    lineNumber: 50,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SettingsPageClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-ssr] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$PasswordUpdateSection$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/components/PasswordUpdateSection.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$DeleteAccountSection$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/DeleteAccountSection.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$LinkEmailSection$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/components/LinkEmailSection.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$LinkPhoneSection$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/settings/components/LinkPhoneSection.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
function SettingsPageClient({ currentEmail, currentPhone, registrationType }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-3 rounded-xl bg-muted hidden sm:block",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                            className: "w-6 h-6 text-foreground"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                            lineNumber: 26,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                        lineNumber: 25,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-foreground",
                                children: "Account Settings"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                                lineNumber: 29,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground mt-1",
                                children: "Manage your account security and preferences"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                                lineNumber: 32,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                        lineNumber: 28,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$LinkEmailSection$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        currentEmail: currentEmail,
                        currentPhone: currentPhone,
                        registrationType: registrationType
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                        lineNumber: 41,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$LinkPhoneSection$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        currentEmail: currentEmail,
                        currentPhone: currentPhone,
                        registrationType: registrationType
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                        lineNumber: 48,
                        columnNumber: 9
                    }, this),
                    registrationType !== 'google' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$components$2f$PasswordUpdateSection$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        registrationType: registrationType
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                        lineNumber: 56,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$settings$2f$DeleteAccountSection$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DeleteAccountSection"], {}, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                        lineNumber: 62,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
                lineNumber: 39,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/settings/components/SettingsPageClient.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=_8807198e._.js.map