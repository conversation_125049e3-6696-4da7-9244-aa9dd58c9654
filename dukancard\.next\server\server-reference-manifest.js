self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00a78b43259bdfa35946a0918da66b9382dcd7b4dc\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/dashboard/customer/settings/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": \"action-browser\",\n        \"app/(dashboard)/dashboard/customer/settings/page\": \"action-browser\"\n      }\n    },\n    \"60e2048999976108d182f440e74ddcd263930eb412\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": \"action-browser\"\n      }\n    },\n    \"400412f1eb89dd7bc7c1bba76428244b575e3acba6\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": \"action-browser\"\n      }\n    },\n    \"40ebef699b6761cfd539429fec1e4d0a90ae48b158\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": \"action-browser\"\n      }\n    },\n    \"60606984ed763a79a21c8467f3859b77a5c30c66eb\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": \"action-browser\"\n      }\n    },\n    \"601efcd933277679be074bdf16199352e0f1ee1dd3\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": \"action-browser\"\n      }\n    },\n    \"40ab9ff6341449bb46121f282a1e253cc89e3417db\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/profile/page\": \"action-browser\"\n      }\n    },\n    \"60e9b11bd7b2db97e3c5aead25b07ef2124231c287\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": \"action-browser\"\n      }\n    },\n    \"00dfe803aff9d8d25e084d9a6308a55508f64fa926\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": \"action-browser\"\n      }\n    },\n    \"00ab37fdd32a3f1c0c6e7c93bb4aa0ff21e15e406b\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": \"action-browser\"\n      }\n    },\n    \"00fdacdd07757dbcf036da6c4c8196a99011b00235\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": \"action-browser\"\n      }\n    },\n    \"6020ad6d97dac089977bf4950f6387c7820894fb1d\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": \"action-browser\"\n      }\n    },\n    \"40ee9c84d5b8fec27991395586b2b303c9b15a7da5\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": \"action-browser\"\n      }\n    },\n    \"60bcf908d98036dc36ecb2e02892a415d912237e27\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": \"action-browser\"\n      }\n    },\n    \"60a0f0d7673472711bcf5d745034f216e9743862e6\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/settings/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(dashboard)/dashboard/customer/settings/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/customer/settings/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"PmcKF30SqCPkNYQmqp/KEb/JOOZXtLlhvlBDAdzzX24=\"\n}"