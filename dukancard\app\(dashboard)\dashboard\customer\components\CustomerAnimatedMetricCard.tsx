"use client";

import { LucideIcon } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

interface CustomerAnimatedMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description: string;
  color: "blue" | "indigo" | "purple" | "rose" | "red" | "yellow" | "brand";
  href?: string;
}

export default function CustomerAnimatedMetricCard({
  title,
  value,
  icon: Icon,
  description,
  color,
  href,
}: CustomerAnimatedMetricCardProps) {
  // Simple, clean design without colors or hover effects
  const iconColors = {
    blue: "text-blue-600 dark:text-blue-400",
    indigo: "text-indigo-600 dark:text-indigo-400",
    purple: "text-purple-600 dark:text-purple-400",
    rose: "text-rose-600 dark:text-rose-400",
    red: "text-red-600 dark:text-red-400",
    yellow: "text-yellow-600 dark:text-yellow-400",
    brand: "text-amber-600 dark:text-amber-400",
  };

  const iconColor = iconColors[color];

  const cardContent = (
    <>
      {/* Content */}
      <div className="flex flex-col items-center text-center space-y-4">
        {/* Icon */}
        <div className="p-3 rounded-xl bg-muted">
          <Icon className={`w-6 h-6 ${iconColor}`} />
        </div>

        {/* Value */}
        <div className="space-y-1">
          <div className="text-2xl font-bold text-foreground">
            {value}
          </div>
          <div className="text-sm font-medium text-muted-foreground">
            {title}
          </div>
        </div>

        {/* Description */}
        <p className="text-xs text-muted-foreground">
          {description}
        </p>

        {/* Interactive Button - only show for cards with href */}
        {href && (
          <div className="mt-2">
            <Button
              asChild
              variant="outline"
              size="sm"
              className="w-full text-xs font-medium"
            >
              <Link href={href}>
                View {title}
              </Link>
            </Button>
          </div>
        )}
      </div>
    </>
  );

  return (
    <div className="rounded-xl p-6 bg-card border border-border">
      {cardContent}
    </div>
  );
}
