"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, Loader2, Link, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { linkCustomerEmail, verifyEmailOTP } from "../actions";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";

// Email form schema
const EmailSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

// OTP form schema
const OTPSchema = z.object({
  otp: z.string().min(6, { message: "OTP must be 6 digits" }).max(6, { message: "OTP must be 6 digits" }),
});

interface LinkEmailSectionProps {
  currentEmail?: string | null | undefined;
  currentPhone?: string | null | undefined;
  registrationType: 'google' | 'email' | 'phone';
}

export default function LinkEmailSection({
  currentEmail,
  currentPhone: _currentPhone,
  registrationType
}: LinkEmailSectionProps) {
  const [isPending, startTransition] = useTransition();
  const [message, setMessage] = useState<string | null>(null);
  const [step, setStep] = useState<'email' | 'otp'>('email');
  const [emailForOTP, setEmailForOTP] = useState<string>('');

  const emailForm = useForm<z.infer<typeof EmailSchema>>({
    resolver: zodResolver(EmailSchema),
    defaultValues: {
      email: registrationType === 'email' ? (currentEmail || "") : "",
    },
  });

  const otpForm = useForm<z.infer<typeof OTPSchema>>({
    resolver: zodResolver(OTPSchema),
    defaultValues: {
      otp: "",
    },
  });

  // Handle email linking
  const onEmailSubmit = (data: z.infer<typeof EmailSchema>) => {
    startTransition(async () => {
      try {
        // Create FormData object for the server action
        const formData = new FormData();
        formData.append('email', data.email);

        // Call the server action
        const result = await linkCustomerEmail(
          { message: null, success: false }, // Initial state
          formData
        );

        if (result.success) {
          if (result.message === 'otp_sent') {
            // Phone user with existing email - show OTP step
            setEmailForOTP(data.email);
            setStep('otp');
            setMessage("We've sent a 6-digit verification code to your email address.");
            toast.success("Verification code sent!");
          } else if (result.message === 'Email address linked successfully!') {
            // User with no email - direct linking successful
            toast.success("Email linked successfully!");
            setMessage("Email address has been linked to your account.");
            emailForm.reset();
            // Optionally refresh the page or update parent component state
            window.location.reload();
          } else {
            // Email user - email change flow
            toast.success("Verification email sent!");
            setMessage(result.message || "Please check your email for the verification link.");
            emailForm.reset();
          }
        } else {
          toast.error(result.message || "Failed to send verification email");
          setMessage(result.message || "Failed to send verification email");
        }
      } catch (error) {
        toast.error("An unexpected error occurred");
        console.error(error);
      }
    });
  };

  // Handle OTP verification
  const onOTPSubmit = (data: z.infer<typeof OTPSchema>) => {
    startTransition(async () => {
      try {
        // Create FormData object for the server action
        const formData = new FormData();
        formData.append('email', emailForOTP);
        formData.append('otp', data.otp);

        // Call the server action
        const result = await verifyEmailOTP(
          { message: null, success: false }, // Initial state
          formData
        );

        if (result.success) {
          toast.success("Email linked successfully!");
          setMessage("Email address has been linked to your account.");
          setStep('email');
          setEmailForOTP('');
          emailForm.reset();
          otpForm.reset();
        } else {
          toast.error(result.message || "Failed to verify code");
          setMessage(result.message || "Failed to verify code");
        }
      } catch (error) {
        toast.error("An unexpected error occurred");
        console.error(error);
      }
    });
  };

  // Handle back to email step
  const onBackToEmail = () => {
    setStep('email');
    setEmailForOTP('');
    setMessage(null);
    otpForm.reset();
  };

  // Determine the behavior based on registration type
  const isGoogleUser = registrationType === 'google';
  const isEmailUser = registrationType === 'email';
  const _isPhoneUser = registrationType === 'phone';

  return (
    <Card className="border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black">
      <CardHeader className="pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <CardTitle className="flex items-center gap-2 text-lg font-semibold text-neutral-800 dark:text-neutral-100">
          <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
            <Mail className="w-4 h-4" />
          </div>
          {isGoogleUser ? "Email Address (Google)" : isEmailUser ? "Update Email Address" : "Link Email Address"}
        </CardTitle>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-2">
          {isGoogleUser
            ? "Your email is linked to your Google account and cannot be changed here."
            : isEmailUser
            ? "Update your email address. We'll send OTP verification to both old and new email addresses."
            : "Add an email address to your account for additional login options and notifications."
          }
        </p>
      </CardHeader>
      <CardContent className="pt-4">
        {message && (
          <div className="mb-4 p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">{message}</span>
            </div>
          </div>
        )}

        {isGoogleUser ? (
          // Google users - show email as read-only
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                Email Address
              </label>
              <div className="mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md">
                <span className="text-sm text-neutral-600 dark:text-neutral-400">{currentEmail}</span>
              </div>
              <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                This email is managed by your Google account.
              </p>
            </div>
          </div>
        ) : step === 'email' ? (
          // Email step - show email form
          <Form {...emailForm}>
            <form onSubmit={emailForm.handleSubmit(onEmailSubmit)}>
              <div className="space-y-4">
                <FormField
                  control={emailForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm text-neutral-700 dark:text-neutral-300">
                        Email Address
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                          disabled={isPending}
                          className="bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                        />
                      </FormControl>
                      <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400">
                        {isEmailUser
                          ? "We'll send verification codes to both your current and new email addresses."
                          : "We'll send a verification code to this email address."
                        }
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end">
                    <Button
                      type="submit"
                      disabled={isPending || !emailForm.formState.isValid}
                      className="bg-primary hover:bg-primary/90 text-primary-foreground"
                    >
                      {isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Sending Verification...
                        </>
                      ) : (
                        <>
                          <Link className="w-4 h-4 mr-2" />
                          {isEmailUser ? "Update Email" : "Link Email Address"}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        ) : (
          // OTP step - show OTP form
          <Form {...otpForm}>
            <form onSubmit={otpForm.handleSubmit(onOTPSubmit)}>
              <div className="space-y-4">
                <div className="text-center mb-4">
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    We&apos;ve sent a 6-digit code to
                  </p>
                  <p className="text-sm font-medium text-neutral-800 dark:text-neutral-200">{emailForOTP}</p>
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-2">
                    Code expires in 24 hours
                  </p>
                </div>

                <FormField
                  control={otpForm.control}
                  name="otp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm text-neutral-700 dark:text-neutral-300 text-center block">
                        Enter Verification Code
                      </FormLabel>
                      <FormControl>
                        <div className="flex justify-center">
                          <InputOTP
                            maxLength={6}
                            {...field}
                            className="gap-2"
                          >
                            <InputOTPGroup>
                              <InputOTPSlot
                                index={0}
                                className="w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400"
                              />
                              <InputOTPSlot
                                index={1}
                                className="w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400"
                              />
                              <InputOTPSlot
                                index={2}
                                className="w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400"
                              />
                              <InputOTPSlot
                                index={3}
                                className="w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400"
                              />
                              <InputOTPSlot
                                index={4}
                                className="w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400"
                              />
                              <InputOTPSlot
                                index={5}
                                className="w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400"
                              />
                            </InputOTPGroup>
                          </InputOTP>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={onBackToEmail}
                    disabled={isPending}
                    className="text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200"
                  >
                    ← Back to Email
                  </Button>

                  <Button
                      type="submit"
                      disabled={isPending || !otpForm.formState.isValid}
                      className="bg-primary hover:bg-primary/90 text-primary-foreground"
                    >
                      {isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Verifying...
                        </>
                      ) : (
                        <>
                          <Link className="w-4 h-4 mr-2" />
                          Verify & Link Email
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
