'use client';

import React, { useRef, useState, useTransition } from 'react';
import { ProfileForm, type ProfileFormRef } from '../ProfileForm';
import AddressForm, { type AddressFormRef } from './AddressForm';
import { Button } from '@/components/ui/button';
import { Save, Loader2, User, MapPin } from 'lucide-react';
import { toast } from 'sonner';
import { updateCustomerProfileAndAddress, type UnifiedProfileFormState } from '../actions';

interface UnifiedProfileFormProps {
  initialName: string | null;
  initialAddressData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  } | null;
  avatarUrl?: string;
}

export default function UnifiedProfileForm({
  initialName,
  initialAddressData,
}: UnifiedProfileFormProps) {
  const profileFormRef = useRef<ProfileFormRef>(null);
  const addressFormRef = useRef<AddressFormRef>(null);
  const [isPending, startTransition] = useTransition();
  const [formState, setFormState] = useState<UnifiedProfileFormState>({
    message: null,
    errors: {},
    success: false
  });

  const handleUnifiedSubmit = async () => {
    // Get data from both forms
    const profileData = profileFormRef.current?.getFormData();
    const addressData = addressFormRef.current?.getFormData();
    
    // Validate both forms
    const isProfileValid = profileFormRef.current?.validateForm() ?? false;
    const isAddressValid = addressFormRef.current?.validateForm() ?? true; // Address is optional
    
    // Check if name is provided (required)
    if (!profileData?.name?.trim()) {
      toast.error('Name is required');
      return;
    }

    // If address data is partially filled, validate that required fields are present
    const hasAnyAddressField = addressData && (
      addressData.pincode || addressData.city || addressData.state || addressData.locality
    );
    
    if (hasAnyAddressField && !isAddressValid) {
      toast.error('Please complete all required address fields or leave them empty');
      return;
    }

    // Create FormData for submission
    const formData = new FormData();
    formData.append('name', profileData.name);
    
    // Add address data if provided
    if (addressData) {
      formData.append('address', addressData.address || '');
      formData.append('pincode', addressData.pincode || '');
      formData.append('city', addressData.city || '');
      formData.append('state', addressData.state || '');
      formData.append('locality', addressData.locality || '');
    }

    // Submit the unified form
    startTransition(async () => {
      try {
        const initialState: UnifiedProfileFormState = {
          message: null,
          errors: {},
          success: false
        };

        const result = await updateCustomerProfileAndAddress(initialState, formData);
        setFormState(result);
        
        if (result.success) {
          toast.success(result.message || 'Profile updated successfully!');
        } else {
          toast.error(result.message || 'Failed to update profile');
        }
      } catch (error) {
        console.error('Error submitting unified form:', error);
        const errorState: UnifiedProfileFormState = {
          message: 'An unexpected error occurred. Please try again.',
          success: false,
          errors: {}
        };
        setFormState(errorState);
        toast.error('An unexpected error occurred. Please try again.');
      }
    });
  };

  return (
    <div className="space-y-8">
      {/* Profile Information Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-muted">
            <User className="w-5 h-5 text-foreground" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-foreground">
              Personal Information
            </h2>
            <p className="text-sm text-muted-foreground">
              Update your name and personal details
            </p>
          </div>
        </div>
        
        <ProfileForm 
          ref={profileFormRef}
          initialName={initialName}
          hideSubmitButton={true}
        />
      </div>

      {/* Address Information Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-muted">
            <MapPin className="w-5 h-5 text-foreground" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-foreground">
              Address Information
            </h2>
            <p className="text-sm text-muted-foreground">
              Update your address details (optional)
            </p>
          </div>
        </div>
        
        <AddressForm 
          ref={addressFormRef}
          initialData={initialAddressData || undefined}
          hideSubmitButton={true}
        />
      </div>

      {/* Unified Submit Button */}
      <div className="flex justify-center pt-6 border-t">
        <Button
          onClick={handleUnifiedSubmit}
          disabled={isPending}
          size="lg"
          className="bg-primary hover:bg-primary/90 text-primary-foreground px-8"
        >
          {isPending ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              Saving Changes...
            </>
          ) : (
            <>
              <Save className="w-5 h-5 mr-2" />
              Save Profile
            </>
          )}
        </Button>
      </div>

      {/* Error Display */}
      {formState.message && !formState.success && (
        <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
          <p className="text-sm text-destructive">{formState.message}</p>
        </div>
      )}
    </div>
  );
}
