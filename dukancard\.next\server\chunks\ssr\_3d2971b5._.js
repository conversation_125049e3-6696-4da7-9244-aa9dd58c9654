module.exports = {

"[project]/app/auth/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00a78b43259bdfa35946a0918da66b9382dcd7b4dc":"signOutUser"},"",""] */ __turbopack_context__.s({
    "signOutUser": (()=>signOutUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function signOutUser() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { error: _error } = await supabase.auth.signOut();
        // Note: Sign out errors are typically not critical for user experience
        // The user will be redirected to login regardless
        // Explicitly clear auth cookies to ensure logout
        const cookieStore = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i).then((m)=>m.cookies());
        const cookiesToClear = [
            "sb-access-token",
            "sb-refresh-token"
        ];
        for (const cookieName of cookiesToClear){
            try {
                cookieStore.set(cookieName, "", {
                    expires: new Date(0),
                    maxAge: -1
                });
            } catch  {
            // Cookie clearing errors are not critical for sign out
            // Continue with the sign out process
            }
        }
    } catch  {
    // Even if sign out fails, redirect to login for security
    // User will be treated as logged out
    }
    // Redirect to login with a flag to prevent middleware redirect loop
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?logged_out=true");
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    signOutUser
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signOutUser, "00a78b43259bdfa35946a0918da66b9382dcd7b4dc", null);
}}),
"[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminClient": (()=>createAdminClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-rsc] (ecmascript) <locals>");
;
function createAdminClient() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
}
}}),
"[project]/lib/schemas/authSchemas.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "IndianMobileSchema": (()=>IndianMobileSchema),
    "NewPasswordConfirmationSchema": (()=>NewPasswordConfirmationSchema),
    "PasswordComplexitySchema": (()=>PasswordComplexitySchema),
    "PasswordConfirmationSchema": (()=>PasswordConfirmationSchema),
    "SingleNewPasswordSchema": (()=>SingleNewPasswordSchema),
    "SinglePasswordSchema": (()=>SinglePasswordSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
const PasswordComplexitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(6, {
    message: "Password must be at least 6 characters"
}).regex(/[A-Z]/, {
    message: "Password must contain at least one capital letter"
}).regex(/[a-z]/, {
    message: "Password must contain at least one lowercase letter."
}) // Added lowercase check for consistency
.regex(/[0-9]/, {
    message: "Password must contain at least one number"
}).regex(/[^A-Za-z0-9]/, {
    message: "Password must contain at least one symbol"
});
const IndianMobileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(10, {
    message: "Mobile number must be at least 10 digits"
}).max(10, {
    message: "Mobile number must be exactly 10 digits"
}).regex(/^\d{10}$/, {
    message: "Please enter a valid 10-digit mobile number"
});
const PasswordConfirmationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    password: PasswordComplexitySchema,
    confirmPassword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}).refine((data)=>data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: [
        "confirmPassword"
    ]
});
const NewPasswordConfirmationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    newPassword: PasswordComplexitySchema,
    confirmPassword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}).refine((data)=>data.newPassword === data.confirmPassword, {
    message: "Passwords do not match.",
    path: [
        "confirmPassword"
    ]
});
const SinglePasswordSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    password: PasswordComplexitySchema
});
const SingleNewPasswordSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    password: PasswordComplexitySchema
});
}}),
"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"601efcd933277679be074bdf16199352e0f1ee1dd3":"updateCustomerAddress","6030518e442d65bd60ceabac00e58e2d46c3a0c2bc":"updateCustomerEmail","60606984ed763a79a21c8467f3859b77a5c30c66eb":"updateCustomerProfile","60b030c5922dce4fc4ce7994c5b77c77c4ecc7186a":"updateCustomerMobile","60b52c8e077b3bd4f2e340da602cbf227e7628ca8b":"updateCustomerPhone","60e2048999976108d182f440e74ddcd263930eb412":"updateCustomerProfileAndAddress"},"",""] */ __turbopack_context__.s({
    "updateCustomerAddress": (()=>updateCustomerAddress),
    "updateCustomerEmail": (()=>updateCustomerEmail),
    "updateCustomerMobile": (()=>updateCustomerMobile),
    "updateCustomerPhone": (()=>updateCustomerPhone),
    "updateCustomerProfile": (()=>updateCustomerProfile),
    "updateCustomerProfileAndAddress": (()=>updateCustomerProfileAndAddress)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/schemas/authSchemas.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
// Define the schema for profile updates
const ProfileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(1, 'Name cannot be empty').max(100, 'Name is too long')
});
// Define the schema for phone updates
const PhoneSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    phone: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["IndianMobileSchema"]
});
// Define the schema for address updates with proper validation
const AddressSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().max(100, {
        message: "Address cannot exceed 100 characters."
    }).optional().or(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].literal("")),
    pincode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(1, {
        message: "Pincode is required"
    }).regex(/^\d{6}$/, {
        message: "Must be a valid 6-digit pincode"
    }),
    city: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(1, {
        message: "City is required"
    }).refine((val)=>val.trim().length > 0, {
        message: "City cannot be empty"
    }),
    state: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(1, {
        message: "State is required"
    }).refine((val)=>val.trim().length > 0, {
        message: "State cannot be empty"
    }),
    locality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(1, {
        message: "Locality is required"
    }).refine((val)=>val.trim().length > 0, {
        message: "Locality cannot be empty"
    })
});
// Define the schema for email updates
const EmailSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().email({
        message: "Please enter a valid email address"
    })
});
// Define the schema for mobile updates
const MobileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    mobile: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["IndianMobileSchema"]
});
// Define the unified schema for profile and address updates
const UnifiedProfileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),
    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().max(100, {
        message: "Address cannot exceed 100 characters."
    }).optional().or(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].literal("")),
    pincode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(1, {
        message: "Pincode is required"
    }).regex(/^\d{6}$/, {
        message: "Must be a valid 6-digit pincode"
    }).optional().or(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].literal("")),
    city: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().or(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].literal("")),
    state: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().or(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].literal("")),
    locality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().or(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].literal(""))
});
async function updateCustomerProfile(prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Added await
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = ProfileSchema.safeParse({
        name: formData.get('name')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { name } = validatedFields.data;
    try {
        // Update name in auth.users table (full_name in user_metadata)
        // The database trigger will automatically sync this to customer_profiles table
        const { error: authUpdateError } = await supabase.auth.updateUser({
            data: {
                full_name: name
            }
        });
        if (authUpdateError) {
            console.error('Error updating auth user metadata:', authUpdateError);
            return {
                message: `Auth Error: ${authUpdateError.message}`,
                success: false
            };
        }
        // Revalidate the profile page and potentially the layout to reflect name change
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/profile');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/layout'); // To update sidebar/header name
        return {
            message: 'Profile updated successfully!',
            success: true
        };
    } catch (error) {
        console.error('Unexpected error updating profile:', error);
        return {
            message: 'An unexpected error occurred.',
            success: false
        };
    }
}
async function updateCustomerAddress(prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = AddressSchema.safeParse({
        address: formData.get('address'),
        pincode: formData.get('pincode'),
        city: formData.get('city'),
        state: formData.get('state'),
        locality: formData.get('locality')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { address, pincode, city, state, locality } = validatedFields.data;
    try {
        const { error: updateError } = await supabase.from('customer_profiles').update({
            address: address || null,
            pincode,
            city,
            state,
            locality
        }).eq('id', user.id);
        if (updateError) {
            console.error('Error updating customer address:', updateError);
            return {
                message: `Database Error: ${updateError.message}`,
                success: false
            };
        }
        // Revalidate relevant pages
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/profile');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer');
        return {
            message: 'Address updated successfully!',
            success: true
        };
    } catch (error) {
        console.error('Unexpected error updating address:', error);
        return {
            message: 'An unexpected error occurred.',
            success: false
        };
    }
}
async function updateCustomerPhone(prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = PhoneSchema.safeParse({
        phone: formData.get('phone')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { phone } = validatedFields.data;
    try {
        // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number
        // Update phone in customer_profiles table
        const { error: updateError } = await supabase.from('customer_profiles').update({
            phone: phone
        }).eq('id', user.id);
        if (updateError) {
            console.error('Error updating customer phone:', updateError);
            return {
                message: `Database Error: ${updateError.message}`,
                success: false
            };
        }
        // Update phone in Supabase auth.users table to maintain user ID consistency
        const { error: authUpdateError } = await supabase.auth.updateUser({
            phone: `+91${phone}`
        });
        if (authUpdateError) {
            console.warn('Failed to update auth phone field:', authUpdateError.message);
        // Don't fail the operation for this, just log the warning
        // The customer_profiles table is updated successfully
        }
        // Revalidate relevant pages
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/profile');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer');
        return {
            message: 'Phone number updated successfully!',
            success: true
        };
    } catch (error) {
        console.error('Unexpected error updating phone:', error);
        return {
            message: 'An unexpected error occurred.',
            success: false
        };
    }
}
async function updateCustomerEmail(prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = EmailSchema.safeParse({
        email: formData.get('email')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { email } = validatedFields.data;
    try {
        // Check if user registered with Google OAuth (matching settings page logic)
        const isGoogleLogin = user.app_metadata?.provider === 'google';
        // Check if the user has email/password authentication
        let hasEmailAuth = false;
        if (isGoogleLogin && user.email) {
            try {
                // Use admin client to check user identities
                const adminClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
                const { data: authData } = await adminClient.auth.admin.getUserById(user.id);
                // Check if the user has email/password authentication
                if (authData?.user?.identities) {
                    hasEmailAuth = authData.user.identities.some((identity)=>identity.provider === "email");
                }
            } catch (error) {
                console.error("Error checking user auth methods:", error);
                hasEmailAuth = false;
            }
        }
        // Only disable email changes if they're using Google and don't have email auth
        const shouldDisableEmailChange = isGoogleLogin && !hasEmailAuth;
        if (shouldDisableEmailChange) {
            return {
                message: 'Email cannot be changed for Google accounts. Your email is linked to your Google account.',
                success: false
            };
        }
        // Check if email is the same as current
        if (user.email === email) {
            return {
                message: 'Email address is the same as current.',
                success: false
            };
        }
        // Update email in Supabase auth.users table
        // This is the primary source of truth for email
        const { error: authUpdateError } = await supabase.auth.updateUser({
            email: email
        });
        if (authUpdateError) {
            console.error('Error updating auth email:', authUpdateError);
            // Provide user-friendly error messages
            let errorMessage = 'Failed to update email address.';
            if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {
                errorMessage = 'This email address is already in use by another account.';
            } else if (authUpdateError.message.includes('check constraint')) {
                errorMessage = 'Invalid email format provided.';
            } else if (authUpdateError.message.includes('rate limit')) {
                errorMessage = 'Too many requests. Please try again later.';
            }
            return {
                message: errorMessage,
                success: false
            };
        }
        // Note: customer_profiles table will be automatically updated via database trigger
        // Revalidate relevant pages
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/profile');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer');
        return {
            message: 'Email address updated successfully! You may need to verify the new email address.',
            success: true
        };
    } catch (error) {
        console.error('Unexpected error updating email:', error);
        return {
            message: 'An unexpected error occurred.',
            success: false
        };
    }
}
async function updateCustomerMobile(prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = MobileSchema.safeParse({
        mobile: formData.get('mobile')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { mobile } = validatedFields.data;
    try {
        // Check if mobile is the same as current
        const currentMobile = user.phone ? user.phone.replace(/^\+91/, '') : '';
        if (mobile === currentMobile) {
            return {
                message: 'Mobile number is the same as current.',
                success: false
            };
        }
        // Note: Mobile uniqueness check removed as multiple businesses/customers can share the same number
        // Update mobile in Supabase auth.users table
        // This is the primary source of truth for mobile
        const { error: authUpdateError } = await supabase.auth.updateUser({
            phone: `+91${mobile}`
        });
        if (authUpdateError) {
            console.error('Error updating auth mobile:', authUpdateError);
            // Provide user-friendly error messages
            let errorMessage = 'Failed to update mobile number.';
            if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {
                errorMessage = 'This mobile number is already in use by another account.';
            } else if (authUpdateError.message.includes('check constraint')) {
                errorMessage = 'Invalid mobile number format provided.';
            } else if (authUpdateError.message.includes('rate limit')) {
                errorMessage = 'Too many requests. Please try again later.';
            }
            return {
                message: errorMessage,
                success: false
            };
        }
        // Note: customer_profiles table will be automatically updated via database trigger
        // Revalidate relevant pages
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/profile');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer');
        return {
            message: 'Mobile number updated successfully!',
            success: true
        };
    } catch (error) {
        console.error('Unexpected error updating mobile:', error);
        return {
            message: 'An unexpected error occurred.',
            success: false
        };
    }
}
async function updateCustomerProfileAndAddress(prevState, formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        return {
            message: 'Not authenticated',
            success: false
        };
    }
    const validatedFields = UnifiedProfileSchema.safeParse({
        name: formData.get('name'),
        address: formData.get('address'),
        pincode: formData.get('pincode'),
        city: formData.get('city'),
        state: formData.get('state'),
        locality: formData.get('locality')
    });
    if (!validatedFields.success) {
        return {
            message: 'Invalid data provided.',
            errors: validatedFields.error.flatten().fieldErrors,
            success: false
        };
    }
    const { name, address, pincode, city, state, locality } = validatedFields.data;
    // Validate that if any address field is provided, required fields are present
    const hasAnyAddressField = pincode || city || state || locality;
    if (hasAnyAddressField) {
        if (!pincode || !city || !state || !locality) {
            return {
                message: 'If providing address information, pincode, city, state, and locality are required.',
                success: false
            };
        }
    }
    try {
        // Update name in auth.users table (full_name in user_metadata)
        const { error: authUpdateError } = await supabase.auth.updateUser({
            data: {
                full_name: name
            }
        });
        if (authUpdateError) {
            console.error('Error updating auth user metadata:', authUpdateError);
            return {
                message: `Auth Error: ${authUpdateError.message}`,
                success: false
            };
        }
        // Update address in customer_profiles table if address data is provided
        if (hasAnyAddressField) {
            const { error: updateError } = await supabase.from('customer_profiles').update({
                address: address || null,
                pincode,
                city,
                state,
                locality
            }).eq('id', user.id);
            if (updateError) {
                console.error('Error updating customer address:', updateError);
                return {
                    message: `Database Error: ${updateError.message}`,
                    success: false
                };
            }
        }
        // Revalidate relevant pages
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/profile');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/layout');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer');
        return {
            message: hasAnyAddressField ? 'Profile and address updated successfully!' : 'Profile updated successfully!',
            success: true
        };
    } catch (error) {
        console.error('Unexpected error updating profile and address:', error);
        return {
            message: 'An unexpected error occurred.',
            success: false
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    updateCustomerProfile,
    updateCustomerAddress,
    updateCustomerPhone,
    updateCustomerEmail,
    updateCustomerMobile,
    updateCustomerProfileAndAddress
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomerProfile, "60606984ed763a79a21c8467f3859b77a5c30c66eb", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomerAddress, "601efcd933277679be074bdf16199352e0f1ee1dd3", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomerPhone, "60b52c8e077b3bd4f2e340da602cbf227e7628ca8b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomerEmail, "6030518e442d65bd60ceabac00e58e2d46c3a0c2bc", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomerMobile, "60b030c5922dce4fc4ce7994c5b77c77c4ecc7186a", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomerProfileAndAddress, "60e2048999976108d182f440e74ddcd263930eb412", null);
}}),
"[project]/lib/actions/location.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40a4f1bdb64f685f6ccce4167da600f46ae100d291":"getCitySuggestions","40ab9ff6341449bb46121f282a1e253cc89e3417db":"getPincodeDetails","40bca17f643f4850be4c127be89ffee8d9df0dc138":"getCityDetails"},"",""] */ __turbopack_context__.s({
    "getCityDetails": (()=>getCityDetails),
    "getCitySuggestions": (()=>getCitySuggestions),
    "getPincodeDetails": (()=>getPincodeDetails)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function getPincodeDetails(pincode) {
    if (!pincode || !/^\d{6}$/.test(pincode)) {
        return {
            error: "Invalid Pincode format."
        };
    }
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // First get city and state from pincodes table
        const { data: pincodeData, error: pincodeError } = await supabase.from("pincodes").select("OfficeName, DivisionName, StateName").eq("Pincode", pincode) // Updated column name to match database
        .order("OfficeName");
        if (pincodeError) {
            console.error("Pincode Fetch Error:", pincodeError);
            return {
                error: "Database error fetching pincode details."
            };
        }
        if (!pincodeData || pincodeData.length === 0) {
            return {
                error: "Pincode not found."
            };
        }
        // State names are already in title case format in the database
        const state = pincodeData[0].StateName;
        // Use DivisionName as the city (already cleaned)
        const city = pincodeData[0].DivisionName;
        // Get unique localities from post office names
        const localities = [
            ...new Set(pincodeData.map((item)=>item.OfficeName))
        ];
        return {
            data: {
                city,
                state,
                localities
            },
            city,
            state,
            localities
        };
    } catch (e) {
        console.error("Pincode Lookup Exception:", e);
        return {
            error: "An unexpected error occurred during pincode lookup."
        };
    }
}
async function getCityDetails(city) {
    if (!city || city.length < 2) {
        return {
            error: "City name must be at least 2 characters."
        };
    }
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Get pincodes and state for the city - DivisionName is the city column
        const { data: cityData, error: cityError } = await supabase.from("pincodes").select("Pincode, OfficeName, StateName, DivisionName").ilike("DivisionName", `%${city}%`).order("Pincode");
        if (cityError) {
            console.error("City Fetch Error:", cityError);
            return {
                error: "Database error fetching city details."
            };
        }
        if (!cityData || cityData.length === 0) {
            return {
                error: "City not found."
            };
        }
        // State names are already in title case format in the database
        const state = cityData[0].StateName;
        // Get unique pincodes
        const pincodes = [
            ...new Set(cityData.map((item)=>item.Pincode))
        ];
        // Get unique localities from post office names
        const localities = [
            ...new Set(cityData.map((item)=>item.OfficeName))
        ];
        return {
            data: {
                pincodes,
                state,
                localities
            },
            pincodes,
            state,
            localities
        };
    } catch (e) {
        console.error("City Lookup Exception:", e);
        return {
            error: "An unexpected error occurred during city lookup."
        };
    }
}
async function getCitySuggestions(query) {
    if (!query || query.length < 2) {
        return {
            error: "Query must be at least 2 characters."
        };
    }
    const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
    try {
        // Use the PostgreSQL function to get distinct cities (up to 5)
        const { data: cityData, error: cityError } = await supabaseAdmin.rpc('get_distinct_cities', {
            search_query: `%${query}%`,
            result_limit: 5
        });
        if (cityError) {
            console.error("City Suggestions Error:", cityError);
            // Fallback to regular query if RPC fails
            try {
                // Use a regular query as fallback
                const { data: fallbackData, error: fallbackError } = await supabaseAdmin.from("pincodes").select("DivisionName").ilike("DivisionName", `%${query}%`).order("DivisionName").limit(100);
                if (fallbackError) {
                    throw fallbackError;
                }
                if (!fallbackData || fallbackData.length === 0) {
                    return {
                        data: {
                            cities: []
                        },
                        cities: []
                    };
                }
                // Get unique cities and format them
                const cities = [
                    ...new Set(fallbackData.map((item)=>item.DivisionName.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase())))
                ];
                const topCities = cities.slice(0, 5);
                return {
                    data: {
                        cities: topCities
                    },
                    cities: topCities
                };
            } catch (fallbackErr) {
                console.error("Fallback City Query Error:", fallbackErr);
                return {
                    error: "Database error fetching city suggestions."
                };
            }
        }
        if (!cityData || cityData.length === 0) {
            return {
                data: {
                    cities: []
                },
                cities: []
            };
        }
        // Format the city names to Title Case
        const cities = cityData.map((item)=>item.city.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase()));
        return {
            data: {
                cities
            },
            cities
        };
    } catch (e) {
        console.error("City Suggestions Exception:", e);
        return {
            error: "An unexpected error occurred while fetching city suggestions."
        };
    }
} // --- End City Autocomplete ---
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getPincodeDetails,
    getCityDetails,
    getCitySuggestions
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getPincodeDetails, "40ab9ff6341449bb46121f282a1e253cc89e3417db", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCityDetails, "40bca17f643f4850be4c127be89ffee8d9df0dc138", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCitySuggestions, "40a4f1bdb64f685f6ccce4167da600f46ae100d291", null);
}}),
"[project]/lib/utils/storage-paths.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Scalable Storage Path Utilities
 *
 * This module provides utilities for generating scalable storage paths
 * that can handle billions of users efficiently using hash-based distribution.
 */ /**
 * Generate scalable user path using hash-based distribution
 *
 * @param userId - The user's UUID
 * @returns Scalable path: users/{prefix}/{midfix}/{userId}
 *
 * Example:
 * - Input: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 * - Output: "users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 */ __turbopack_context__.s({
    "PathValidator": (()=>PathValidator),
    "StorageAnalytics": (()=>StorageAnalytics),
    "getCustomAdImagePath": (()=>getCustomAdImagePath),
    "getCustomHeaderImagePath": (()=>getCustomHeaderImagePath),
    "getCustomerAvatarPath": (()=>getCustomerAvatarPath),
    "getCustomerPostImagePath": (()=>getCustomerPostImagePath),
    "getGalleryImagePath": (()=>getGalleryImagePath),
    "getPostFolderPath": (()=>getPostFolderPath),
    "getPostImagePath": (()=>getPostImagePath),
    "getProductBaseImagePath": (()=>getProductBaseImagePath),
    "getProductImagePath": (()=>getProductImagePath),
    "getProductVariantImagePath": (()=>getProductVariantImagePath),
    "getProfileImagePath": (()=>getProfileImagePath),
    "getScalableUserPath": (()=>getScalableUserPath),
    "getThemeSpecificHeaderImagePath": (()=>getThemeSpecificHeaderImagePath)
});
function getScalableUserPath(userId) {
    if (!userId || typeof userId !== 'string') {
        throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);
    }
    if (userId.length < 4) {
        throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);
    }
    const prefix = userId.substring(0, 2).toLowerCase();
    const midfix = userId.substring(2, 4).toLowerCase();
    return `users/${prefix}/${midfix}/${userId}`;
}
function getProfileImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/profile/logo_${timestamp}.webp`;
}
function getProductImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;
}
function getProductBaseImagePath(userId, productId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;
}
function getProductVariantImagePath(userId, productId, variantId, imageIndex, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;
}
function getGalleryImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/gallery/gallery_${timestamp}.webp`;
}
function getPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getPostFolderPath(userId, postId, createdAt) {
    const userPath = getScalableUserPath(userId);
    const postDate = new Date(createdAt);
    const year = postDate.getFullYear();
    const month = String(postDate.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}`;
}
function getCustomerAvatarPath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/avatar/avatar_${timestamp}.webp`;
}
function getCustomerPostImagePath(userId, postId, imageIndex, timestamp, createdAt) {
    const userPath = getScalableUserPath(userId);
    // Use post creation date if provided, otherwise use current date (for backward compatibility)
    const dateToUse = createdAt ? new Date(createdAt) : new Date();
    const year = dateToUse.getFullYear();
    const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
    return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}
function getCustomAdImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/ads/custom_ad_${timestamp}.webp`;
}
function getCustomHeaderImagePath(userId, timestamp) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${timestamp}.webp`;
}
function getThemeSpecificHeaderImagePath(userId, timestamp, theme) {
    const userPath = getScalableUserPath(userId);
    return `${userPath}/branding/header_${theme}_${timestamp}.webp`;
}
class PathValidator {
    /**
   * Validate if a path follows the new scalable structure
   */ static isScalablePath(path) {
        return path.startsWith('users/') && path.split('/').length >= 4;
    }
    /**
   * Extract user ID from scalable path
   */ static extractUserIdFromPath(path) {
        if (!this.isScalablePath(path)) {
            return null;
        }
        const parts = path.split('/');
        return parts[3]; // users/{prefix}/{midfix}/{userId}/...
    }
    /**
   * Validate path structure integrity
   */ static validatePathStructure(userId, path) {
        const expectedUserPath = getScalableUserPath(userId);
        return path.startsWith(expectedUserPath);
    }
}
class StorageAnalytics {
    /**
   * Get storage distribution info for monitoring
   */ static getDistributionInfo(userId) {
        const prefix = userId.substring(0, 2).toLowerCase();
        const midfix = userId.substring(2, 4).toLowerCase();
        // Estimate number of users in same bucket (assuming even distribution)
        const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets
        const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users
        return {
            prefix,
            midfix,
            bucket: `${prefix}/${midfix}`,
            estimatedPeers
        };
    }
}
}}),
"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"400412f1eb89dd7bc7c1bba76428244b575e3acba6":"uploadAvatarAndGetUrl","40ebef699b6761cfd539429fec1e4d0a90ae48b158":"updateAvatarUrl"},"",""] */ __turbopack_context__.s({
    "updateAvatarUrl": (()=>updateAvatarUrl),
    "uploadAvatarAndGetUrl": (()=>uploadAvatarAndGetUrl)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/storage-paths.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function uploadAvatarAndGetUrl(formData) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    const userId = user.id;
    const file = formData.get("avatarFile");
    if (!file) {
        return {
            success: false,
            error: "No avatar file provided."
        };
    }
    const allowedTypes = [
        "image/png",
        "image/jpeg",
        "image/gif",
        "image/webp"
    ];
    if (!allowedTypes.includes(file.type)) {
        return {
            success: false,
            error: "Invalid file type."
        };
    }
    // Server-side file size validation (15MB limit)
    if (file.size > 15 * 1024 * 1024) {
        return {
            success: false,
            error: "File size must be less than 15MB."
        };
    }
    try {
        // File is already compressed on client-side, just upload it
        const fileBuffer = Buffer.from(await file.arrayBuffer());
        const bucketName = "customers"; // Plural form - matches the bucket name
        const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000); // Add random milliseconds for extra uniqueness
        const fullPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$storage$2d$paths$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCustomerAvatarPath"])(userId, timestamp);
        // Upload the processed image
        const { error: uploadError } = await supabase.storage.from(bucketName).upload(fullPath, fileBuffer, {
            contentType: file.type,
            upsert: true
        });
        if (uploadError) {
            console.error("Avatar Upload Error:", uploadError);
            return {
                success: false,
                error: `Failed to upload avatar: ${uploadError.message}`
            };
        }
        // No need to add timestamp to URL as we already have it in the filename
        const { data: urlData } = supabase.storage.from(bucketName).getPublicUrl(fullPath);
        if (!urlData?.publicUrl) {
            console.error("Get Public URL Error: URL data is null or missing publicUrl property for path:", fullPath);
            return {
                success: false,
                error: "Could not retrieve public URL after upload."
            };
        }
        return {
            success: true,
            url: urlData.publicUrl
        };
    } catch (processingError) {
        console.error("Image Processing/Upload Error:", processingError);
        return {
            success: false,
            error: "Failed to process or upload image."
        };
    }
}
async function updateAvatarUrl(avatarUrl) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    const { error: updateError } = await supabase.from("customer_profiles").update({
        avatar_url: avatarUrl,
        updated_at: new Date().toISOString()
    }).eq("id", user.id);
    if (updateError) {
        console.error("Avatar URL Update Error:", updateError);
        return {
            success: false,
            error: `Failed to update avatar URL: ${updateError.message}`
        };
    }
    // Revalidate paths to update the UI
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard/customer/profile');
    return {
        success: true
    };
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    uploadAvatarAndGetUrl,
    updateAvatarUrl
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(uploadAvatarAndGetUrl, "400412f1eb89dd7bc7c1bba76428244b575e3acba6", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateAvatarUrl, "40ebef699b6761cfd539429fec1e4d0a90ae48b158", null);
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00a78b43259bdfa35946a0918da66b9382dcd7b4dc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["signOutUser"]),
    "400412f1eb89dd7bc7c1bba76428244b575e3acba6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uploadAvatarAndGetUrl"]),
    "40ab9ff6341449bb46121f282a1e253cc89e3417db": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPincodeDetails"]),
    "40ebef699b6761cfd539429fec1e4d0a90ae48b158": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateAvatarUrl"]),
    "601efcd933277679be074bdf16199352e0f1ee1dd3": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateCustomerAddress"]),
    "60606984ed763a79a21c8467f3859b77a5c30c66eb": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateCustomerProfile"]),
    "60e2048999976108d182f440e74ddcd263930eb412": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateCustomerProfileAndAddress"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00a78b43259bdfa35946a0918da66b9382dcd7b4dc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00a78b43259bdfa35946a0918da66b9382dcd7b4dc"]),
    "400412f1eb89dd7bc7c1bba76428244b575e3acba6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["400412f1eb89dd7bc7c1bba76428244b575e3acba6"]),
    "40ab9ff6341449bb46121f282a1e253cc89e3417db": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40ab9ff6341449bb46121f282a1e253cc89e3417db"]),
    "40ebef699b6761cfd539429fec1e4d0a90ae48b158": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40ebef699b6761cfd539429fec1e4d0a90ae48b158"]),
    "601efcd933277679be074bdf16199352e0f1ee1dd3": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["601efcd933277679be074bdf16199352e0f1ee1dd3"]),
    "60606984ed763a79a21c8467f3859b77a5c30c66eb": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60606984ed763a79a21c8467f3859b77a5c30c66eb"]),
    "60e2048999976108d182f440e74ddcd263930eb412": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60e2048999976108d182f440e74ddcd263930eb412"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$avatar$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/profile/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/(dashboard)/dashboard/customer/profile/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/(dashboard)/dashboard/customer/profile/avatar-actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx <module evaluation>", "default");
}}),
"[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx", "default");
}}),
"[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$components$2f$ProfilePageClient$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$components$2f$ProfilePageClient$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$components$2f$ProfilePageClient$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/lib/utils/addressValidation.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Customer address validation utility
 * Checks if customer has complete address information
 */ __turbopack_context__.s({
    "getAddressValidationMessage": (()=>getAddressValidationMessage),
    "getMissingAddressFields": (()=>getMissingAddressFields),
    "getMissingProfileFields": (()=>getMissingProfileFields),
    "getProfileValidationMessage": (()=>getProfileValidationMessage),
    "isCustomerAddressComplete": (()=>isCustomerAddressComplete),
    "isCustomerNameComplete": (()=>isCustomerNameComplete),
    "isCustomerProfileComplete": (()=>isCustomerProfileComplete)
});
function isCustomerAddressComplete(addressData) {
    const { pincode, state, city, locality } = addressData;
    // Check if required fields are present and not empty
    return !!(pincode && pincode.trim() !== '' && state && state.trim() !== '' && city && city.trim() !== '' && locality && locality.trim() !== '');
}
function getMissingAddressFields(addressData) {
    const missing = [];
    if (!addressData.pincode || addressData.pincode.trim() === '') {
        missing.push('pincode');
    }
    if (!addressData.state || addressData.state.trim() === '') {
        missing.push('state');
    }
    if (!addressData.city || addressData.city.trim() === '') {
        missing.push('city');
    }
    if (!addressData.locality || addressData.locality.trim() === '') {
        missing.push('locality');
    }
    return missing;
}
function getAddressValidationMessage(missingFields) {
    if (missingFields.length === 0) {
        return '';
    }
    const fieldNames = missingFields.map((field)=>{
        switch(field){
            case 'pincode':
                return 'Pincode';
            case 'state':
                return 'State';
            case 'city':
                return 'City';
            case 'locality':
                return 'Locality';
            default:
                return field;
        }
    });
    if (fieldNames.length === 1) {
        return `Please update your ${fieldNames[0]} in your profile.`;
    } else if (fieldNames.length === 2) {
        return `Please update your ${fieldNames.join(' and ')} in your profile.`;
    } else {
        const lastField = fieldNames.pop();
        return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;
    }
}
function isCustomerNameComplete(name) {
    return !!(name && name.trim() !== '');
}
function isCustomerProfileComplete(profileData) {
    return isCustomerNameComplete(profileData.name) && isCustomerAddressComplete(profileData);
}
function getMissingProfileFields(profileData) {
    const missing = [];
    // Check name
    if (!isCustomerNameComplete(profileData.name)) {
        missing.push('name');
    }
    // Check address fields
    const missingAddressFields = getMissingAddressFields(profileData);
    missing.push(...missingAddressFields);
    return missing;
}
function getProfileValidationMessage(missingFields) {
    if (missingFields.length === 0) {
        return '';
    }
    const fieldNames = missingFields.map((field)=>{
        switch(field){
            case 'name':
                return 'Name';
            case 'pincode':
                return 'Pincode';
            case 'state':
                return 'State';
            case 'city':
                return 'City';
            case 'locality':
                return 'Locality';
            default:
                return field;
        }
    });
    if (fieldNames.length === 1) {
        return `Please update your ${fieldNames[0]} in your profile.`;
    } else if (fieldNames.length === 2) {
        return `Please update your ${fieldNames.join(' and ')} in your profile.`;
    } else {
        const lastField = fieldNames.pop();
        return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;
    }
}
}}),
"[project]/app/(dashboard)/dashboard/customer/profile/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CustomerProfilePage),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$components$2f$ProfilePageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/addressValidation.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const metadata = {
    title: 'My Profile - Dukancard',
    description: 'Manage your Dukancard customer profile.',
    robots: 'noindex, nofollow'
};
async function CustomerProfilePage() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/login');
    }
    // Fetch customer profile data from customer_profiles table using user.id
    const { data: profile, error: profileError } = await supabase.from('customer_profiles').select('avatar_url, address, pincode, city, state, locality').eq('id', user.id).maybeSingle(); // Use maybeSingle to handle potential null profile
    // Get name from auth.users table (full_name column)
    let initialName = null;
    if (user.user_metadata?.full_name) {
        initialName = user.user_metadata.full_name;
    } else if (user.user_metadata?.name) {
        initialName = user.user_metadata.name;
    }
    let initialAvatarUrl = null;
    let initialAddressData = null;
    let hasCompleteAddress = false;
    if (profileError) {
        console.error('Error fetching customer profile:', profileError);
    // Handle error appropriately, maybe show a message to the user
    // For now, we'll proceed with null values, the components can handle it
    } else {
        initialAvatarUrl = profile?.avatar_url || null; // Use fetched avatar_url or null
        initialAddressData = {
            address: profile?.address,
            pincode: profile?.pincode,
            city: profile?.city,
            state: profile?.state,
            locality: profile?.locality
        };
        // Check if address is complete
        hasCompleteAddress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isCustomerAddressComplete"])(initialAddressData);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$profile$2f$components$2f$ProfilePageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
        initialName: initialName,
        initialAvatarUrl: initialAvatarUrl,
        initialAddressData: initialAddressData,
        hasCompleteAddress: hasCompleteAddress
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/profile/page.tsx",
        lineNumber: 63,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(dashboard)/dashboard/customer/profile/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/profile/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_3d2971b5._.js.map