{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/avatar-actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { getCustomerAvatarPath } from '@/lib/utils/storage-paths';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// Action to upload avatar and return public URL\r\nexport async function uploadAvatarAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"avatarFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No avatar file provided.\" };\r\n  }\r\n\r\n  const allowedTypes = [\"image/png\", \"image/jpeg\", \"image/gif\", \"image/webp\"];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation (15MB limit)\r\n  if (file.size > 15 * 1024 * 1024) {\r\n    return { success: false, error: \"File size must be less than 15MB.\" };\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    const bucketName = \"customers\"; // Plural form - matches the bucket name\r\n    const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000); // Add random milliseconds for extra uniqueness\r\n    const fullPath = getCustomerAvatarPath(userId, timestamp);\r\n\r\n    // Upload the processed image\r\n    const { error: uploadError } = await supabase.storage\r\n      .from(bucketName)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Avatar Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload avatar: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // No need to add timestamp to URL as we already have it in the filename\r\n    const { data: urlData } = supabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n\r\n// Action to specifically update only the avatar URL\r\nexport async function updateAvatarUrl(\r\n  avatarUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"customer_profiles\")\r\n    .update({ avatar_url: avatarUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Avatar URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update avatar URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  // Revalidate paths to update the UI\r\n  revalidatePath('/dashboard/customer');\r\n  revalidatePath('/dashboard/customer/profile');\r\n\r\n  return { success: true };\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAOsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/avatar-actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { getCustomerAvatarPath } from '@/lib/utils/storage-paths';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// Action to upload avatar and return public URL\r\nexport async function uploadAvatarAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"avatarFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No avatar file provided.\" };\r\n  }\r\n\r\n  const allowedTypes = [\"image/png\", \"image/jpeg\", \"image/gif\", \"image/webp\"];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation (15MB limit)\r\n  if (file.size > 15 * 1024 * 1024) {\r\n    return { success: false, error: \"File size must be less than 15MB.\" };\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    const bucketName = \"customers\"; // Plural form - matches the bucket name\r\n    const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000); // Add random milliseconds for extra uniqueness\r\n    const fullPath = getCustomerAvatarPath(userId, timestamp);\r\n\r\n    // Upload the processed image\r\n    const { error: uploadError } = await supabase.storage\r\n      .from(bucketName)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Avatar Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload avatar: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // No need to add timestamp to URL as we already have it in the filename\r\n    const { data: urlData } = supabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n\r\n// Action to specifically update only the avatar URL\r\nexport async function updateAvatarUrl(\r\n  avatarUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"customer_profiles\")\r\n    .update({ avatar_url: avatarUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Avatar URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update avatar URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  // Revalidate paths to update the UI\r\n  revalidatePath('/dashboard/customer');\r\n  revalidatePath('/dashboard/customer/profile');\r\n\r\n  return { success: true };\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAqFsB,kBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/client-image-compression.ts"], "sourcesContent": ["/**\r\n * Client-side image compression using Canvas API\r\n * This avoids memory issues in serverless environments like Google Cloud Run\r\n */\r\n\r\nexport interface CompressionOptions {\r\n  targetSizeKB?: number;\r\n  maxDimension?: number;\r\n  quality?: number;\r\n  format?: \"webp\" | \"jpeg\" | \"png\";\r\n}\r\n\r\nexport interface CompressionResult {\r\n  blob: Blob;\r\n  finalSizeKB: number;\r\n  compressionRatio: number;\r\n  dimensions: { width: number; height: number };\r\n}\r\n\r\n/**\r\n * Compress image on client-side using Canvas API\r\n */\r\nexport async function compressImageClientSide(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const {\r\n    format = \"webp\",\r\n    targetSizeKB = 100,\r\n    maxDimension = 800,\r\n    quality: initialQuality = 0.8\r\n  } = options;\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      try {\r\n        const canvas = document.createElement('canvas');\r\n        const ctx = canvas.getContext('2d');\r\n        \r\n        if (!ctx) {\r\n          reject(new Error('Could not get canvas context'));\r\n          return;\r\n        }\r\n\r\n        // Calculate new dimensions\r\n        let { width, height } = img;\r\n        \r\n        if (width > maxDimension || height > maxDimension) {\r\n          if (width > height) {\r\n            height = (height * maxDimension) / width;\r\n            width = maxDimension;\r\n          } else {\r\n            width = (width * maxDimension) / height;\r\n            height = maxDimension;\r\n          }\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Draw and compress\r\n        ctx.drawImage(img, 0, 0, width, height);\r\n\r\n        // Try different quality levels until we hit target size\r\n        let quality = initialQuality;\r\n        let attempts = 0;\r\n        const maxAttempts = 5;\r\n\r\n        const tryCompress = () => {\r\n          canvas.toBlob((blob) => {\r\n            if (!blob) {\r\n              reject(new Error('Failed to create blob'));\r\n              return;\r\n            }\r\n\r\n            const sizeKB = blob.size / 1024;\r\n            \r\n            if (sizeKB <= targetSizeKB || attempts >= maxAttempts || quality <= 0.1) {\r\n              // Success or max attempts reached\r\n              const compressionRatio = file.size / blob.size;\r\n              resolve({\r\n                blob,\r\n                finalSizeKB: Math.round(sizeKB * 100) / 100,\r\n                compressionRatio: Math.round(compressionRatio * 100) / 100,\r\n                dimensions: { width, height }\r\n              });\r\n            } else {\r\n              // Try again with lower quality\r\n              attempts++;\r\n              quality = Math.max(0.1, quality - 0.15);\r\n              tryCompress();\r\n            }\r\n          }, `image/${format}`, quality);\r\n        };\r\n\r\n        tryCompress();\r\n      } catch (error) {\r\n        reject(error);\r\n      }\r\n    };\r\n\r\n    img.onerror = () => reject(new Error('Failed to load image'));\r\n    img.src = URL.createObjectURL(file);\r\n  });\r\n}\r\n\r\n/**\r\n * Ultra-aggressive client-side compression\r\n */\r\nexport async function compressImageUltraAggressiveClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const originalSizeMB = file.size / (1024 * 1024);\r\n  \r\n  // Auto-determine settings based on file size\r\n  let targetSizeKB = 100;\r\n  let maxDimension = 800;\r\n  let quality = 0.7;\r\n\r\n  if (originalSizeMB <= 2) {\r\n    quality = 0.7;\r\n    maxDimension = 800;\r\n    targetSizeKB = 90;\r\n  } else if (originalSizeMB <= 5) {\r\n    quality = 0.55;\r\n    maxDimension = 700;\r\n    targetSizeKB = 80;\r\n  } else if (originalSizeMB <= 10) {\r\n    quality = 0.45;\r\n    maxDimension = 600;\r\n    targetSizeKB = 70;\r\n  } else {\r\n    quality = 0.35;\r\n    maxDimension = 550;\r\n    targetSizeKB = 60;\r\n  }\r\n\r\n  return compressImageClientSide(file, {\r\n    ...options,\r\n    targetSizeKB: options.targetSizeKB || targetSizeKB,\r\n    maxDimension: options.maxDimension || maxDimension,\r\n    quality: options.quality || quality\r\n  });\r\n}\r\n\r\n/**\r\n * Moderate client-side compression\r\n */\r\nexport async function compressImageModerateClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  return compressImageClientSide(file, {\r\n    targetSizeKB: 200,\r\n    maxDimension: 800,\r\n    quality: 0.75,\r\n    ...options\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAmBM,eAAe,wBACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,EACJ,SAAS,MAAM,EACf,eAAe,GAAG,EAClB,eAAe,GAAG,EAClB,SAAS,iBAAiB,GAAG,EAC9B,GAAG;IAEJ,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,IAAI;gBACF,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,MAAM,MAAM,OAAO,UAAU,CAAC;gBAE9B,IAAI,CAAC,KAAK;oBACR,OAAO,IAAI,MAAM;oBACjB;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;gBAExB,IAAI,QAAQ,gBAAgB,SAAS,cAAc;oBACjD,IAAI,QAAQ,QAAQ;wBAClB,SAAS,AAAC,SAAS,eAAgB;wBACnC,QAAQ;oBACV,OAAO;wBACL,QAAQ,AAAC,QAAQ,eAAgB;wBACjC,SAAS;oBACX;gBACF;gBAEA,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAEhB,oBAAoB;gBACpB,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO;gBAEhC,wDAAwD;gBACxD,IAAI,UAAU;gBACd,IAAI,WAAW;gBACf,MAAM,cAAc;gBAEpB,MAAM,cAAc;oBAClB,OAAO,MAAM,CAAC,CAAC;wBACb,IAAI,CAAC,MAAM;4BACT,OAAO,IAAI,MAAM;4BACjB;wBACF;wBAEA,MAAM,SAAS,KAAK,IAAI,GAAG;wBAE3B,IAAI,UAAU,gBAAgB,YAAY,eAAe,WAAW,KAAK;4BACvE,kCAAkC;4BAClC,MAAM,mBAAmB,KAAK,IAAI,GAAG,KAAK,IAAI;4BAC9C,QAAQ;gCACN;gCACA,aAAa,KAAK,KAAK,CAAC,SAAS,OAAO;gCACxC,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;gCACvD,YAAY;oCAAE;oCAAO;gCAAO;4BAC9B;wBACF,OAAO;4BACL,+BAA+B;4BAC/B;4BACA,UAAU,KAAK,GAAG,CAAC,KAAK,UAAU;4BAClC;wBACF;oBACF,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE;gBACxB;gBAEA;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;QAEA,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;QACrC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAChC;AACF;AAKO,eAAe,mCACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,iBAAiB,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;IAE/C,6CAA6C;IAC7C,IAAI,eAAe;IACnB,IAAI,eAAe;IACnB,IAAI,UAAU;IAEd,IAAI,kBAAkB,GAAG;QACvB,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,GAAG;QAC9B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,IAAI;QAC/B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO;QACL,UAAU;QACV,eAAe;QACf,eAAe;IACjB;IAEA,OAAO,wBAAwB,MAAM;QACnC,GAAG,OAAO;QACV,cAAc,QAAQ,YAAY,IAAI;QACtC,cAAc,QAAQ,YAAY,IAAI;QACtC,SAAS,QAAQ,OAAO,IAAI;IAC9B;AACF;AAKO,eAAe,4BACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,OAAO,wBAAwB,MAAM;QACnC,cAAc;QACd,cAAc;QACd,SAAS;QACT,GAAG,OAAO;IACZ;AACF", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/hooks/useAvatarUpload.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useTransition } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { uploadAvatarAndGetUrl, updateAvatarUrl } from \"../avatar-actions\";\r\nimport { compressImageModerateClient } from \"@/lib/utils/client-image-compression\";\r\n\r\nexport type AvatarUploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\ninterface UseAvatarUploadOptions {\r\n  initialAvatarUrl?: string;\r\n  onUpdateAvatar: (_url: string) => void;\r\n}\r\n\r\nexport function useAvatarUpload({ onUpdateAvatar }: UseAvatarUploadOptions) {\r\n  const [avatarUploadStatus, setAvatarUploadStatus] =\r\n    useState<AvatarUploadStatus>(\"idle\");\r\n  const [avatarUploadError, setAvatarUploadError] = useState<string | null>(\r\n    null\r\n  );\r\n  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(null);\r\n  const [isAvatarUploading, startAvatarUploadTransition] = useTransition();\r\n  const [imageToCrop, setImageToCrop] = useState<string | null>(null);\r\n  const [originalFile, setOriginalFile] = useState<File | null>(null);\r\n\r\n  // File selection handler\r\n  const onFileSelect = (file: File | null) => {\r\n    if (localPreviewUrl) {\r\n      URL.revokeObjectURL(localPreviewUrl);\r\n      setLocalPreviewUrl(null);\r\n    }\r\n\r\n    if (file) {\r\n      if (file.size > 15 * 1024 * 1024) {\r\n        toast.error(\"File size must be less than 15MB.\");\r\n        setAvatarUploadStatus(\"idle\");\r\n        setAvatarUploadError(\"File size must be less than 15MB.\");\r\n        setLocalPreviewUrl(null);\r\n        return;\r\n      }\r\n\r\n      // Prepare for cropping\r\n      setOriginalFile(file);\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setImageToCrop(reader.result as string);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    } else {\r\n      setAvatarUploadStatus(\"idle\");\r\n      setAvatarUploadError(null);\r\n      setLocalPreviewUrl(null);\r\n    }\r\n  };\r\n\r\n  // Avatar upload handler\r\n  const handleAvatarUpload = async (file: File) => {\r\n    setAvatarUploadStatus(\"uploading\");\r\n    setAvatarUploadError(null);\r\n\r\n    startAvatarUploadTransition(async () => {\r\n      const formData = new FormData();\r\n      formData.append(\"avatarFile\", file);\r\n\r\n      const result = await uploadAvatarAndGetUrl(formData);\r\n\r\n      if (result.success && result.url) {\r\n        const newAvatarUrl = result.url;\r\n\r\n        // Update preview\r\n        setAvatarUploadStatus(\"success\");\r\n\r\n        // Clean up preview URL\r\n        setLocalPreviewUrl(null);\r\n        if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);\r\n\r\n        toast.success(\"Avatar uploaded successfully!\");\r\n\r\n        // Save URL to DB immediately\r\n        try {\r\n          const updateResult = await updateAvatarUrl(newAvatarUrl);\r\n          if (!updateResult.success) {\r\n            toast.error(\r\n              `Avatar uploaded, but failed to save URL: ${updateResult.error}`\r\n            );\r\n          }\r\n\r\n          // Update parent component state after successful DB save\r\n          if (updateResult.success) {\r\n            onUpdateAvatar(newAvatarUrl);\r\n          }\r\n        } catch (err) {\r\n          console.error(\"Error saving avatar URL:\", err);\r\n          toast.error(\"Error saving avatar URL after upload.\");\r\n        }\r\n      } else {\r\n        setAvatarUploadStatus(\"error\");\r\n        const errorMessage = result.error || \"Failed to upload avatar.\";\r\n        setAvatarUploadError(errorMessage);\r\n        setLocalPreviewUrl(null);\r\n        if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);\r\n\r\n        // Show user-friendly error message\r\n        if (errorMessage.includes(\"File size must be less than 15MB\")) {\r\n          toast.error(\"Image too large\", {\r\n            description: \"Please select an image smaller than 15MB\"\r\n          });\r\n        } else if (errorMessage.includes(\"Invalid file type\")) {\r\n          toast.error(\"Invalid file type\", {\r\n            description: \"Please select a JPG, PNG, WebP, or GIF image\"\r\n          });\r\n        } else {\r\n          toast.error(\"Upload failed\", {\r\n            description: errorMessage\r\n          });\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  // Handle crop completion\r\n  const handleCropComplete = async (croppedBlob: Blob | null) => {\r\n    setImageToCrop(null); // Close dialog\r\n\r\n    if (croppedBlob && originalFile) {\r\n      try {\r\n        // Convert blob to file for compression\r\n        const croppedFile = new File([croppedBlob], originalFile.name, {\r\n          type: \"image/png\", // Canvas outputs PNG\r\n        });\r\n\r\n        // Compress image on client-side first\r\n        const compressionResult = await compressImageModerateClient(croppedFile, {\r\n          maxDimension: 400,\r\n          targetSizeKB: 150\r\n        });\r\n\r\n        // Convert compressed blob back to file\r\n        const compressedFile = new File([compressionResult.blob], originalFile.name, {\r\n          type: compressionResult.blob.type\r\n        });\r\n\r\n        const previewUrl = URL.createObjectURL(compressedFile);\r\n        setLocalPreviewUrl(previewUrl);\r\n        handleAvatarUpload(compressedFile);\r\n      } catch (error) {\r\n        console.error(\"Image compression failed:\", error);\r\n        toast.error(\"Failed to process image. Please try a different image.\");\r\n        setOriginalFile(null);\r\n        const fileInput = document.querySelector(\r\n          'input[type=\"file\"]'\r\n        ) as HTMLInputElement;\r\n        if (fileInput) fileInput.value = \"\";\r\n      }\r\n    } else {\r\n      // Handle crop cancellation or error\r\n      console.log(\"Cropping cancelled or failed.\");\r\n      setOriginalFile(null);\r\n      const fileInput = document.querySelector(\r\n        'input[type=\"file\"]'\r\n      ) as HTMLInputElement;\r\n      if (fileInput) fileInput.value = \"\";\r\n    }\r\n  };\r\n\r\n  // Handle crop dialog close\r\n  const handleCropDialogClose = () => {\r\n    setImageToCrop(null);\r\n    setOriginalFile(null);\r\n    // Clear the file input\r\n    const fileInput = document.querySelector(\r\n      'input[type=\"file\"]'\r\n    ) as HTMLInputElement;\r\n    if (fileInput) fileInput.value = \"\";\r\n  };\r\n\r\n  // Avatar error display component\r\n  const avatarErrorDisplay =\r\n    avatarUploadStatus === \"error\" && avatarUploadError\r\n      ? avatarUploadError\r\n      : null;\r\n\r\n  return {\r\n    avatarUploadStatus,\r\n    avatarUploadError,\r\n    localPreviewUrl,\r\n    isAvatarUploading,\r\n    imageToCrop,\r\n    onFileSelect,\r\n    handleAvatarUpload,\r\n    handleCropComplete,\r\n    handleCropDialogClose,\r\n    avatarErrorDisplay,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;AAcO,SAAS,gBAAgB,EAAE,cAAc,EAA0B;IACxE,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,mBAAmB,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,IAAI,iBAAiB;YACnB,IAAI,eAAe,CAAC;YACpB,mBAAmB;QACrB;QAEA,IAAI,MAAM;YACR,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;gBAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,sBAAsB;gBACtB,qBAAqB;gBACrB,mBAAmB;gBACnB;YACF;YAEA,uBAAuB;YACvB,gBAAgB;YAChB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,eAAe,OAAO,MAAM;YAC9B;YACA,OAAO,aAAa,CAAC;QACvB,OAAO;YACL,sBAAsB;YACtB,qBAAqB;YACrB,mBAAmB;QACrB;IACF;IAEA,wBAAwB;IACxB,MAAM,qBAAqB,OAAO;QAChC,sBAAsB;QACtB,qBAAqB;QAErB,4BAA4B;YAC1B,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,cAAc;YAE9B,MAAM,SAAS,MAAM,CAAA,GAAA,gMAAA,CAAA,wBAAqB,AAAD,EAAE;YAE3C,IAAI,OAAO,OAAO,IAAI,OAAO,GAAG,EAAE;gBAChC,MAAM,eAAe,OAAO,GAAG;gBAE/B,iBAAiB;gBACjB,sBAAsB;gBAEtB,uBAAuB;gBACvB,mBAAmB;gBACnB,IAAI,iBAAiB,IAAI,eAAe,CAAC;gBAEzC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,6BAA6B;gBAC7B,IAAI;oBACF,MAAM,eAAe,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;oBAC3C,IAAI,CAAC,aAAa,OAAO,EAAE;wBACzB,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,yCAAyC,EAAE,aAAa,KAAK,EAAE;oBAEpE;oBAEA,yDAAyD;oBACzD,IAAI,aAAa,OAAO,EAAE;wBACxB,eAAe;oBACjB;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF,OAAO;gBACL,sBAAsB;gBACtB,MAAM,eAAe,OAAO,KAAK,IAAI;gBACrC,qBAAqB;gBACrB,mBAAmB;gBACnB,IAAI,iBAAiB,IAAI,eAAe,CAAC;gBAEzC,mCAAmC;gBACnC,IAAI,aAAa,QAAQ,CAAC,qCAAqC;oBAC7D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,mBAAmB;wBAC7B,aAAa;oBACf;gBACF,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB;oBACrD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qBAAqB;wBAC/B,aAAa;oBACf;gBACF,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB;wBAC3B,aAAa;oBACf;gBACF;YACF;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,qBAAqB,OAAO;QAChC,eAAe,OAAO,eAAe;QAErC,IAAI,eAAe,cAAc;YAC/B,IAAI;gBACF,uCAAuC;gBACvC,MAAM,cAAc,IAAI,KAAK;oBAAC;iBAAY,EAAE,aAAa,IAAI,EAAE;oBAC7D,MAAM;gBACR;gBAEA,sCAAsC;gBACtC,MAAM,oBAAoB,MAAM,CAAA,GAAA,8IAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa;oBACvE,cAAc;oBACd,cAAc;gBAChB;gBAEA,uCAAuC;gBACvC,MAAM,iBAAiB,IAAI,KAAK;oBAAC,kBAAkB,IAAI;iBAAC,EAAE,aAAa,IAAI,EAAE;oBAC3E,MAAM,kBAAkB,IAAI,CAAC,IAAI;gBACnC;gBAEA,MAAM,aAAa,IAAI,eAAe,CAAC;gBACvC,mBAAmB;gBACnB,mBAAmB;YACrB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB,MAAM,YAAY,SAAS,aAAa,CACtC;gBAEF,IAAI,WAAW,UAAU,KAAK,GAAG;YACnC;QACF,OAAO;YACL,oCAAoC;YACpC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,MAAM,YAAY,SAAS,aAAa,CACtC;YAEF,IAAI,WAAW,UAAU,KAAK,GAAG;QACnC;IACF;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB;QAC5B,eAAe;QACf,gBAAgB;QAChB,uBAAuB;QACvB,MAAM,YAAY,SAAS,aAAa,CACtC;QAEF,IAAI,WAAW,UAAU,KAAK,GAAG;IACnC;IAEA,iCAAiC;IACjC,MAAM,qBACJ,uBAAuB,WAAW,oBAC9B,oBACA;IAEN,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\ninterface DialogContentProps extends React.ComponentProps<typeof DialogPrimitive.Content> {\r\n  hideClose?: boolean;\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  hideClose = false,\r\n  ...props\r\n}: DialogContentProps) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {!hideClose && (\r\n          <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer\">\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAMA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,YAAY,KAAK,EACjB,GAAG,OACgB;IACnB,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,CAAC,2BACA,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Slider({\r\n  className,\r\n  defaultValue,\r\n  value,\r\n  min = 0,\r\n  max = 100,\r\n  ...props\r\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\r\n  const _values = React.useMemo(\r\n    () =>\r\n      Array.isArray(value)\r\n        ? value\r\n        : Array.isArray(defaultValue)\r\n          ? defaultValue\r\n          : [min, max],\r\n    [value, defaultValue, min, max]\r\n  )\r\n\r\n  return (\r\n    <SliderPrimitive.Root\r\n      data-slot=\"slider\"\r\n      defaultValue={defaultValue}\r\n      value={value}\r\n      min={min}\r\n      max={max}\r\n      className={cn(\r\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SliderPrimitive.Track\r\n        data-slot=\"slider-track\"\r\n        className={cn(\r\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\r\n        )}\r\n      >\r\n        <SliderPrimitive.Range\r\n          data-slot=\"slider-range\"\r\n          className={cn(\r\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\r\n          )}\r\n        />\r\n      </SliderPrimitive.Track>\r\n      {Array.from({ length: _values.length }, (_, index) => (\r\n        <SliderPrimitive.Thumb\r\n          data-slot=\"slider-thumb\"\r\n          key={index}\r\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\r\n        />\r\n      ))}\r\n    </SliderPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Slider }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;IAClD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC1B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;YAAC;YAAK;SAAI,EAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/ImageCropDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback, useEffect } from \"react\"; // Added React and useEffect import\r\nimport <PERSON><PERSON><PERSON>, { Point, Area } from \"react-easy-crop\"; // Import types directly\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>alog<PERSON>ontent,\r\n  <PERSON><PERSON>Header,\r\n  <PERSON><PERSON><PERSON><PERSON>le,\r\n  DialogFooter,\r\n} from \"@/components/ui/dialog\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { Slider } from \"@/components/ui/slider\"; // Import Slider\r\n\r\n// Helper function to create an image element\r\nconst createImage = (url: string): Promise<HTMLImageElement> =>\r\n  new Promise((resolve, reject) => {\r\n    const image = new Image();\r\n    image.addEventListener(\"load\", () => resolve(image));\r\n    image.addEventListener(\"error\", (error) => reject(error));\r\n    image.setAttribute(\"crossOrigin\", \"anonymous\"); // needed to avoid cross-origin issues\r\n    image.src = url;\r\n  });\r\n\r\n// Helper function to get the cropped image blob\r\nasync function getCroppedImgBlob(\r\n  imageSrc: string,\r\n  pixelCrop: Area\r\n): Promise<Blob | null> {\r\n  const image = await createImage(imageSrc);\r\n  const canvas = document.createElement(\"canvas\");\r\n  const ctx = canvas.getContext(\"2d\");\r\n\r\n  if (!ctx) {\r\n    return null;\r\n  }\r\n\r\n  const scaleX = image.naturalWidth / image.width;\r\n  const scaleY = image.naturalHeight / image.height;\r\n  const pixelRatio = window.devicePixelRatio || 1;\r\n\r\n  canvas.width = pixelCrop.width * pixelRatio * scaleX;\r\n  canvas.height = pixelCrop.height * pixelRatio * scaleY;\r\n\r\n  ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\r\n  ctx.imageSmoothingQuality = \"high\";\r\n\r\n  ctx.drawImage(\r\n    image,\r\n    pixelCrop.x * scaleX,\r\n    pixelCrop.y * scaleY,\r\n    pixelCrop.width * scaleX,\r\n    pixelCrop.height * scaleY,\r\n    0,\r\n    0,\r\n    pixelCrop.width * scaleX,\r\n    pixelCrop.height * scaleY\r\n  );\r\n\r\n  return new Promise((resolve) => {\r\n    canvas.toBlob(\r\n      resolve, // Pass resolve directly as the callback\r\n       \"image/png\" // Output as PNG from canvas\r\n       // Quality parameter is not applicable for PNG\r\n     );\r\n  });\r\n}\r\n\r\ninterface ImageCropDialogProps {\r\n  imgSrc: string | null;\r\n  onCropComplete: (_blob: Blob | null) => void; // Disabled warning for unused type param\r\n  onClose: () => void;\r\n  isOpen: boolean;\r\n}\r\n\r\nexport default function ImageCropDialog({\r\n  imgSrc,\r\n  onCropComplete,\r\n  onClose,\r\n  isOpen,\r\n}: ImageCropDialogProps) {\r\n  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });\r\n  const [zoom, setZoom] = useState(1);\r\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);\r\n  const [isCropping, setIsCropping] = useState(false);\r\n\r\n  const onCropCompleteCallback = useCallback((_croppedArea: Area, croppedAreaPixels: Area) => {\r\n    setCroppedAreaPixels(croppedAreaPixels);\r\n  }, []);\r\n\r\n  const handleCrop = async () => {\r\n    if (!imgSrc || !croppedAreaPixels) {\r\n      console.warn(\"Image source or crop area not available.\");\r\n      onCropComplete(null);\r\n      return;\r\n    }\r\n    setIsCropping(true);\r\n    try {\r\n      const croppedBlob = await getCroppedImgBlob(imgSrc, croppedAreaPixels);\r\n      onCropComplete(croppedBlob);\r\n    } catch (e) {\r\n      console.error(\"Error cropping image:\", e);\r\n      onCropComplete(null); // Indicate error\r\n    } finally {\r\n      setIsCropping(false);\r\n    }\r\n  };\r\n\r\n  // Reset zoom when dialog opens using useEffect\r\n  useEffect(() => { // Use useEffect directly after importing React\r\n    if (isOpen) {\r\n      setZoom(1);\r\n    }\r\n  }, [isOpen]); // Add isOpen as a dependency\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"sm:max-w-[600px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Crop Your Logo</DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800\">\r\n          {imgSrc ? (\r\n            <Cropper\r\n              image={imgSrc}\r\n              crop={crop}\r\n              zoom={zoom}\r\n              aspect={1} // 1:1 aspect ratio\r\n              cropShape=\"round\" // Make the crop area round\r\n              showGrid={false}\r\n              onCropChange={setCrop}\r\n              onZoomChange={setZoom}\r\n              onCropComplete={onCropCompleteCallback}\r\n            />\r\n          ) : (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <p>Loading image...</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n         {/* Zoom Slider */}\r\n         <div className=\"px-4 pb-4\">\r\n           <Slider\r\n             min={1}\r\n              max={3}\r\n              step={0.1}\r\n              value={[zoom]}\r\n              onValueChange={(value: number[]) => setZoom(value[0])} // Added type for value\r\n              className=\"w-full\"\r\n              aria-label=\"Zoom slider\"\r\n           />\r\n         </div>\r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={onClose} disabled={isCropping}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleCrop}\r\n            disabled={isCropping}\r\n            className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]\"\r\n          >\r\n            {isCropping ? (\r\n              <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n            ) : null}\r\n            Crop Image\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  ); // Ensure the function closing brace and semicolon are correct\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,oVAAiE,mCAAmC;AACpG,yQAAwD,wBAAwB;AAChF;AACA;AAOA;AACA,wNAAiD,gBAAgB;AAbjE;;;;;;;;AAeA,6CAA6C;AAC7C,MAAM,cAAc,CAAC,MACnB,IAAI,QAAQ,CAAC,SAAS;QACpB,MAAM,QAAQ,IAAI;QAClB,MAAM,gBAAgB,CAAC,QAAQ,IAAM,QAAQ;QAC7C,MAAM,gBAAgB,CAAC,SAAS,CAAC,QAAU,OAAO;QAClD,MAAM,YAAY,CAAC,eAAe,cAAc,sCAAsC;QACtF,MAAM,GAAG,GAAG;IACd;AAEF,gDAAgD;AAChD,eAAe,kBACb,QAAgB,EAChB,SAAe;IAEf,MAAM,QAAQ,MAAM,YAAY;IAChC,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,MAAM,MAAM,OAAO,UAAU,CAAC;IAE9B,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IAEA,MAAM,SAAS,MAAM,YAAY,GAAG,MAAM,KAAK;IAC/C,MAAM,SAAS,MAAM,aAAa,GAAG,MAAM,MAAM;IACjD,MAAM,aAAa,OAAO,gBAAgB,IAAI;IAE9C,OAAO,KAAK,GAAG,UAAU,KAAK,GAAG,aAAa;IAC9C,OAAO,MAAM,GAAG,UAAU,MAAM,GAAG,aAAa;IAEhD,IAAI,YAAY,CAAC,YAAY,GAAG,GAAG,YAAY,GAAG;IAClD,IAAI,qBAAqB,GAAG;IAE5B,IAAI,SAAS,CACX,OACA,UAAU,CAAC,GAAG,QACd,UAAU,CAAC,GAAG,QACd,UAAU,KAAK,GAAG,QAClB,UAAU,MAAM,GAAG,QACnB,GACA,GACA,UAAU,KAAK,GAAG,QAClB,UAAU,MAAM,GAAG;IAGrB,OAAO,IAAI,QAAQ,CAAC;QAClB,OAAO,MAAM,CACX,SACC,YAAY,4BAA4B;;IAG7C;AACF;AASe,SAAS,gBAAgB,EACtC,MAAM,EACN,cAAc,EACd,OAAO,EACP,MAAM,EACe;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;QAAE,GAAG;QAAG,GAAG;IAAE;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,cAAoB;QAC9D,qBAAqB;IACvB,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,CAAC,mBAAmB;YACjC,QAAQ,IAAI,CAAC;YACb,eAAe;YACf;QACF;QACA,cAAc;QACd,IAAI;YACF,MAAM,cAAc,MAAM,kBAAkB,QAAQ;YACpD,eAAe;QACjB,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,yBAAyB;YACvC,eAAe,OAAO,iBAAiB;QACzC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,QAAQ;QACV;IACF,GAAG;QAAC;KAAO,GAAG,6BAA6B;IAE3C,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,8OAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,2HAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,2HAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,8OAAC;oBAAI,WAAU;8BACZ,uBACC,8OAAC,wJAAA,CAAA,UAAO;wBACN,OAAO;wBACP,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,WAAU,QAAQ,2BAA2B;;wBAC7C,UAAU;wBACV,cAAc;wBACd,cAAc;wBACd,gBAAgB;;;;;6CAGlB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;8BAKR,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;wBACL,KAAK;wBACJ,KAAK;wBACL,MAAM;wBACN,OAAO;4BAAC;yBAAK;wBACb,eAAe,CAAC,QAAoB,QAAQ,KAAK,CAAC,EAAE;wBACpD,WAAU;wBACV,cAAW;;;;;;;;;;;8BAGjB,8OAAC,2HAAA,CAAA,eAAY;;sCACX,8OAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAY;;;;;;sCAGlE,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;;gCAET,2BACC,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;2CACjB;gCAAK;;;;;;;;;;;;;;;;;;;;;;;cAMhB,8DAA8D;AACnE", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/AvatarUpload.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Camera, Loader2 } from \"lucide-react\";\r\nimport { useAvatarUpload } from \"../hooks/useAvatarUpload\";\r\nimport ImageCropDialog from \"@/app/(dashboard)/dashboard/business/card/components/ImageCropDialog\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface AvatarUploadProps {\r\n  initialAvatarUrl?: string;\r\n  userName?: string | null;\r\n  onUpdateAvatar: (_url: string) => void;\r\n}\r\n\r\nexport default function AvatarUpload({\r\n  initialAvatarUrl,\r\n  userName,\r\n  onUpdateAvatar,\r\n}: AvatarUploadProps) {\r\n  const {\r\n    localPreviewUrl,\r\n    isAvatarUploading,\r\n    imageToCrop,\r\n    onFileSelect,\r\n    handleCropComplete,\r\n    handleCropDialogClose,\r\n    avatarErrorDisplay,\r\n  } = useAvatarUpload({\r\n    initialAvatarUrl,\r\n    onUpdateAvatar,\r\n  });\r\n\r\n  // Generate initials from name\r\n  const getInitials = (name?: string | null) => {\r\n    if (!name) return \"U\";\r\n\r\n    const parts = name.split(/\\s+/);\r\n    if (parts.length === 1) {\r\n      return name.substring(0, 2).toUpperCase();\r\n    }\r\n\r\n    return (\r\n      parts[0].charAt(0) + parts[parts.length - 1].charAt(0)\r\n    ).toUpperCase();\r\n  };\r\n\r\n  const initials = getInitials(userName);\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-4\">\r\n      <motion.div\r\n        className=\"relative\"\r\n        whileHover={{ scale: 1.05 }}\r\n        transition={{ type: \"spring\", stiffness: 300, damping: 15 }}\r\n      >\r\n        <Avatar className={cn(\r\n          \"h-24 w-24\",\r\n          \"border-2 border-[var(--brand-gold)]\",\r\n          \"shadow-lg\",\r\n          \"ring-2 ring-[var(--brand-gold)]/20\",\r\n          \"transition-all duration-300\"\r\n        )}>\r\n          {(localPreviewUrl || initialAvatarUrl) ? (\r\n            <AvatarImage\r\n              src={localPreviewUrl || initialAvatarUrl}\r\n              alt={userName || \"User\"}\r\n            />\r\n          ) : null}\r\n          <AvatarFallback className={cn(\r\n            \"bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900\",\r\n            \"text-blue-800 dark:text-blue-200 text-xl\"\r\n          )}>\r\n            {initials}\r\n          </AvatarFallback>\r\n        </Avatar>\r\n\r\n        <motion.label\r\n          htmlFor=\"avatar-upload\"\r\n          className={cn(\r\n            \"absolute bottom-0 right-0 p-1.5 rounded-full\",\r\n            \"bg-[var(--brand-gold)] text-white cursor-pointer\",\r\n            \"hover:bg-[var(--brand-gold)]/90 transition-colors\",\r\n            \"shadow-md\"\r\n          )}\r\n          whileHover={{ scale: 1.1 }}\r\n          whileTap={{ scale: 0.95 }}\r\n        >\r\n          <Camera className=\"h-4 w-4\" />\r\n          <span className=\"sr-only\">Upload avatar</span>\r\n        </motion.label>\r\n\r\n        <Input\r\n          id=\"avatar-upload\"\r\n          type=\"file\"\r\n          accept=\"image/png, image/jpeg, image/gif, image/webp\"\r\n          className=\"hidden\"\r\n          onChange={(e) => onFileSelect(e.target.files?.[0] || null)}\r\n          disabled={isAvatarUploading}\r\n        />\r\n      </motion.div>\r\n\r\n      {isAvatarUploading && (\r\n        <div className=\"flex items-center text-sm text-neutral-500 dark:text-neutral-400\">\r\n          <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n          Uploading...\r\n        </div>\r\n      )}\r\n\r\n      {avatarErrorDisplay && (\r\n        <div className=\"text-sm text-red-500\">{avatarErrorDisplay}</div>\r\n      )}\r\n\r\n      {/* Image Crop Dialog */}\r\n      <ImageCropDialog\r\n        isOpen={!!imageToCrop}\r\n        imgSrc={imageToCrop}\r\n        onCropComplete={handleCropComplete}\r\n        onClose={handleCropDialogClose}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAiBe,SAAS,aAAa,EACnC,gBAAgB,EAChB,QAAQ,EACR,cAAc,EACI;IAClB,MAAM,EACJ,eAAe,EACf,iBAAiB,EACjB,WAAW,EACX,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,kBAAkB,EACnB,GAAG,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE;QAClB;QACA;IACF;IAEA,8BAA8B;IAC9B,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,KAAK,SAAS,CAAC,GAAG,GAAG,WAAW;QACzC;QAEA,OAAO,CACL,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,EACtD,EAAE,WAAW;IACf;IAEA,MAAM,WAAW,YAAY;IAE7B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;;kCAE1D,8OAAC,2HAAA,CAAA,SAAM;wBAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAClB,aACA,uCACA,aACA,sCACA;;4BAEE,mBAAmB,iCACnB,8OAAC,2HAAA,CAAA,cAAW;gCACV,KAAK,mBAAmB;gCACxB,KAAK,YAAY;;;;;uCAEjB;0CACJ,8OAAC,2HAAA,CAAA,iBAAc;gCAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAC1B,mFACA;0CAEC;;;;;;;;;;;;kCAIL,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;wBACX,SAAQ;wBACR,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gDACA,oDACA,qDACA;wBAEF,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAK;;0CAExB,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAG5B,8OAAC,0HAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,QAAO;wBACP,WAAU;wBACV,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;wBACrD,UAAU;;;;;;;;;;;;YAIb,mCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA8B;;;;;;;YAKpD,oCACC,8OAAC;gBAAI,WAAU;0BAAwB;;;;;;0BAIzC,8OAAC,uLAAA,CAAA,UAAe;gBACd,QAAQ,CAAC,CAAC;gBACV,QAAQ;gBACR,gBAAgB;gBAChB,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/ProfileRequirementDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useSearchParams, useRouter } from \"next/navigation\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { AlertCircle, Mail, Phone, MapPin, CheckCircle } from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface ProfileRequirementDialogProps {\r\n  hasCompleteAddress?: boolean;\r\n}\r\n\r\nexport default function ProfileRequirementDialog({\r\n  hasCompleteAddress = false,\r\n}: ProfileRequirementDialogProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [missingFields, setMissingFields] = useState<string[]>([]);\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    // Check URL parameters for missing fields\r\n    const missingParam = searchParams.get(\"missing\");\r\n    const messageParam = searchParams.get(\"message\");\r\n    \r\n    if (missingParam || messageParam) {\r\n      const fields = missingParam ? missingParam.split(\",\") : [];\r\n      \r\n      // Also check current state to determine what's actually missing\r\n      const actuallyMissing: string[] = [];\r\n      if (!hasCompleteAddress) actuallyMissing.push(\"address\");\r\n      \r\n      // Use the more comprehensive list\r\n      const finalMissing = actuallyMissing.length > 0 ? actuallyMissing : fields;\r\n      \r\n      setMissingFields(finalMissing);\r\n      setIsOpen(true);\r\n      \r\n      // Clean up URL parameters\r\n      const newUrl = window.location.pathname;\r\n      router.replace(newUrl, { scroll: false });\r\n    }\r\n  }, [searchParams, hasCompleteAddress, router]);\r\n\r\n  const getFieldInfo = (field: string) => {\r\n    switch (field) {\r\n      case \"email\":\r\n        return {\r\n          icon: <Mail className=\"w-5 h-5\" />,\r\n          label: \"Email Address\",\r\n          description: \"Required for account notifications and password reset\",\r\n          color: \"text-blue-600 dark:text-blue-400\",\r\n          bgColor: \"bg-blue-100 dark:bg-blue-900/30\",\r\n        };\r\n      case \"phone\":\r\n        return {\r\n          icon: <Phone className=\"w-5 h-5\" />,\r\n          label: \"Mobile Number\",\r\n          description: \"Required for account access and verification\",\r\n          color: \"text-green-600 dark:text-green-400\",\r\n          bgColor: \"bg-green-100 dark:bg-green-900/30\",\r\n        };\r\n      case \"address\":\r\n        return {\r\n          icon: <MapPin className=\"w-5 h-5\" />,\r\n          label: \"Address Information\",\r\n          description: \"Required for location-based services\",\r\n          color: \"text-purple-600 dark:text-purple-400\",\r\n          bgColor: \"bg-purple-100 dark:bg-purple-900/30\",\r\n        };\r\n      default:\r\n        return {\r\n          icon: <AlertCircle className=\"w-5 h-5\" />,\r\n          label: field,\r\n          description: \"Required information\",\r\n          color: \"text-gray-600 dark:text-gray-400\",\r\n          bgColor: \"bg-gray-100 dark:bg-gray-900/30\",\r\n        };\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setIsOpen(false);\r\n  };\r\n\r\n  if (missingFields.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2 text-lg font-semibold\">\r\n            <div className=\"p-2 rounded-full bg-amber-100 dark:bg-amber-900/30\">\r\n              <AlertCircle className=\"w-5 h-5 text-amber-600 dark:text-amber-400\" />\r\n            </div>\r\n            Complete Your Profile\r\n          </DialogTitle>\r\n          <DialogDescription className=\"text-sm text-muted-foreground\">\r\n            Please add the following required information to continue using the dashboard.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-3 my-4\">\r\n          {missingFields.map((field, index) => {\r\n            const fieldInfo = getFieldInfo(field);\r\n            return (\r\n              <motion.div\r\n                key={field}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n                className={`flex items-start gap-3 p-3 rounded-lg border ${fieldInfo.bgColor} border-opacity-50`}\r\n              >\r\n                <div className={`p-1.5 rounded-lg ${fieldInfo.bgColor} ${fieldInfo.color}`}>\r\n                  {fieldInfo.icon}\r\n                </div>\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <h4 className=\"text-sm font-medium text-foreground\">\r\n                    {fieldInfo.label}\r\n                  </h4>\r\n                  <p className=\"text-xs text-muted-foreground mt-1\">\r\n                    {fieldInfo.description}\r\n                  </p>\r\n                </div>\r\n              </motion.div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        <div className=\"flex flex-col gap-2\">\r\n          <Button onClick={handleClose} className=\"w-full\">\r\n            <CheckCircle className=\"w-4 h-4 mr-2\" />\r\n            Got it, let me update my profile\r\n          </Button>\r\n          <p className=\"text-xs text-center text-muted-foreground\">\r\n            You can update these details in the forms below\r\n          </p>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAbA;;;;;;;;AAmBe,SAAS,yBAAyB,EAC/C,qBAAqB,KAAK,EACI;IAC9B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0CAA0C;QAC1C,MAAM,eAAe,aAAa,GAAG,CAAC;QACtC,MAAM,eAAe,aAAa,GAAG,CAAC;QAEtC,IAAI,gBAAgB,cAAc;YAChC,MAAM,SAAS,eAAe,aAAa,KAAK,CAAC,OAAO,EAAE;YAE1D,gEAAgE;YAChE,MAAM,kBAA4B,EAAE;YACpC,IAAI,CAAC,oBAAoB,gBAAgB,IAAI,CAAC;YAE9C,kCAAkC;YAClC,MAAM,eAAe,gBAAgB,MAAM,GAAG,IAAI,kBAAkB;YAEpE,iBAAiB;YACjB,UAAU;YAEV,0BAA0B;YAC1B,MAAM,SAAS,OAAO,QAAQ,CAAC,QAAQ;YACvC,OAAO,OAAO,CAAC,QAAQ;gBAAE,QAAQ;YAAM;QACzC;IACF,GAAG;QAAC;QAAc;QAAoB;KAAO;IAE7C,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,SAAS;gBACX;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,SAAS;gBACX;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,8OAAC,0MAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACxB,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,SAAS;gBACX;YACF;gBACE,OAAO;oBACL,oBAAM,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAC7B,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,SAAS;gBACX;QACJ;IACF;IAEA,MAAM,cAAc;QAClB,UAAU;IACZ;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,2HAAA,CAAA,eAAY;;sCACX,8OAAC,2HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;gCACnB;;;;;;;sCAGR,8OAAC,2HAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAgC;;;;;;;;;;;;8BAK/D,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,OAAO;wBACzB,MAAM,YAAY,aAAa;wBAC/B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAW,CAAC,6CAA6C,EAAE,UAAU,OAAO,CAAC,kBAAkB,CAAC;;8CAEhG,8OAAC;oCAAI,WAAW,CAAC,iBAAiB,EAAE,UAAU,OAAO,CAAC,CAAC,EAAE,UAAU,KAAK,EAAE;8CACvE,UAAU,IAAI;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,UAAU,KAAK;;;;;;sDAElB,8OAAC;4CAAE,WAAU;sDACV,UAAU,WAAW;;;;;;;;;;;;;2BAdrB;;;;;oBAmBX;;;;;;8BAGF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,2HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAa,WAAU;;8CACtC,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAG1C,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;;;;;;;;;;;;AAOnE", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/ProfilePageClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { User } from \"lucide-react\";\r\nimport AvatarUpload from \"./AvatarUpload\";\r\nimport ProfileRequirementDialog from \"./ProfileRequirementDialog\";\r\nimport UnifiedProfileForm from \"./UnifiedProfileForm\";\r\n\r\ninterface ProfilePageClientProps {\r\n  initialName: string | null;\r\n  initialAvatarUrl?: string | null;\r\n  initialAddressData?: {\r\n    address?: string | null;\r\n    pincode?: string | null;\r\n    city?: string | null;\r\n    state?: string | null;\r\n    locality?: string | null;\r\n  } | null;\r\n  hasCompleteAddress?: boolean;\r\n}\r\n\r\nexport default function ProfilePageClient({\r\n  initialName,\r\n  initialAvatarUrl,\r\n  initialAddressData,\r\n  hasCompleteAddress = false,\r\n}: ProfilePageClientProps) {\r\n  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(initialAvatarUrl || undefined);\r\n\r\n  return (\r\n    <>\r\n      {/* Profile Requirement Dialog */}\r\n      <ProfileRequirementDialog\r\n        hasCompleteAddress={hasCompleteAddress}\r\n      />\r\n\r\n      <div className=\"space-y-8\">\r\n        {/* Header Section */}\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n            <User className=\"w-6 h-6 text-foreground\" />\r\n          </div>\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold text-foreground\">\r\n              Profile Information\r\n            </h1>\r\n            <p className=\"text-muted-foreground mt-1\">\r\n              Update your personal details and address\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content Section */}\r\n        <div className=\"space-y-8\">\r\n          {/* Avatar Upload Section */}\r\n          <div className=\"flex justify-center\">\r\n            <AvatarUpload\r\n              initialAvatarUrl={avatarUrl}\r\n              userName={initialName}\r\n              onUpdateAvatar={(url) => setAvatarUrl(url)}\r\n            />\r\n          </div>\r\n\r\n          {/* Unified Profile and Address Form */}\r\n          <UnifiedProfileForm\r\n            initialName={initialName}\r\n            initialAddressData={initialAddressData}\r\n            avatarUrl={avatarUrl}\r\n          />\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;;;;AALA;;;;;;;AAqBe,SAAS,kBAAkB,EACxC,WAAW,EACX,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,KAAK,EACH;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,oBAAoB;IAEnF,qBACE;;0BAEE,8OAAC,mMAAA,CAAA,UAAwB;gBACvB,oBAAoB;;;;;;0BAGtB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAO9C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,uLAAA,CAAA,UAAY;oCACX,kBAAkB;oCAClB,UAAU;oCACV,gBAAgB,CAAC,MAAQ,aAAa;;;;;;;;;;;0CAK1C,8OAAC;gCACC,aAAa;gCACb,oBAAoB;gCACpB,WAAW;;;;;;;;;;;;;;;;;;;;AAMvB", "debugId": null}}]}