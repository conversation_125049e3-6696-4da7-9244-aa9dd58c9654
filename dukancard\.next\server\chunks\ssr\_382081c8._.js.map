{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/addressValidation.ts"], "sourcesContent": ["/**\r\n * Customer address validation utility\r\n * Checks if customer has complete address information\r\n */\r\n\r\nexport interface CustomerAddressData {\r\n  pincode?: string | null;\r\n  state?: string | null;\r\n  city?: string | null;\r\n  locality?: string | null;\r\n  // address is optional as per requirements\r\n  address?: string | null;\r\n}\r\n\r\nexport interface CustomerProfileData extends CustomerAddressData {\r\n  name?: string | null;\r\n}\r\n\r\n/**\r\n * Validates if customer address is complete\r\n * Address field is optional, but pincode, state, city, and locality are required\r\n */\r\nexport function isCustomerAddressComplete(addressData: CustomerAddressData): boolean {\r\n  const { pincode, state, city, locality } = addressData;\r\n\r\n  // Check if required fields are present and not empty\r\n  return !!(\r\n    pincode && pincode.trim() !== '' &&\r\n    state && state.trim() !== '' &&\r\n    city && city.trim() !== '' &&\r\n    locality && locality.trim() !== ''\r\n  );\r\n}\r\n\r\n/**\r\n * Gets missing address fields for customer\r\n */\r\nexport function getMissingAddressFields(addressData: CustomerAddressData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  if (!addressData.pincode || addressData.pincode.trim() === '') {\r\n    missing.push('pincode');\r\n  }\r\n  if (!addressData.state || addressData.state.trim() === '') {\r\n    missing.push('state');\r\n  }\r\n  if (!addressData.city || addressData.city.trim() === '') {\r\n    missing.push('city');\r\n  }\r\n  if (!addressData.locality || addressData.locality.trim() === '') {\r\n    missing.push('locality');\r\n  }\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing address fields\r\n */\r\nexport function getAddressValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n\r\n/**\r\n * Validates if customer name is complete\r\n */\r\nexport function isCustomerNameComplete(name?: string | null): boolean {\r\n  return !!(name && name.trim() !== '');\r\n}\r\n\r\n/**\r\n * Validates if customer profile is complete (both name and address)\r\n */\r\nexport function isCustomerProfileComplete(profileData: CustomerProfileData): boolean {\r\n  return isCustomerNameComplete(profileData.name) && isCustomerAddressComplete(profileData);\r\n}\r\n\r\n/**\r\n * Gets missing profile fields for customer (name + address)\r\n */\r\nexport function getMissingProfileFields(profileData: CustomerProfileData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  // Check name\r\n  if (!isCustomerNameComplete(profileData.name)) {\r\n    missing.push('name');\r\n  }\r\n\r\n  // Check address fields\r\n  const missingAddressFields = getMissingAddressFields(profileData);\r\n  missing.push(...missingAddressFields);\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing profile fields (name + address)\r\n */\r\nexport function getProfileValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'name': return 'Name';\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAmBM,SAAS,0BAA0B,WAAgC;IACxE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;IAE3C,qDAAqD;IACrD,OAAO,CAAC,CAAC,CACP,WAAW,QAAQ,IAAI,OAAO,MAC9B,SAAS,MAAM,IAAI,OAAO,MAC1B,QAAQ,KAAK,IAAI,OAAO,MACxB,YAAY,SAAS,IAAI,OAAO,EAClC;AACF;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,IAAI,CAAC,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,IAAI,OAAO,IAAI;QAC7D,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,KAAK,CAAC,IAAI,OAAO,IAAI;QACzD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,OAAO,IAAI;QACvD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,IAAI,OAAO,IAAI;QAC/D,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF;AAKO,SAAS,uBAAuB,IAAoB;IACzD,OAAO,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,OAAO,EAAE;AACtC;AAKO,SAAS,0BAA0B,WAAgC;IACxE,OAAO,uBAAuB,YAAY,IAAI,KAAK,0BAA0B;AAC/E;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,aAAa;IACb,IAAI,CAAC,uBAAuB,YAAY,IAAI,GAAG;QAC7C,QAAQ,IAAI,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,uBAAuB,wBAAwB;IACrD,QAAQ,IAAI,IAAI;IAEhB,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/customerProfiles/addressValidation.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { isCustomerAddressComplete, getMissingAddressFields, getAddressValidationMessage, type CustomerAddressData } from \"@/lib/utils/addressValidation\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\n/**\r\n * Checks if customer has complete address information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerAddress(userId: string): Promise<{\r\n  isValid: boolean;\r\n  missingFields?: string[];\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    // Fetch customer address data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer profile for address validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your address information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n      };\r\n    }\r\n    \r\n    const addressData: CustomerAddressData = {\r\n      pincode: profile?.pincode,\r\n      state: profile?.state,\r\n      city: profile?.city,\r\n      locality: profile?.locality,\r\n      address: profile?.address\r\n    };\r\n    \r\n    const isValid = isCustomerAddressComplete(addressData);\r\n    \r\n    if (!isValid) {\r\n      const missingFields = getMissingAddressFields(addressData);\r\n      const message = getAddressValidationMessage(missingFields);\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n      \r\n      return {\r\n        isValid: false,\r\n        missingFields,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n    \r\n    return { isValid: true };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error during address validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your address. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check address and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteAddress(userId: string): Promise<void> {\r\n  const validation = await validateCustomerAddress(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Checks if customer has complete name information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerName(userId: string): Promise<{\r\n  isValid: boolean;\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Fetch customer name data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('name')\r\n      .eq('id', userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error('Error fetching customer profile for name validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your profile information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n      };\r\n    }\r\n\r\n    // Check if name is present and not empty\r\n    const isValid = !!(profile?.name && profile.name.trim() !== '');\r\n\r\n    if (!isValid) {\r\n      const message = 'Please complete your name in your profile to access the dashboard.';\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n\r\n      return {\r\n        isValid: false,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n\r\n  } catch (error) {\r\n    console.error('Unexpected error during name validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your profile. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check name and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteName(userId: string): Promise<void> {\r\n  const validation = await validateCustomerName(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check both address and name, redirect if incomplete\r\n * Use this in customer dashboard pages (except settings page)\r\n * Settings page is exempt from address validation\r\n */\r\nexport async function requireCompleteProfile(userId: string, exemptFromAddressValidation: boolean = false): Promise<void> {\r\n  // Always check name (required for all dashboard access)\r\n  await requireCompleteName(userId);\r\n\r\n  // Only check address if not exempt (settings page is exempt)\r\n  if (!exemptFromAddressValidation) {\r\n    await requireCompleteAddress(userId);\r\n  }\r\n}\r\n\r\n/**\r\n * Get customer address data for forms\r\n */\r\nexport async function getCustomerAddressData(userId: string): Promise<{\r\n  data?: CustomerAddressData;\r\n  error?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer address data:', error);\r\n      return { error: 'Failed to fetch address data' };\r\n    }\r\n    \r\n    return {\r\n      data: {\r\n        pincode: profile?.pincode,\r\n        state: profile?.state,\r\n        city: profile?.city,\r\n        locality: profile?.locality,\r\n        address: profile?.address\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error fetching address data:', error);\r\n    return { error: 'An unexpected error occurred' };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;;;;;;;AAMO,eAAe,wBAAwB,MAAc;IAM1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,8BAA8B;QAC9B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2DAA2D;YACzE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,MAAM,cAAmC;YACvC,SAAS,SAAS;YAClB,OAAO,SAAS;YAChB,MAAM,SAAS;YACf,UAAU,SAAS;YACnB,SAAS,SAAS;QACpB;QAEA,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE;QAE1C,IAAI,CAAC,SAAS;YACZ,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE;YAC9C,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD,EAAE;YAC5C,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,uBAAuB,MAAc;IACzD,MAAM,aAAa,MAAM,wBAAwB;IAEjD,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAMO,eAAe,qBAAqB,MAAc;IAKvD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,2BAA2B;QAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wDAAwD;YACtE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,yCAAyC;QACzC,MAAM,UAAU,CAAC,CAAC,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,OAAO,EAAE;QAE9D,IAAI,CAAC,SAAS;YACZ,MAAM,UAAU;YAChB,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,oBAAoB,MAAc;IACtD,MAAM,aAAa,MAAM,qBAAqB;IAE9C,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAOO,eAAe,uBAAuB,MAAc,EAAE,8BAAuC,KAAK;IACvG,wDAAwD;IACxD,MAAM,oBAAoB;IAE1B,6DAA6D;IAC7D,IAAI,CAAC,6BAA6B;QAChC,MAAM,uBAAuB;IAC/B;AACF;AAKO,eAAe,uBAAuB,MAAc;IAIzD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBAAE,OAAO;YAA+B;QACjD;QAEA,OAAO;YACL,MAAM;gBACJ,SAAS,SAAS;gBAClB,OAAO,SAAS;gBAChB,MAAM,SAAS;gBACf,UAAU,SAAS;gBACnB,SAAS,SAAS;YACpB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;;;IA9LsB;IAiEA;IAYA;IAuDA;IAaA;IAaA;;AA9JA,+OAAA;AAiEA,+OAAA;AAYA,+OAAA;AAuDA,+OAAA;AAaA,+OAAA;AAaA,+OAAA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/admin.ts"], "sourcesContent": ["import { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * Creates a Supabase admin client with the service role key.\r\n * This client has admin privileges and should only be used on the server.\r\n * Never expose your service_role key in the browser.\r\n */\r\nexport function createAdminClient() {\r\n  return createSupabaseClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,gFAExB,QAAQ,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use admin client to bypass RLS\r\n    const supabaseAdminForSubscribe = createAdminClient();\r\n    const { error: insertError } = await supabaseAdminForSubscribe\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use admin client to bypass RLS\r\n    const supabaseAdminForUnsubscribe = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForUnsubscribe\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use admin client to bypass RLS\r\n    const supabaseAdminForReview = createAdminClient();\r\n    const { error: upsertError } = await supabaseAdminForReview\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForDeleteReview = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForDeleteReview\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use admin client to bypass RLS\r\n    const supabaseAdminForLike = createAdminClient();\r\n    const { error: insertError } = await supabaseAdminForLike\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForSlug = createAdminClient();\r\n    const { data: cardData } = await supabaseAdminForSlug\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use admin client to bypass RLS\r\n    const supabaseAdminForUnlike = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForUnlike\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForCardData = createAdminClient();\r\n    const { data: cardData } = await supabaseAdminForCardData\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForStatus = createAdminClient();\r\n\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabaseAdminForStatus\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabaseAdminForStatus\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabaseAdminForStatus\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;;;;;;;AAKO,eAAe,oBACpB,iBAAyB;IAEzB,0DAA0D;IAC1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,iEAAiE;IACjE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,0DAA0D;QAC1D,MAAM,4BAA4B,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAClD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,0BAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,+EAA+E;YAC/E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,gCAAgC,EAAE,kBAAkB,CAAC,CAAC;gBAExE,qEAAqE;gBACrE,OAAO;oBAAE,SAAS;gBAAK;YACzB;YACA,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,yEAAyE;QACzE,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,cAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;QAEvE,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAIO,eAAe,wBACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,qEAAqE;IACrE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,0DAA0D;QAC1D,MAAM,8BAA8B,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACpD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,4BAClC,IAAI,CAAC,iBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,cAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB,EACzB,MAAc,EACd,UAA0B,AAAC,6BAA6B;;IAExD,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,4DAA4D;IAC5D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;IAEA,IAAI,SAAS,KAAK,SAAS,GAAG;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAkC;IACpE;IAEA,IAAI;QACF,6FAA6F;QAC7F,MAAM,yBAAyB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAC/C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,uBAClC,IAAI,CAAC,mBACL,MAAM,CACL;YACE,SAAS,KAAK,EAAE;YAChB,qBAAqB;YACrB,QAAQ;YACR,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACA;YACE,YAAY;QACd;QAGJ,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,cAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,6DAA6D;QAEpG,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,IAAI;QACF,iCAAiC;QACjC,MAAM,+BAA+B,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACrD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,6BAClC,IAAI,CAAC,mBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,cAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,yDAAyD;IACzD,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,uBAAuB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAC7C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,qBAClC,IAAI,CAAC,SACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,0EAA0E;YAC1E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,CAAC;gBAEhE,OAAO;oBAAE,SAAS;gBAAK,GAAG,uCAAuC;YACnE;YACA,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,iCAAiC;QACjC,MAAM,uBAAuB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAC7C,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,qBAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,eACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,2DAA2D;IAC3D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,yBAAyB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAC/C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,uBAClC,IAAI,CAAC,SACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,iCAAiC;QACjC,MAAM,2BAA2B,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACjD,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,yBAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,qBAAqB,iBAAyB;IAOlE,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAClE,IAAI,SAAwB;IAE5B,mEAAmE;IACnE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,IAAI,MAAM;QACR,SAAS,KAAK,EAAE;IAClB;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,cAAc;QACd,UAAU;QACV,YAAY;QACZ,YAAY;IACd;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO,eAAe,yCAAyC;IACjE;IAEA,IAAI;QACF,iCAAiC;QACjC,MAAM,yBAAyB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAE/C,iCAAiC;QACjC,MAAM,CAAC,iBAAiB,SAAS,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC9D,uBACG,IAAI,CAAC,iBACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,uBACG,IAAI,CAAC,SACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,uBACG,IAAI,CAAC,mBACL,MAAM,CAAC,uBACP,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB,GAChE,WAAW;SACf;QAED,uCAAuC;QACvC,IAAI,gBAAgB,KAAK,EACvB,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,gBAAgB,KAAK,CAAC,OAAO,EAAE;QAEhE,IAAI,QAAQ,KAAK,EACf,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ,KAAK,CAAC,OAAO,EAAE;QAC9D,IAAI,UAAU,KAAK,EACjB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,UAAU,KAAK,CAAC,OAAO,EAAE;QAElE,MAAM,aAAa,UAAU,IAAI;QAEjC,OAAO;YACL,cAAc,CAAC,gBAAgB,KAAK,IAAI,CAAC,IAAI;YAC7C,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI;YACjC,YAAY,YAAY,UAAU;YAClC,YAAY,YAAY,eAAe;QACzC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,sDAAsD;QACtD,OAAO;YAAE,GAAG,aAAa;YAAE,OAAO;QAAa;IACjD;AACF;;;IArhBsB;IAyFA;IA8EA;IA6EA;IAwDA;IAgFA;IA4EA;;AAxcA,+OAAA;AAyFA,+OAAA;AA8EA,+OAAA;AA6EA,+OAAA;AAwDA,+OAAA;AAgFA,+OAAA;AA4EA,+OAAA", "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/customer/reviews/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '00a78b43259bdfa35946a0918da66b9382dcd7b4dc'} from 'ACTIONS_MODULE0'\nexport {validateCustomerAddress as '4044cbba3f28f10081025b33e838df6e3ddc0072ca'} from 'ACTIONS_MODULE1'\nexport {validateCustomerName as '404fd0a9be6e93cb26858695096fb8f7a80e566a2c'} from 'ACTIONS_MODULE1'\nexport {getCustomerAddressData as '407b620db7ebb4e0475b90bfef8276a8d79b4bb51a'} from 'ACTIONS_MODULE1'\nexport {requireCompleteAddress as '40a87cee1cb9a0795a4c6990ef3ca2ba202fadb868'} from 'ACTIONS_MODULE1'\nexport {requireCompleteName as '40e02d24852c03895746a18f4b2a7e50cb5b140aa4'} from 'ACTIONS_MODULE1'\nexport {requireCompleteProfile as '60e0ef2a1f8eb9955a0b5b5ac51d408b39d02549ab'} from 'ACTIONS_MODULE1'\nexport {deleteReview as '40b93613196b00eb1d0d8a6194d02d9df73c399df0'} from 'ACTIONS_MODULE2'\n"], "names": [], "mappings": ";AAAA;AACA;AAMA", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/reviews/components/ReviewsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+U,GAC5W,6GACA", "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/reviews/components/ReviewsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2T,GACxV,yFACA", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/reviews/page.tsx"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { redirect } from 'next/navigation';\r\nimport { Metadata } from 'next';\r\nimport ReviewsPageClient from './components/ReviewsPageClient';\r\nimport { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';\r\n\r\n// Define interfaces for the expected data structure\r\n// interface BusinessProfileDataForReview {\r\n//   id: string;\r\n//   business_name: string | null;\r\n//   business_slug: string | null;\r\n//   logo_url: string | null;\r\n// }\r\n\r\n// Unused interface - keeping for potential future use\r\n// interface ReviewWithProfile {\r\n//   id: string;\r\n//   rating: number;\r\n//   review_text: string | null;\r\n//   created_at: string;\r\n//   updated_at: string;\r\n//   business_profile_id: string;\r\n//   user_id: string;\r\n//   business_profiles: BusinessProfileDataForReview | null;\r\n// }\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"My Reviews - Dukancard\",\r\n  robots: \"noindex, nofollow\",\r\n};\r\n\r\nexport default async function CustomerReviewsPage() {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    redirect('/login?message=Please log in to view your reviews.');\r\n  }\r\n\r\n  // Check if customer has complete address\r\n  await requireCompleteProfile(user.id);\r\n\r\n  try {\r\n    // Get count of reviews written by the customer\r\n    const { count: reviewsCount } = await supabase\r\n      .from('ratings_reviews')\r\n      .select('*', { count: 'exact', head: true })\r\n      .eq('user_id', user.id);\r\n\r\n    return <ReviewsPageClient reviewsCount={reviewsCount || 0} />;\r\n  } catch (_error) {\r\n    // If there's an error fetching count, still render the page with 0 count\r\n    return <ReviewsPageClient reviewsCount={0} />;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAEA;AACA;;;;;;AAsBO,MAAM,WAAqB;IAChC,OAAO;IACP,QAAQ;AACV;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,yCAAyC;IACzC,MAAM,CAAA,GAAA,uJAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,EAAE;IAEpC,IAAI;QACF,+CAA+C;QAC/C,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,mBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,WAAW,KAAK,EAAE;QAExB,qBAAO,8OAAC,4LAAA,CAAA,UAAiB;YAAC,cAAc,gBAAgB;;;;;;IAC1D,EAAE,OAAO,QAAQ;QACf,yEAAyE;QACzE,qBAAO,8OAAC,4LAAA,CAAA,UAAiB;YAAC,cAAc;;;;;;IAC1C;AACF", "debugId": null}}]}