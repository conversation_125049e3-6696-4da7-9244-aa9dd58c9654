{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-2\", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,0HAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { getScalableUserPath } from '@/lib/utils/storage-paths';\r\nimport { z } from 'zod';\r\nimport { PasswordComplexitySchema } from '@/lib/schemas/authSchemas';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// --- Update Email ---\r\n\r\nconst UpdateEmailSchema = z.object({\r\n  newEmail: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type UpdateEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    newEmail?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerEmail(\r\n  _prevState: UpdateEmailFormState,\r\n  formData: FormData\r\n): Promise<UpdateEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdateEmailSchema.safeParse({\r\n    newEmail: formData.get('newEmail'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { newEmail } = validatedFields.data;\r\n\r\n  // Check if the new email is the same as the current one\r\n  if (newEmail === user.email) {\r\n    return { message: 'New email is the same as the current email.', success: false };\r\n  }\r\n\r\n  try {\r\n    // Update email in Supabase Auth\r\n    // This typically sends a confirmation email to both old and new addresses\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: newEmail,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes\r\n      let errorMessage = 'Failed to update email address.';\r\n\r\n      switch (updateError.code) {\r\n        case 'email_exists':\r\n          errorMessage = 'This email address is already registered with another account.';\r\n          break;\r\n        case 'invalid_email':\r\n          errorMessage = 'Please enter a valid email address.';\r\n          break;\r\n        case 'email_change_confirm_limit':\r\n          errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n          break;\r\n        case 'over_email_send_rate_limit':\r\n          errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n          break;\r\n        case 'email_not_confirmed':\r\n          errorMessage = 'Please confirm your current email address before changing it.';\r\n          break;\r\n        case 'same_email':\r\n          errorMessage = 'The new email address is the same as your current email.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update email address. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Revalidate relevant paths if needed, though email change might require user action (confirmation)\r\n    // revalidatePath('/dashboard/customer/settings');\r\n    // revalidatePath('/dashboard/customer/profile'); // Email is shown here\r\n\r\n    return {\r\n      message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Link Email ---\r\n\r\nconst LinkEmailSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type LinkEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function linkCustomerEmail(\r\n  _prevState: LinkEmailFormState,\r\n  formData: FormData\r\n): Promise<LinkEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = LinkEmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user already has an email (email update) or not (email linking)\r\n    const isEmailUpdate = !!user.email;\r\n\r\n    if (isEmailUpdate) {\r\n      // User already has email - use email change flow\r\n      const { error: authUpdateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (authUpdateError) {\r\n        // Handle specific Supabase auth error codes\r\n        let errorMessage = 'Failed to update email address.';\r\n\r\n        switch (authUpdateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n            break;\r\n          case 'email_not_confirmed':\r\n            errorMessage = 'Please confirm your current email address before changing it.';\r\n            break;\r\n          case 'same_email':\r\n            errorMessage = 'The new email address is the same as your current email.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to update email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n        success: true,\r\n      };\r\n    } else {\r\n      // User doesn't have email - directly link the email without OTP verification\r\n      // Supabase will automatically handle duplicate validation\r\n      const { error: updateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (updateError) {\r\n        let errorMessage = 'Failed to link email address.';\r\n\r\n        switch (updateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to link email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Email address linked successfully!',\r\n        success: true,\r\n      };\r\n    }\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while linking email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Verify Email OTP ---\r\n\r\nconst VerifyEmailOTPSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),\r\n});\r\n\r\nexport type VerifyEmailOTPFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n    otp?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function verifyEmailOTP(\r\n  _prevState: VerifyEmailOTPFormState,\r\n  formData: FormData\r\n): Promise<VerifyEmailOTPFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = VerifyEmailOTPSchema.safeParse({\r\n    email: formData.get('email'),\r\n    otp: formData.get('otp'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email, otp } = validatedFields.data;\r\n\r\n  try {\r\n    // Verify the OTP\r\n    const { error: verifyError } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (verifyError) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (verifyError.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // If OTP verification successful, update the user's email\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (updateError) {\r\n      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    return {\r\n      message: 'Email address linked successfully!',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while verifying code.', success: false };\r\n  }\r\n}\r\n\r\n// --- Update Password ---\r\n\r\nconst UpdatePasswordSchema = z.object({\r\n  currentPassword: z.string().min(1, 'Current password is required.'),\r\n  newPassword: PasswordComplexitySchema,\r\n});\r\n\r\nexport type UpdatePasswordFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    currentPassword?: string[];\r\n    newPassword?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerPassword(\r\n  _prevState: UpdatePasswordFormState,\r\n  formData: FormData\r\n): Promise<UpdatePasswordFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) { // Need email for re-authentication check\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdatePasswordSchema.safeParse({\r\n    currentPassword: formData.get('currentPassword'),\r\n    newPassword: formData.get('newPassword'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { currentPassword, newPassword } = validatedFields.data;\r\n\r\n  // IMPORTANT: Verify the current password first before attempting update.\r\n  // Supabase doesn't directly expose a \"verify password\" endpoint.\r\n  // The recommended way is to try signing in with the current password.\r\n  // This is a crucial security step.\r\n  const { error: signInError } = await supabase.auth.signInWithPassword({\r\n    email: user.email,\r\n    password: currentPassword,\r\n  });\r\n\r\n  if (signInError) {\r\n     // Handle specific Supabase auth error codes for sign-in\r\n     let errorMessage = 'Failed to verify current password.';\r\n     const fieldErrors: { currentPassword?: string[] } = {};\r\n\r\n     switch (signInError.code) {\r\n       case 'invalid_credentials':\r\n       case 'email_not_confirmed':\r\n         errorMessage = 'Incorrect current password.';\r\n         fieldErrors.currentPassword = ['Incorrect current password.'];\r\n         break;\r\n       case 'too_many_requests':\r\n         errorMessage = 'Too many failed attempts. Please wait before trying again.';\r\n         break;\r\n       case 'user_not_found':\r\n         errorMessage = 'Account not found. Please contact support.';\r\n         break;\r\n       default:\r\n         errorMessage = 'Unable to verify current password. Please try again.';\r\n     }\r\n\r\n     return {\r\n       message: errorMessage,\r\n       errors: fieldErrors,\r\n       success: false\r\n     };\r\n  }\r\n\r\n\r\n  // If sign-in was successful (current password is correct), proceed to update\r\n  try {\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      password: newPassword,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes for password update\r\n      let errorMessage = 'Failed to update password.';\r\n\r\n      switch (updateError.code) {\r\n        case 'weak_password':\r\n          errorMessage = 'Password is too weak. Please choose a stronger password.';\r\n          break;\r\n        case 'same_password':\r\n          errorMessage = 'New password must be different from your current password.';\r\n          break;\r\n        case 'password_too_short':\r\n          errorMessage = 'Password must be at least 6 characters long.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many password change requests. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update password. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Password updated successfully\r\n    return { message: 'Password updated successfully!', success: true };\r\n\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating password.', success: false };\r\n  }\r\n}\r\n\r\n\r\n// --- Enhanced Delete Account Security Actions ---\r\n\r\n// Check user's email and phone availability for delete account verification\r\nexport async function checkDeleteAccountVerificationOptions(): Promise<{\r\n  success: boolean;\r\n  hasEmail: boolean;\r\n  hasPhone: boolean;\r\n  message?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      hasEmail: false,\r\n      hasPhone: false,\r\n      message: \"Authentication required.\"\r\n    };\r\n  }\r\n\r\n  const hasEmail = !!(user.email && user.email.trim() !== '');\r\n  const hasPhone = !!(user.phone && user.phone.trim() !== '');\r\n\r\n  return {\r\n    success: true,\r\n    hasEmail,\r\n    hasPhone,\r\n  };\r\n}\r\n\r\n// Send OTP to email for delete account verification\r\nexport async function sendDeleteAccountOTP(): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n  email?: string;\r\n  isConfigurationError?: boolean;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no email found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: user.email,\r\n      options: {\r\n        shouldCreateUser: false, // Don't create new user\r\n      },\r\n    });\r\n\r\n    if (error) {\r\n      // Handle rate limit errors specifically\r\n      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {\r\n        return {\r\n          success: false,\r\n          message: \"Email rate limit exceeded. Please try again later.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: error.message || \"Failed to send verification code.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Verification code sent to your email address.\",\r\n      email: user.email,\r\n    };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred while sending verification code.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for delete account\r\nexport async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (error) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (error.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { success: false, message: errorMessage };\r\n    }\r\n\r\n    return { success: true, message: \"Verification successful.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify password for delete account (for phone users)\r\nexport async function verifyDeleteAccountPassword(password: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.phone) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no phone found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Verify current password by attempting to sign in\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      phone: user.phone,\r\n      password: password,\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        message: \"Invalid password. Please try again.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, message: \"Password verified successfully.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during password verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// --- Delete Account ---\r\n\r\nexport type DeleteAccountFormState = {\r\n  message: string | null;\r\n  success: boolean;\r\n};\r\n\r\nexport async function deleteCustomerAccount(\r\n  // No prevState or formData needed for this action\r\n): Promise<DeleteAccountFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  // Check for and clean up any storage data using hash-based structure\r\n  try {\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n    const bucketName = \"customers\"; // Correct bucket name (plural)\r\n    const userStoragePath = getScalableUserPath(user.id);\r\n\r\n    // Recursive function to delete all files and folders\r\n    const deleteRecursively = async (path: string): Promise<void> => {\r\n      const { data: items, error: listError } = await adminSupabase.storage\r\n        .from(bucketName)\r\n        .list(path);\r\n\r\n      if (listError) {\r\n        console.error(`Error listing files in ${path}:`, listError);\r\n        return;\r\n      }\r\n\r\n      if (!items || items.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Separate files and folders\r\n      const files: string[] = [];\r\n      const folders: string[] = [];\r\n\r\n      for (const item of items) {\r\n        const fullPath = path ? `${path}/${item.name}` : item.name;\r\n\r\n        if (item.metadata === null) {\r\n          // This is a folder\r\n          folders.push(fullPath);\r\n        } else {\r\n          // This is a file\r\n          files.push(fullPath);\r\n        }\r\n      }\r\n\r\n      // Delete all files in the current directory\r\n      if (files.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(bucketName)\r\n          .remove(files);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(`Error deleting files in ${path}:`, deleteError);\r\n        } else {\r\n          console.log(`Successfully deleted ${files.length} files in ${path}`);\r\n        }\r\n      }\r\n\r\n      // Recursively delete folders\r\n      for (const folder of folders) {\r\n        await deleteRecursively(folder);\r\n      }\r\n    };\r\n\r\n    // Start the recursive deletion from the user's root folder\r\n    await deleteRecursively(userStoragePath);\r\n\r\n    console.log('Successfully cleaned up customer storage data');\r\n  } catch (storageError) {\r\n    // Log but continue with deletion\r\n    console.error('Error checking/cleaning customer storage:', storageError);\r\n  }\r\n\r\n  // Use the admin client to delete the user and profile\r\n  try {\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Delete from customer_profiles table (CASCADE will handle related data)\r\n    console.log('Deleting customer profile...');\r\n    const { error: deleteProfileError } = await supabaseAdmin\r\n      .from('customer_profiles')\r\n      .delete()\r\n      .eq('id', user.id);\r\n\r\n    if (deleteProfileError) {\r\n      console.error('Error deleting customer profile:', deleteProfileError);\r\n      return { message: `Failed to delete customer profile: ${deleteProfileError.message}`, success: false };\r\n    }\r\n\r\n    console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');\r\n\r\n    // Sign out the user locally first (while the session is still valid)\r\n    await supabase.auth.signOut();\r\n\r\n    // Then delete the user using the admin client\r\n    // Using hard delete (shouldSoftDelete=false) to completely remove the user\r\n    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);\r\n\r\n    if (deleteUserError) {\r\n      console.error('Error deleting user account:', deleteUserError);\r\n      return { message: `Failed to delete account: ${deleteUserError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate paths if needed\r\n    revalidatePath(\"/\", \"layout\"); // Revalidate root layout\r\n\r\n    return { message: 'Account deleted successfully.', success: true };\r\n\r\n  } catch (error) {\r\n     console.error('Unexpected error during account deletion:', error);\r\n     return { message: 'An unexpected error occurred during account deletion.', success: false };\r\n  }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0VsB,yBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\n// Reusable base schema for password complexity\r\nexport const PasswordComplexitySchema = z\r\n  .string()\r\n  .min(6, { message: \"Password must be at least 6 characters\" })\r\n  .regex(/[A-Z]/, { message: \"Password must contain at least one capital letter\" })\r\n  .regex(/[a-z]/, { message: \"Password must contain at least one lowercase letter.\" }) // Added lowercase check for consistency\r\n  .regex(/[0-9]/, { message: \"Password must contain at least one number\" })\r\n  .regex(/[^A-Za-z0-9]/, { message: \"Password must contain at least one symbol\" });\r\n\r\n// Reusable schema for mobile number validation\r\nexport const IndianMobileSchema = z\r\n  .string()\r\n  .min(10, { message: \"Mobile number must be at least 10 digits\" })\r\n  .max(10, { message: \"Mobile number must be exactly 10 digits\" })\r\n  .regex(/^\\d{10}$/, {\r\n    message: \"Please enter a valid 10-digit mobile number\"\r\n  });\r\n\r\n// Schema for forms requiring password confirmation (e.g., registration, change password)\r\nexport const PasswordConfirmationSchema = z\r\n  .object({\r\n    password: PasswordComplexitySchema,\r\n    confirmPassword: z.string(),\r\n  })\r\n  .refine((data) => data.password === data.confirmPassword, {\r\n    message: \"Passwords don't match\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\n// Schema for forms requiring new password confirmation (used in settings/reset)\r\n// Renaming fields for clarity in those contexts\r\nexport const NewPasswordConfirmationSchema = z\r\n  .object({\r\n    newPassword: PasswordComplexitySchema,\r\n    confirmPassword: z.string(),\r\n  })\r\n  .refine((data) => data.newPassword === data.confirmPassword, {\r\n    message: \"Passwords do not match.\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\n// Schema for just the password field (e.g., for login or single field validation)\r\nexport const SinglePasswordSchema = z.object({\r\n    password: PasswordComplexitySchema,\r\n});\r\n\r\n// Schema for just the new password field (used in reset password action)\r\nexport const SingleNewPasswordSchema = z.object({\r\n    password: PasswordComplexitySchema, // Action receives it as 'password'\r\n});\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG;IAAE,SAAS;AAAyC,GAC3D,KAAK,CAAC,SAAS;IAAE,SAAS;AAAoD,GAC9E,KAAK,CAAC,SAAS;IAAE,SAAS;AAAuD,GAAG,wCAAwC;CAC5H,KAAK,CAAC,SAAS;IAAE,SAAS;AAA4C,GACtE,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAGzE,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAChC,MAAM,GACN,GAAG,CAAC,IAAI;IAAE,SAAS;AAA2C,GAC9D,GAAG,CAAC,IAAI;IAAE,SAAS;AAA0C,GAC7D,KAAK,CAAC,YAAY;IACjB,SAAS;AACX;AAGK,MAAM,6BAA6B,oIAAA,CAAA,IAAC,CACxC,MAAM,CAAC;IACN,UAAU;IACV,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAIK,MAAM,gCAAgC,oIAAA,CAAA,IAAC,CAC3C,MAAM,CAAC;IACN,aAAa;IACb,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC3D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGK,MAAM,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU;AACd;AAGO,MAAM,0BAA0B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,UAAU;AACd", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/components/PasswordUpdateSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useTransition } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { z } from \"zod\";\r\nimport { toast } from \"sonner\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Loader2, KeyRound, Info } from \"lucide-react\";\r\nimport { updateCustomerPassword } from \"../actions\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { PasswordComplexitySchema } from \"@/lib/schemas/authSchemas\";\r\n\r\n// Password schema\r\nconst PasswordSchema = z.object({\r\n  currentPassword: z.string().min(1, \"Current password is required\"),\r\n  newPassword: PasswordComplexitySchema,\r\n  confirmPassword: z.string().min(1, \"Please confirm your password\"),\r\n}).refine((data) => data.newPassword === data.confirmPassword, {\r\n  message: \"Passwords do not match\",\r\n  path: [\"confirmPassword\"],\r\n});\r\n\r\ninterface PasswordUpdateSectionProps {\r\n  registrationType: 'google' | 'email' | 'phone';\r\n}\r\n\r\nexport default function PasswordUpdateSection({\r\n  registrationType,\r\n}: PasswordUpdateSectionProps) {\r\n  const isGoogleLogin = registrationType === 'google';\r\n  const [isPending, startTransition] = useTransition();\r\n\r\n  // Password Form\r\n  const passwordForm = useForm<z.infer<typeof PasswordSchema>>({\r\n    resolver: zodResolver(PasswordSchema),\r\n    defaultValues: {\r\n      currentPassword: \"\",\r\n      newPassword: \"\",\r\n      confirmPassword: \"\",\r\n    },\r\n  });\r\n\r\n  // Handle password update\r\n  const onPasswordSubmit = (data: z.infer<typeof PasswordSchema>) => {\r\n    startTransition(async () => {\r\n      try {\r\n        // Create FormData object for the server action\r\n        const formData = new FormData();\r\n        formData.append('currentPassword', data.currentPassword);\r\n        formData.append('newPassword', data.newPassword);\r\n\r\n        // Call the server action with the required parameters\r\n        const result = await updateCustomerPassword(\r\n          { message: null, success: false }, // Initial state\r\n          formData\r\n        );\r\n\r\n        if (result.success) {\r\n          toast.success(\"Password updated successfully!\");\r\n          passwordForm.reset();\r\n        } else {\r\n          toast.error(result.message || \"Failed to update password\");\r\n\r\n          // Set server-side field errors into react-hook-form state\r\n          if (result.errors?.currentPassword) {\r\n            passwordForm.setError('currentPassword', {\r\n              type: 'server',\r\n              message: result.errors.currentPassword.join(', ')\r\n            });\r\n          }\r\n\r\n          if (result.errors?.newPassword) {\r\n            passwordForm.setError('newPassword', {\r\n              type: 'server',\r\n              message: result.errors.newPassword.join(', ')\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        toast.error(\"An unexpected error occurred\");\r\n        console.error(error);\r\n      }\r\n    });\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.4, delay: 0.1 }}\r\n      className=\"rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 transition-all duration-300 hover:shadow-lg\"\r\n    >\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <div className=\"p-2 rounded-lg bg-primary/10 text-primary self-start\">\r\n          <KeyRound className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n            Password\r\n          </h3>\r\n          <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n            Update your password\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {isGoogleLogin ? (\r\n        <div className=\"p-4 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg border border-neutral-200 dark:border-neutral-700\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Info className=\"w-4 h-4 text-amber-500\" />\r\n            <p className=\"text-sm text-neutral-700 dark:text-neutral-300\">\r\n              You signed up with Google. Password management is handled by your Google account.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <Form {...passwordForm}>\r\n          <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}>\r\n            <div className=\"space-y-4\">\r\n              <FormField\r\n                control={passwordForm.control}\r\n                name=\"currentPassword\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel className=\"text-sm text-neutral-700 dark:text-neutral-300\">\r\n                      Current Password\r\n                    </FormLabel>\r\n                    <FormControl>\r\n                      <Input\r\n                        type=\"password\"\r\n                        placeholder=\"••••••••\"\r\n                        {...field}\r\n                        disabled={isPending}\r\n                        className=\"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700\"\r\n                      />\r\n                    </FormControl>\r\n                    <FormMessage className=\"text-xs text-red-500\" />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n              <FormField\r\n                control={passwordForm.control}\r\n                name=\"newPassword\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel className=\"text-sm text-neutral-700 dark:text-neutral-300\">\r\n                      New Password\r\n                    </FormLabel>\r\n                    <FormControl>\r\n                      <Input\r\n                        type=\"password\"\r\n                        placeholder=\"••••••••\"\r\n                        {...field}\r\n                        disabled={isPending}\r\n                        className=\"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700\"\r\n                      />\r\n                    </FormControl>\r\n                    <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400\">\r\n                      Must contain: 6+ chars, 1 uppercase letter, 1 lowercase letter, 1 number, 1 symbol\r\n                    </FormDescription>\r\n                    <FormMessage className=\"text-xs text-red-500\" />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n              <FormField\r\n                control={passwordForm.control}\r\n                name=\"confirmPassword\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel className=\"text-sm text-neutral-700 dark:text-neutral-300\">\r\n                      Confirm Password\r\n                    </FormLabel>\r\n                    <FormControl>\r\n                      <Input\r\n                        type=\"password\"\r\n                        placeholder=\"••••••••\"\r\n                        {...field}\r\n                        disabled={isPending}\r\n                        className=\"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700\"\r\n                      />\r\n                    </FormControl>\r\n                    <FormMessage className=\"text-xs text-red-500\" />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n              <div className=\"mt-4 sm:mt-6 flex justify-end\">\r\n                  <Button\r\n                    type=\"submit\"\r\n                    disabled={isPending}\r\n                    className=\"bg-primary hover:bg-primary/90 text-primary-foreground\"\r\n                  >\r\n                    {isPending && (\r\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                    )}\r\n                    Change Password\r\n                  </Button>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </Form>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AACA;AACA;AACA;AArBA;;;;;;;;;;;;;;AAuBA,kBAAkB;AAClB,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,aAAa,6HAAA,CAAA,2BAAwB;IACrC,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACrC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC7D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAMe,SAAS,sBAAsB,EAC5C,gBAAgB,EACW;IAC3B,MAAM,gBAAgB,qBAAqB;IAC3C,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IAEjD,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAkC;QAC3D,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,iBAAiB;YACjB,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA,yBAAyB;IACzB,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;YACd,IAAI;gBACF,+CAA+C;gBAC/C,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,mBAAmB,KAAK,eAAe;gBACvD,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;gBAE/C,sDAAsD;gBACtD,MAAM,SAAS,MAAM,CAAA,GAAA,iMAAA,CAAA,yBAAsB,AAAD,EACxC;oBAAE,SAAS;oBAAM,SAAS;gBAAM,GAChC;gBAGF,IAAI,OAAO,OAAO,EAAE;oBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,aAAa,KAAK;gBACpB,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;oBAE9B,0DAA0D;oBAC1D,IAAI,OAAO,MAAM,EAAE,iBAAiB;wBAClC,aAAa,QAAQ,CAAC,mBAAmB;4BACvC,MAAM;4BACN,SAAS,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC;wBAC9C;oBACF;oBAEA,IAAI,OAAO,MAAM,EAAE,aAAa;wBAC9B,aAAa,QAAQ,CAAC,eAAe;4BACnC,MAAM;4BACN,SAAS,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;wBAC1C;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,QAAQ,KAAK,CAAC;YAChB;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;QACxC,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4E;;;;;;0CAG1F,8OAAC;gCAAE,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;YAMxE,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAE,WAAU;sCAAiD;;;;;;;;;;;;;;;;qCAMlE,8OAAC,yHAAA,CAAA,OAAI;gBAAE,GAAG,YAAY;0BACpB,cAAA,8OAAC;oBAAK,UAAU,aAAa,YAAY,CAAC;8BACxC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,yHAAA,CAAA,YAAS;gCACR,SAAS,aAAa,OAAO;gCAC7B,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;0DACP,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAiD;;;;;;0DAGtE,8OAAC,yHAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACX,GAAG,KAAK;oDACT,UAAU;oDACV,WAAU;;;;;;;;;;;0DAGd,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;0CAI7B,8OAAC,yHAAA,CAAA,YAAS;gCACR,SAAS,aAAa,OAAO;gCAC7B,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;0DACP,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAiD;;;;;;0DAGtE,8OAAC,yHAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACX,GAAG,KAAK;oDACT,UAAU;oDACV,WAAU;;;;;;;;;;;0DAGd,8OAAC,yHAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAiD;;;;;;0DAG5E,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;0CAI7B,8OAAC,yHAAA,CAAA,YAAS;gCACR,SAAS,aAAa,OAAO;gCAC7B,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;0DACP,8OAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAiD;;;;;;0DAGtE,8OAAC,yHAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACX,GAAG,KAAK;oDACT,UAAU;oDACV,WAAU;;;;;;;;;;;0DAGd,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;0CAI7B,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;;wCAET,2BACC,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtB", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { getScalableUserPath } from '@/lib/utils/storage-paths';\r\nimport { z } from 'zod';\r\nimport { PasswordComplexitySchema } from '@/lib/schemas/authSchemas';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// --- Update Email ---\r\n\r\nconst UpdateEmailSchema = z.object({\r\n  newEmail: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type UpdateEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    newEmail?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerEmail(\r\n  _prevState: UpdateEmailFormState,\r\n  formData: FormData\r\n): Promise<UpdateEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdateEmailSchema.safeParse({\r\n    newEmail: formData.get('newEmail'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { newEmail } = validatedFields.data;\r\n\r\n  // Check if the new email is the same as the current one\r\n  if (newEmail === user.email) {\r\n    return { message: 'New email is the same as the current email.', success: false };\r\n  }\r\n\r\n  try {\r\n    // Update email in Supabase Auth\r\n    // This typically sends a confirmation email to both old and new addresses\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: newEmail,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes\r\n      let errorMessage = 'Failed to update email address.';\r\n\r\n      switch (updateError.code) {\r\n        case 'email_exists':\r\n          errorMessage = 'This email address is already registered with another account.';\r\n          break;\r\n        case 'invalid_email':\r\n          errorMessage = 'Please enter a valid email address.';\r\n          break;\r\n        case 'email_change_confirm_limit':\r\n          errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n          break;\r\n        case 'over_email_send_rate_limit':\r\n          errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n          break;\r\n        case 'email_not_confirmed':\r\n          errorMessage = 'Please confirm your current email address before changing it.';\r\n          break;\r\n        case 'same_email':\r\n          errorMessage = 'The new email address is the same as your current email.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update email address. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Revalidate relevant paths if needed, though email change might require user action (confirmation)\r\n    // revalidatePath('/dashboard/customer/settings');\r\n    // revalidatePath('/dashboard/customer/profile'); // Email is shown here\r\n\r\n    return {\r\n      message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Link Email ---\r\n\r\nconst LinkEmailSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type LinkEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function linkCustomerEmail(\r\n  _prevState: LinkEmailFormState,\r\n  formData: FormData\r\n): Promise<LinkEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = LinkEmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user already has an email (email update) or not (email linking)\r\n    const isEmailUpdate = !!user.email;\r\n\r\n    if (isEmailUpdate) {\r\n      // User already has email - use email change flow\r\n      const { error: authUpdateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (authUpdateError) {\r\n        // Handle specific Supabase auth error codes\r\n        let errorMessage = 'Failed to update email address.';\r\n\r\n        switch (authUpdateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n            break;\r\n          case 'email_not_confirmed':\r\n            errorMessage = 'Please confirm your current email address before changing it.';\r\n            break;\r\n          case 'same_email':\r\n            errorMessage = 'The new email address is the same as your current email.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to update email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n        success: true,\r\n      };\r\n    } else {\r\n      // User doesn't have email - directly link the email without OTP verification\r\n      // Supabase will automatically handle duplicate validation\r\n      const { error: updateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (updateError) {\r\n        let errorMessage = 'Failed to link email address.';\r\n\r\n        switch (updateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to link email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Email address linked successfully!',\r\n        success: true,\r\n      };\r\n    }\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while linking email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Verify Email OTP ---\r\n\r\nconst VerifyEmailOTPSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),\r\n});\r\n\r\nexport type VerifyEmailOTPFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n    otp?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function verifyEmailOTP(\r\n  _prevState: VerifyEmailOTPFormState,\r\n  formData: FormData\r\n): Promise<VerifyEmailOTPFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = VerifyEmailOTPSchema.safeParse({\r\n    email: formData.get('email'),\r\n    otp: formData.get('otp'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email, otp } = validatedFields.data;\r\n\r\n  try {\r\n    // Verify the OTP\r\n    const { error: verifyError } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (verifyError) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (verifyError.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // If OTP verification successful, update the user's email\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (updateError) {\r\n      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    return {\r\n      message: 'Email address linked successfully!',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while verifying code.', success: false };\r\n  }\r\n}\r\n\r\n// --- Update Password ---\r\n\r\nconst UpdatePasswordSchema = z.object({\r\n  currentPassword: z.string().min(1, 'Current password is required.'),\r\n  newPassword: PasswordComplexitySchema,\r\n});\r\n\r\nexport type UpdatePasswordFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    currentPassword?: string[];\r\n    newPassword?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerPassword(\r\n  _prevState: UpdatePasswordFormState,\r\n  formData: FormData\r\n): Promise<UpdatePasswordFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) { // Need email for re-authentication check\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdatePasswordSchema.safeParse({\r\n    currentPassword: formData.get('currentPassword'),\r\n    newPassword: formData.get('newPassword'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { currentPassword, newPassword } = validatedFields.data;\r\n\r\n  // IMPORTANT: Verify the current password first before attempting update.\r\n  // Supabase doesn't directly expose a \"verify password\" endpoint.\r\n  // The recommended way is to try signing in with the current password.\r\n  // This is a crucial security step.\r\n  const { error: signInError } = await supabase.auth.signInWithPassword({\r\n    email: user.email,\r\n    password: currentPassword,\r\n  });\r\n\r\n  if (signInError) {\r\n     // Handle specific Supabase auth error codes for sign-in\r\n     let errorMessage = 'Failed to verify current password.';\r\n     const fieldErrors: { currentPassword?: string[] } = {};\r\n\r\n     switch (signInError.code) {\r\n       case 'invalid_credentials':\r\n       case 'email_not_confirmed':\r\n         errorMessage = 'Incorrect current password.';\r\n         fieldErrors.currentPassword = ['Incorrect current password.'];\r\n         break;\r\n       case 'too_many_requests':\r\n         errorMessage = 'Too many failed attempts. Please wait before trying again.';\r\n         break;\r\n       case 'user_not_found':\r\n         errorMessage = 'Account not found. Please contact support.';\r\n         break;\r\n       default:\r\n         errorMessage = 'Unable to verify current password. Please try again.';\r\n     }\r\n\r\n     return {\r\n       message: errorMessage,\r\n       errors: fieldErrors,\r\n       success: false\r\n     };\r\n  }\r\n\r\n\r\n  // If sign-in was successful (current password is correct), proceed to update\r\n  try {\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      password: newPassword,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes for password update\r\n      let errorMessage = 'Failed to update password.';\r\n\r\n      switch (updateError.code) {\r\n        case 'weak_password':\r\n          errorMessage = 'Password is too weak. Please choose a stronger password.';\r\n          break;\r\n        case 'same_password':\r\n          errorMessage = 'New password must be different from your current password.';\r\n          break;\r\n        case 'password_too_short':\r\n          errorMessage = 'Password must be at least 6 characters long.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many password change requests. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update password. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Password updated successfully\r\n    return { message: 'Password updated successfully!', success: true };\r\n\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating password.', success: false };\r\n  }\r\n}\r\n\r\n\r\n// --- Enhanced Delete Account Security Actions ---\r\n\r\n// Check user's email and phone availability for delete account verification\r\nexport async function checkDeleteAccountVerificationOptions(): Promise<{\r\n  success: boolean;\r\n  hasEmail: boolean;\r\n  hasPhone: boolean;\r\n  message?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      hasEmail: false,\r\n      hasPhone: false,\r\n      message: \"Authentication required.\"\r\n    };\r\n  }\r\n\r\n  const hasEmail = !!(user.email && user.email.trim() !== '');\r\n  const hasPhone = !!(user.phone && user.phone.trim() !== '');\r\n\r\n  return {\r\n    success: true,\r\n    hasEmail,\r\n    hasPhone,\r\n  };\r\n}\r\n\r\n// Send OTP to email for delete account verification\r\nexport async function sendDeleteAccountOTP(): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n  email?: string;\r\n  isConfigurationError?: boolean;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no email found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: user.email,\r\n      options: {\r\n        shouldCreateUser: false, // Don't create new user\r\n      },\r\n    });\r\n\r\n    if (error) {\r\n      // Handle rate limit errors specifically\r\n      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {\r\n        return {\r\n          success: false,\r\n          message: \"Email rate limit exceeded. Please try again later.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: error.message || \"Failed to send verification code.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Verification code sent to your email address.\",\r\n      email: user.email,\r\n    };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred while sending verification code.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for delete account\r\nexport async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (error) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (error.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { success: false, message: errorMessage };\r\n    }\r\n\r\n    return { success: true, message: \"Verification successful.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify password for delete account (for phone users)\r\nexport async function verifyDeleteAccountPassword(password: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.phone) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no phone found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Verify current password by attempting to sign in\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      phone: user.phone,\r\n      password: password,\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        message: \"Invalid password. Please try again.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, message: \"Password verified successfully.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during password verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// --- Delete Account ---\r\n\r\nexport type DeleteAccountFormState = {\r\n  message: string | null;\r\n  success: boolean;\r\n};\r\n\r\nexport async function deleteCustomerAccount(\r\n  // No prevState or formData needed for this action\r\n): Promise<DeleteAccountFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  // Check for and clean up any storage data using hash-based structure\r\n  try {\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n    const bucketName = \"customers\"; // Correct bucket name (plural)\r\n    const userStoragePath = getScalableUserPath(user.id);\r\n\r\n    // Recursive function to delete all files and folders\r\n    const deleteRecursively = async (path: string): Promise<void> => {\r\n      const { data: items, error: listError } = await adminSupabase.storage\r\n        .from(bucketName)\r\n        .list(path);\r\n\r\n      if (listError) {\r\n        console.error(`Error listing files in ${path}:`, listError);\r\n        return;\r\n      }\r\n\r\n      if (!items || items.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Separate files and folders\r\n      const files: string[] = [];\r\n      const folders: string[] = [];\r\n\r\n      for (const item of items) {\r\n        const fullPath = path ? `${path}/${item.name}` : item.name;\r\n\r\n        if (item.metadata === null) {\r\n          // This is a folder\r\n          folders.push(fullPath);\r\n        } else {\r\n          // This is a file\r\n          files.push(fullPath);\r\n        }\r\n      }\r\n\r\n      // Delete all files in the current directory\r\n      if (files.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(bucketName)\r\n          .remove(files);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(`Error deleting files in ${path}:`, deleteError);\r\n        } else {\r\n          console.log(`Successfully deleted ${files.length} files in ${path}`);\r\n        }\r\n      }\r\n\r\n      // Recursively delete folders\r\n      for (const folder of folders) {\r\n        await deleteRecursively(folder);\r\n      }\r\n    };\r\n\r\n    // Start the recursive deletion from the user's root folder\r\n    await deleteRecursively(userStoragePath);\r\n\r\n    console.log('Successfully cleaned up customer storage data');\r\n  } catch (storageError) {\r\n    // Log but continue with deletion\r\n    console.error('Error checking/cleaning customer storage:', storageError);\r\n  }\r\n\r\n  // Use the admin client to delete the user and profile\r\n  try {\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Delete from customer_profiles table (CASCADE will handle related data)\r\n    console.log('Deleting customer profile...');\r\n    const { error: deleteProfileError } = await supabaseAdmin\r\n      .from('customer_profiles')\r\n      .delete()\r\n      .eq('id', user.id);\r\n\r\n    if (deleteProfileError) {\r\n      console.error('Error deleting customer profile:', deleteProfileError);\r\n      return { message: `Failed to delete customer profile: ${deleteProfileError.message}`, success: false };\r\n    }\r\n\r\n    console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');\r\n\r\n    // Sign out the user locally first (while the session is still valid)\r\n    await supabase.auth.signOut();\r\n\r\n    // Then delete the user using the admin client\r\n    // Using hard delete (shouldSoftDelete=false) to completely remove the user\r\n    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);\r\n\r\n    if (deleteUserError) {\r\n      console.error('Error deleting user account:', deleteUserError);\r\n      return { message: `Failed to delete account: ${deleteUserError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate paths if needed\r\n    revalidatePath(\"/\", \"layout\"); // Revalidate root layout\r\n\r\n    return { message: 'Account deleted successfully.', success: true };\r\n\r\n  } catch (error) {\r\n     console.error('Unexpected error during account deletion:', error);\r\n     return { message: 'An unexpected error occurred during account deletion.', success: false };\r\n  }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAynBsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { getScalableUserPath } from '@/lib/utils/storage-paths';\r\nimport { z } from 'zod';\r\nimport { PasswordComplexitySchema } from '@/lib/schemas/authSchemas';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// --- Update Email ---\r\n\r\nconst UpdateEmailSchema = z.object({\r\n  newEmail: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type UpdateEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    newEmail?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerEmail(\r\n  _prevState: UpdateEmailFormState,\r\n  formData: FormData\r\n): Promise<UpdateEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdateEmailSchema.safeParse({\r\n    newEmail: formData.get('newEmail'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { newEmail } = validatedFields.data;\r\n\r\n  // Check if the new email is the same as the current one\r\n  if (newEmail === user.email) {\r\n    return { message: 'New email is the same as the current email.', success: false };\r\n  }\r\n\r\n  try {\r\n    // Update email in Supabase Auth\r\n    // This typically sends a confirmation email to both old and new addresses\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: newEmail,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes\r\n      let errorMessage = 'Failed to update email address.';\r\n\r\n      switch (updateError.code) {\r\n        case 'email_exists':\r\n          errorMessage = 'This email address is already registered with another account.';\r\n          break;\r\n        case 'invalid_email':\r\n          errorMessage = 'Please enter a valid email address.';\r\n          break;\r\n        case 'email_change_confirm_limit':\r\n          errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n          break;\r\n        case 'over_email_send_rate_limit':\r\n          errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n          break;\r\n        case 'email_not_confirmed':\r\n          errorMessage = 'Please confirm your current email address before changing it.';\r\n          break;\r\n        case 'same_email':\r\n          errorMessage = 'The new email address is the same as your current email.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update email address. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Revalidate relevant paths if needed, though email change might require user action (confirmation)\r\n    // revalidatePath('/dashboard/customer/settings');\r\n    // revalidatePath('/dashboard/customer/profile'); // Email is shown here\r\n\r\n    return {\r\n      message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Link Email ---\r\n\r\nconst LinkEmailSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type LinkEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function linkCustomerEmail(\r\n  _prevState: LinkEmailFormState,\r\n  formData: FormData\r\n): Promise<LinkEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = LinkEmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user already has an email (email update) or not (email linking)\r\n    const isEmailUpdate = !!user.email;\r\n\r\n    if (isEmailUpdate) {\r\n      // User already has email - use email change flow\r\n      const { error: authUpdateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (authUpdateError) {\r\n        // Handle specific Supabase auth error codes\r\n        let errorMessage = 'Failed to update email address.';\r\n\r\n        switch (authUpdateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n            break;\r\n          case 'email_not_confirmed':\r\n            errorMessage = 'Please confirm your current email address before changing it.';\r\n            break;\r\n          case 'same_email':\r\n            errorMessage = 'The new email address is the same as your current email.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to update email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n        success: true,\r\n      };\r\n    } else {\r\n      // User doesn't have email - directly link the email without OTP verification\r\n      // Supabase will automatically handle duplicate validation\r\n      const { error: updateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (updateError) {\r\n        let errorMessage = 'Failed to link email address.';\r\n\r\n        switch (updateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to link email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Email address linked successfully!',\r\n        success: true,\r\n      };\r\n    }\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while linking email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Verify Email OTP ---\r\n\r\nconst VerifyEmailOTPSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),\r\n});\r\n\r\nexport type VerifyEmailOTPFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n    otp?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function verifyEmailOTP(\r\n  _prevState: VerifyEmailOTPFormState,\r\n  formData: FormData\r\n): Promise<VerifyEmailOTPFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = VerifyEmailOTPSchema.safeParse({\r\n    email: formData.get('email'),\r\n    otp: formData.get('otp'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email, otp } = validatedFields.data;\r\n\r\n  try {\r\n    // Verify the OTP\r\n    const { error: verifyError } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (verifyError) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (verifyError.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // If OTP verification successful, update the user's email\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (updateError) {\r\n      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    return {\r\n      message: 'Email address linked successfully!',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while verifying code.', success: false };\r\n  }\r\n}\r\n\r\n// --- Update Password ---\r\n\r\nconst UpdatePasswordSchema = z.object({\r\n  currentPassword: z.string().min(1, 'Current password is required.'),\r\n  newPassword: PasswordComplexitySchema,\r\n});\r\n\r\nexport type UpdatePasswordFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    currentPassword?: string[];\r\n    newPassword?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerPassword(\r\n  _prevState: UpdatePasswordFormState,\r\n  formData: FormData\r\n): Promise<UpdatePasswordFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) { // Need email for re-authentication check\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdatePasswordSchema.safeParse({\r\n    currentPassword: formData.get('currentPassword'),\r\n    newPassword: formData.get('newPassword'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { currentPassword, newPassword } = validatedFields.data;\r\n\r\n  // IMPORTANT: Verify the current password first before attempting update.\r\n  // Supabase doesn't directly expose a \"verify password\" endpoint.\r\n  // The recommended way is to try signing in with the current password.\r\n  // This is a crucial security step.\r\n  const { error: signInError } = await supabase.auth.signInWithPassword({\r\n    email: user.email,\r\n    password: currentPassword,\r\n  });\r\n\r\n  if (signInError) {\r\n     // Handle specific Supabase auth error codes for sign-in\r\n     let errorMessage = 'Failed to verify current password.';\r\n     const fieldErrors: { currentPassword?: string[] } = {};\r\n\r\n     switch (signInError.code) {\r\n       case 'invalid_credentials':\r\n       case 'email_not_confirmed':\r\n         errorMessage = 'Incorrect current password.';\r\n         fieldErrors.currentPassword = ['Incorrect current password.'];\r\n         break;\r\n       case 'too_many_requests':\r\n         errorMessage = 'Too many failed attempts. Please wait before trying again.';\r\n         break;\r\n       case 'user_not_found':\r\n         errorMessage = 'Account not found. Please contact support.';\r\n         break;\r\n       default:\r\n         errorMessage = 'Unable to verify current password. Please try again.';\r\n     }\r\n\r\n     return {\r\n       message: errorMessage,\r\n       errors: fieldErrors,\r\n       success: false\r\n     };\r\n  }\r\n\r\n\r\n  // If sign-in was successful (current password is correct), proceed to update\r\n  try {\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      password: newPassword,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes for password update\r\n      let errorMessage = 'Failed to update password.';\r\n\r\n      switch (updateError.code) {\r\n        case 'weak_password':\r\n          errorMessage = 'Password is too weak. Please choose a stronger password.';\r\n          break;\r\n        case 'same_password':\r\n          errorMessage = 'New password must be different from your current password.';\r\n          break;\r\n        case 'password_too_short':\r\n          errorMessage = 'Password must be at least 6 characters long.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many password change requests. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update password. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Password updated successfully\r\n    return { message: 'Password updated successfully!', success: true };\r\n\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating password.', success: false };\r\n  }\r\n}\r\n\r\n\r\n// --- Enhanced Delete Account Security Actions ---\r\n\r\n// Check user's email and phone availability for delete account verification\r\nexport async function checkDeleteAccountVerificationOptions(): Promise<{\r\n  success: boolean;\r\n  hasEmail: boolean;\r\n  hasPhone: boolean;\r\n  message?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      hasEmail: false,\r\n      hasPhone: false,\r\n      message: \"Authentication required.\"\r\n    };\r\n  }\r\n\r\n  const hasEmail = !!(user.email && user.email.trim() !== '');\r\n  const hasPhone = !!(user.phone && user.phone.trim() !== '');\r\n\r\n  return {\r\n    success: true,\r\n    hasEmail,\r\n    hasPhone,\r\n  };\r\n}\r\n\r\n// Send OTP to email for delete account verification\r\nexport async function sendDeleteAccountOTP(): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n  email?: string;\r\n  isConfigurationError?: boolean;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no email found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: user.email,\r\n      options: {\r\n        shouldCreateUser: false, // Don't create new user\r\n      },\r\n    });\r\n\r\n    if (error) {\r\n      // Handle rate limit errors specifically\r\n      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {\r\n        return {\r\n          success: false,\r\n          message: \"Email rate limit exceeded. Please try again later.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: error.message || \"Failed to send verification code.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Verification code sent to your email address.\",\r\n      email: user.email,\r\n    };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred while sending verification code.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for delete account\r\nexport async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (error) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (error.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { success: false, message: errorMessage };\r\n    }\r\n\r\n    return { success: true, message: \"Verification successful.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify password for delete account (for phone users)\r\nexport async function verifyDeleteAccountPassword(password: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.phone) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no phone found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Verify current password by attempting to sign in\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      phone: user.phone,\r\n      password: password,\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        message: \"Invalid password. Please try again.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, message: \"Password verified successfully.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during password verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// --- Delete Account ---\r\n\r\nexport type DeleteAccountFormState = {\r\n  message: string | null;\r\n  success: boolean;\r\n};\r\n\r\nexport async function deleteCustomerAccount(\r\n  // No prevState or formData needed for this action\r\n): Promise<DeleteAccountFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  // Check for and clean up any storage data using hash-based structure\r\n  try {\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n    const bucketName = \"customers\"; // Correct bucket name (plural)\r\n    const userStoragePath = getScalableUserPath(user.id);\r\n\r\n    // Recursive function to delete all files and folders\r\n    const deleteRecursively = async (path: string): Promise<void> => {\r\n      const { data: items, error: listError } = await adminSupabase.storage\r\n        .from(bucketName)\r\n        .list(path);\r\n\r\n      if (listError) {\r\n        console.error(`Error listing files in ${path}:`, listError);\r\n        return;\r\n      }\r\n\r\n      if (!items || items.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Separate files and folders\r\n      const files: string[] = [];\r\n      const folders: string[] = [];\r\n\r\n      for (const item of items) {\r\n        const fullPath = path ? `${path}/${item.name}` : item.name;\r\n\r\n        if (item.metadata === null) {\r\n          // This is a folder\r\n          folders.push(fullPath);\r\n        } else {\r\n          // This is a file\r\n          files.push(fullPath);\r\n        }\r\n      }\r\n\r\n      // Delete all files in the current directory\r\n      if (files.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(bucketName)\r\n          .remove(files);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(`Error deleting files in ${path}:`, deleteError);\r\n        } else {\r\n          console.log(`Successfully deleted ${files.length} files in ${path}`);\r\n        }\r\n      }\r\n\r\n      // Recursively delete folders\r\n      for (const folder of folders) {\r\n        await deleteRecursively(folder);\r\n      }\r\n    };\r\n\r\n    // Start the recursive deletion from the user's root folder\r\n    await deleteRecursively(userStoragePath);\r\n\r\n    console.log('Successfully cleaned up customer storage data');\r\n  } catch (storageError) {\r\n    // Log but continue with deletion\r\n    console.error('Error checking/cleaning customer storage:', storageError);\r\n  }\r\n\r\n  // Use the admin client to delete the user and profile\r\n  try {\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Delete from customer_profiles table (CASCADE will handle related data)\r\n    console.log('Deleting customer profile...');\r\n    const { error: deleteProfileError } = await supabaseAdmin\r\n      .from('customer_profiles')\r\n      .delete()\r\n      .eq('id', user.id);\r\n\r\n    if (deleteProfileError) {\r\n      console.error('Error deleting customer profile:', deleteProfileError);\r\n      return { message: `Failed to delete customer profile: ${deleteProfileError.message}`, success: false };\r\n    }\r\n\r\n    console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');\r\n\r\n    // Sign out the user locally first (while the session is still valid)\r\n    await supabase.auth.signOut();\r\n\r\n    // Then delete the user using the admin client\r\n    // Using hard delete (shouldSoftDelete=false) to completely remove the user\r\n    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);\r\n\r\n    if (deleteUserError) {\r\n      console.error('Error deleting user account:', deleteUserError);\r\n      return { message: `Failed to delete account: ${deleteUserError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate paths if needed\r\n    revalidatePath(\"/\", \"layout\"); // Revalidate root layout\r\n\r\n    return { message: 'Account deleted successfully.', success: true };\r\n\r\n  } catch (error) {\r\n     console.error('Unexpected error during account deletion:', error);\r\n     return { message: 'An unexpected error occurred during account deletion.', success: false };\r\n  }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAwcsB,wCAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { getScalableUserPath } from '@/lib/utils/storage-paths';\r\nimport { z } from 'zod';\r\nimport { PasswordComplexitySchema } from '@/lib/schemas/authSchemas';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// --- Update Email ---\r\n\r\nconst UpdateEmailSchema = z.object({\r\n  newEmail: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type UpdateEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    newEmail?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerEmail(\r\n  _prevState: UpdateEmailFormState,\r\n  formData: FormData\r\n): Promise<UpdateEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdateEmailSchema.safeParse({\r\n    newEmail: formData.get('newEmail'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { newEmail } = validatedFields.data;\r\n\r\n  // Check if the new email is the same as the current one\r\n  if (newEmail === user.email) {\r\n    return { message: 'New email is the same as the current email.', success: false };\r\n  }\r\n\r\n  try {\r\n    // Update email in Supabase Auth\r\n    // This typically sends a confirmation email to both old and new addresses\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: newEmail,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes\r\n      let errorMessage = 'Failed to update email address.';\r\n\r\n      switch (updateError.code) {\r\n        case 'email_exists':\r\n          errorMessage = 'This email address is already registered with another account.';\r\n          break;\r\n        case 'invalid_email':\r\n          errorMessage = 'Please enter a valid email address.';\r\n          break;\r\n        case 'email_change_confirm_limit':\r\n          errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n          break;\r\n        case 'over_email_send_rate_limit':\r\n          errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n          break;\r\n        case 'email_not_confirmed':\r\n          errorMessage = 'Please confirm your current email address before changing it.';\r\n          break;\r\n        case 'same_email':\r\n          errorMessage = 'The new email address is the same as your current email.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update email address. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Revalidate relevant paths if needed, though email change might require user action (confirmation)\r\n    // revalidatePath('/dashboard/customer/settings');\r\n    // revalidatePath('/dashboard/customer/profile'); // Email is shown here\r\n\r\n    return {\r\n      message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Link Email ---\r\n\r\nconst LinkEmailSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type LinkEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function linkCustomerEmail(\r\n  _prevState: LinkEmailFormState,\r\n  formData: FormData\r\n): Promise<LinkEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = LinkEmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user already has an email (email update) or not (email linking)\r\n    const isEmailUpdate = !!user.email;\r\n\r\n    if (isEmailUpdate) {\r\n      // User already has email - use email change flow\r\n      const { error: authUpdateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (authUpdateError) {\r\n        // Handle specific Supabase auth error codes\r\n        let errorMessage = 'Failed to update email address.';\r\n\r\n        switch (authUpdateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n            break;\r\n          case 'email_not_confirmed':\r\n            errorMessage = 'Please confirm your current email address before changing it.';\r\n            break;\r\n          case 'same_email':\r\n            errorMessage = 'The new email address is the same as your current email.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to update email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n        success: true,\r\n      };\r\n    } else {\r\n      // User doesn't have email - directly link the email without OTP verification\r\n      // Supabase will automatically handle duplicate validation\r\n      const { error: updateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (updateError) {\r\n        let errorMessage = 'Failed to link email address.';\r\n\r\n        switch (updateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to link email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Email address linked successfully!',\r\n        success: true,\r\n      };\r\n    }\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while linking email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Verify Email OTP ---\r\n\r\nconst VerifyEmailOTPSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),\r\n});\r\n\r\nexport type VerifyEmailOTPFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n    otp?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function verifyEmailOTP(\r\n  _prevState: VerifyEmailOTPFormState,\r\n  formData: FormData\r\n): Promise<VerifyEmailOTPFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = VerifyEmailOTPSchema.safeParse({\r\n    email: formData.get('email'),\r\n    otp: formData.get('otp'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email, otp } = validatedFields.data;\r\n\r\n  try {\r\n    // Verify the OTP\r\n    const { error: verifyError } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (verifyError) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (verifyError.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // If OTP verification successful, update the user's email\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (updateError) {\r\n      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    return {\r\n      message: 'Email address linked successfully!',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while verifying code.', success: false };\r\n  }\r\n}\r\n\r\n// --- Update Password ---\r\n\r\nconst UpdatePasswordSchema = z.object({\r\n  currentPassword: z.string().min(1, 'Current password is required.'),\r\n  newPassword: PasswordComplexitySchema,\r\n});\r\n\r\nexport type UpdatePasswordFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    currentPassword?: string[];\r\n    newPassword?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerPassword(\r\n  _prevState: UpdatePasswordFormState,\r\n  formData: FormData\r\n): Promise<UpdatePasswordFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) { // Need email for re-authentication check\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdatePasswordSchema.safeParse({\r\n    currentPassword: formData.get('currentPassword'),\r\n    newPassword: formData.get('newPassword'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { currentPassword, newPassword } = validatedFields.data;\r\n\r\n  // IMPORTANT: Verify the current password first before attempting update.\r\n  // Supabase doesn't directly expose a \"verify password\" endpoint.\r\n  // The recommended way is to try signing in with the current password.\r\n  // This is a crucial security step.\r\n  const { error: signInError } = await supabase.auth.signInWithPassword({\r\n    email: user.email,\r\n    password: currentPassword,\r\n  });\r\n\r\n  if (signInError) {\r\n     // Handle specific Supabase auth error codes for sign-in\r\n     let errorMessage = 'Failed to verify current password.';\r\n     const fieldErrors: { currentPassword?: string[] } = {};\r\n\r\n     switch (signInError.code) {\r\n       case 'invalid_credentials':\r\n       case 'email_not_confirmed':\r\n         errorMessage = 'Incorrect current password.';\r\n         fieldErrors.currentPassword = ['Incorrect current password.'];\r\n         break;\r\n       case 'too_many_requests':\r\n         errorMessage = 'Too many failed attempts. Please wait before trying again.';\r\n         break;\r\n       case 'user_not_found':\r\n         errorMessage = 'Account not found. Please contact support.';\r\n         break;\r\n       default:\r\n         errorMessage = 'Unable to verify current password. Please try again.';\r\n     }\r\n\r\n     return {\r\n       message: errorMessage,\r\n       errors: fieldErrors,\r\n       success: false\r\n     };\r\n  }\r\n\r\n\r\n  // If sign-in was successful (current password is correct), proceed to update\r\n  try {\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      password: newPassword,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes for password update\r\n      let errorMessage = 'Failed to update password.';\r\n\r\n      switch (updateError.code) {\r\n        case 'weak_password':\r\n          errorMessage = 'Password is too weak. Please choose a stronger password.';\r\n          break;\r\n        case 'same_password':\r\n          errorMessage = 'New password must be different from your current password.';\r\n          break;\r\n        case 'password_too_short':\r\n          errorMessage = 'Password must be at least 6 characters long.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many password change requests. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update password. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Password updated successfully\r\n    return { message: 'Password updated successfully!', success: true };\r\n\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating password.', success: false };\r\n  }\r\n}\r\n\r\n\r\n// --- Enhanced Delete Account Security Actions ---\r\n\r\n// Check user's email and phone availability for delete account verification\r\nexport async function checkDeleteAccountVerificationOptions(): Promise<{\r\n  success: boolean;\r\n  hasEmail: boolean;\r\n  hasPhone: boolean;\r\n  message?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      hasEmail: false,\r\n      hasPhone: false,\r\n      message: \"Authentication required.\"\r\n    };\r\n  }\r\n\r\n  const hasEmail = !!(user.email && user.email.trim() !== '');\r\n  const hasPhone = !!(user.phone && user.phone.trim() !== '');\r\n\r\n  return {\r\n    success: true,\r\n    hasEmail,\r\n    hasPhone,\r\n  };\r\n}\r\n\r\n// Send OTP to email for delete account verification\r\nexport async function sendDeleteAccountOTP(): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n  email?: string;\r\n  isConfigurationError?: boolean;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no email found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: user.email,\r\n      options: {\r\n        shouldCreateUser: false, // Don't create new user\r\n      },\r\n    });\r\n\r\n    if (error) {\r\n      // Handle rate limit errors specifically\r\n      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {\r\n        return {\r\n          success: false,\r\n          message: \"Email rate limit exceeded. Please try again later.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: error.message || \"Failed to send verification code.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Verification code sent to your email address.\",\r\n      email: user.email,\r\n    };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred while sending verification code.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for delete account\r\nexport async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (error) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (error.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { success: false, message: errorMessage };\r\n    }\r\n\r\n    return { success: true, message: \"Verification successful.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify password for delete account (for phone users)\r\nexport async function verifyDeleteAccountPassword(password: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.phone) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no phone found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Verify current password by attempting to sign in\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      phone: user.phone,\r\n      password: password,\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        message: \"Invalid password. Please try again.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, message: \"Password verified successfully.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during password verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// --- Delete Account ---\r\n\r\nexport type DeleteAccountFormState = {\r\n  message: string | null;\r\n  success: boolean;\r\n};\r\n\r\nexport async function deleteCustomerAccount(\r\n  // No prevState or formData needed for this action\r\n): Promise<DeleteAccountFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  // Check for and clean up any storage data using hash-based structure\r\n  try {\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n    const bucketName = \"customers\"; // Correct bucket name (plural)\r\n    const userStoragePath = getScalableUserPath(user.id);\r\n\r\n    // Recursive function to delete all files and folders\r\n    const deleteRecursively = async (path: string): Promise<void> => {\r\n      const { data: items, error: listError } = await adminSupabase.storage\r\n        .from(bucketName)\r\n        .list(path);\r\n\r\n      if (listError) {\r\n        console.error(`Error listing files in ${path}:`, listError);\r\n        return;\r\n      }\r\n\r\n      if (!items || items.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Separate files and folders\r\n      const files: string[] = [];\r\n      const folders: string[] = [];\r\n\r\n      for (const item of items) {\r\n        const fullPath = path ? `${path}/${item.name}` : item.name;\r\n\r\n        if (item.metadata === null) {\r\n          // This is a folder\r\n          folders.push(fullPath);\r\n        } else {\r\n          // This is a file\r\n          files.push(fullPath);\r\n        }\r\n      }\r\n\r\n      // Delete all files in the current directory\r\n      if (files.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(bucketName)\r\n          .remove(files);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(`Error deleting files in ${path}:`, deleteError);\r\n        } else {\r\n          console.log(`Successfully deleted ${files.length} files in ${path}`);\r\n        }\r\n      }\r\n\r\n      // Recursively delete folders\r\n      for (const folder of folders) {\r\n        await deleteRecursively(folder);\r\n      }\r\n    };\r\n\r\n    // Start the recursive deletion from the user's root folder\r\n    await deleteRecursively(userStoragePath);\r\n\r\n    console.log('Successfully cleaned up customer storage data');\r\n  } catch (storageError) {\r\n    // Log but continue with deletion\r\n    console.error('Error checking/cleaning customer storage:', storageError);\r\n  }\r\n\r\n  // Use the admin client to delete the user and profile\r\n  try {\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Delete from customer_profiles table (CASCADE will handle related data)\r\n    console.log('Deleting customer profile...');\r\n    const { error: deleteProfileError } = await supabaseAdmin\r\n      .from('customer_profiles')\r\n      .delete()\r\n      .eq('id', user.id);\r\n\r\n    if (deleteProfileError) {\r\n      console.error('Error deleting customer profile:', deleteProfileError);\r\n      return { message: `Failed to delete customer profile: ${deleteProfileError.message}`, success: false };\r\n    }\r\n\r\n    console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');\r\n\r\n    // Sign out the user locally first (while the session is still valid)\r\n    await supabase.auth.signOut();\r\n\r\n    // Then delete the user using the admin client\r\n    // Using hard delete (shouldSoftDelete=false) to completely remove the user\r\n    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);\r\n\r\n    if (deleteUserError) {\r\n      console.error('Error deleting user account:', deleteUserError);\r\n      return { message: `Failed to delete account: ${deleteUserError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate paths if needed\r\n    revalidatePath(\"/\", \"layout\"); // Revalidate root layout\r\n\r\n    return { message: 'Account deleted successfully.', success: true };\r\n\r\n  } catch (error) {\r\n     console.error('Unexpected error during account deletion:', error);\r\n     return { message: 'An unexpected error occurred during account deletion.', success: false };\r\n  }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAwesB,uBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { getScalableUserPath } from '@/lib/utils/storage-paths';\r\nimport { z } from 'zod';\r\nimport { PasswordComplexitySchema } from '@/lib/schemas/authSchemas';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// --- Update Email ---\r\n\r\nconst UpdateEmailSchema = z.object({\r\n  newEmail: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type UpdateEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    newEmail?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerEmail(\r\n  _prevState: UpdateEmailFormState,\r\n  formData: FormData\r\n): Promise<UpdateEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdateEmailSchema.safeParse({\r\n    newEmail: formData.get('newEmail'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { newEmail } = validatedFields.data;\r\n\r\n  // Check if the new email is the same as the current one\r\n  if (newEmail === user.email) {\r\n    return { message: 'New email is the same as the current email.', success: false };\r\n  }\r\n\r\n  try {\r\n    // Update email in Supabase Auth\r\n    // This typically sends a confirmation email to both old and new addresses\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: newEmail,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes\r\n      let errorMessage = 'Failed to update email address.';\r\n\r\n      switch (updateError.code) {\r\n        case 'email_exists':\r\n          errorMessage = 'This email address is already registered with another account.';\r\n          break;\r\n        case 'invalid_email':\r\n          errorMessage = 'Please enter a valid email address.';\r\n          break;\r\n        case 'email_change_confirm_limit':\r\n          errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n          break;\r\n        case 'over_email_send_rate_limit':\r\n          errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n          break;\r\n        case 'email_not_confirmed':\r\n          errorMessage = 'Please confirm your current email address before changing it.';\r\n          break;\r\n        case 'same_email':\r\n          errorMessage = 'The new email address is the same as your current email.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update email address. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Revalidate relevant paths if needed, though email change might require user action (confirmation)\r\n    // revalidatePath('/dashboard/customer/settings');\r\n    // revalidatePath('/dashboard/customer/profile'); // Email is shown here\r\n\r\n    return {\r\n      message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Link Email ---\r\n\r\nconst LinkEmailSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type LinkEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function linkCustomerEmail(\r\n  _prevState: LinkEmailFormState,\r\n  formData: FormData\r\n): Promise<LinkEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = LinkEmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user already has an email (email update) or not (email linking)\r\n    const isEmailUpdate = !!user.email;\r\n\r\n    if (isEmailUpdate) {\r\n      // User already has email - use email change flow\r\n      const { error: authUpdateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (authUpdateError) {\r\n        // Handle specific Supabase auth error codes\r\n        let errorMessage = 'Failed to update email address.';\r\n\r\n        switch (authUpdateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n            break;\r\n          case 'email_not_confirmed':\r\n            errorMessage = 'Please confirm your current email address before changing it.';\r\n            break;\r\n          case 'same_email':\r\n            errorMessage = 'The new email address is the same as your current email.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to update email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n        success: true,\r\n      };\r\n    } else {\r\n      // User doesn't have email - directly link the email without OTP verification\r\n      // Supabase will automatically handle duplicate validation\r\n      const { error: updateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (updateError) {\r\n        let errorMessage = 'Failed to link email address.';\r\n\r\n        switch (updateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to link email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Email address linked successfully!',\r\n        success: true,\r\n      };\r\n    }\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while linking email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Verify Email OTP ---\r\n\r\nconst VerifyEmailOTPSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),\r\n});\r\n\r\nexport type VerifyEmailOTPFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n    otp?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function verifyEmailOTP(\r\n  _prevState: VerifyEmailOTPFormState,\r\n  formData: FormData\r\n): Promise<VerifyEmailOTPFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = VerifyEmailOTPSchema.safeParse({\r\n    email: formData.get('email'),\r\n    otp: formData.get('otp'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email, otp } = validatedFields.data;\r\n\r\n  try {\r\n    // Verify the OTP\r\n    const { error: verifyError } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (verifyError) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (verifyError.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // If OTP verification successful, update the user's email\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (updateError) {\r\n      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    return {\r\n      message: 'Email address linked successfully!',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while verifying code.', success: false };\r\n  }\r\n}\r\n\r\n// --- Update Password ---\r\n\r\nconst UpdatePasswordSchema = z.object({\r\n  currentPassword: z.string().min(1, 'Current password is required.'),\r\n  newPassword: PasswordComplexitySchema,\r\n});\r\n\r\nexport type UpdatePasswordFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    currentPassword?: string[];\r\n    newPassword?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerPassword(\r\n  _prevState: UpdatePasswordFormState,\r\n  formData: FormData\r\n): Promise<UpdatePasswordFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) { // Need email for re-authentication check\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdatePasswordSchema.safeParse({\r\n    currentPassword: formData.get('currentPassword'),\r\n    newPassword: formData.get('newPassword'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { currentPassword, newPassword } = validatedFields.data;\r\n\r\n  // IMPORTANT: Verify the current password first before attempting update.\r\n  // Supabase doesn't directly expose a \"verify password\" endpoint.\r\n  // The recommended way is to try signing in with the current password.\r\n  // This is a crucial security step.\r\n  const { error: signInError } = await supabase.auth.signInWithPassword({\r\n    email: user.email,\r\n    password: currentPassword,\r\n  });\r\n\r\n  if (signInError) {\r\n     // Handle specific Supabase auth error codes for sign-in\r\n     let errorMessage = 'Failed to verify current password.';\r\n     const fieldErrors: { currentPassword?: string[] } = {};\r\n\r\n     switch (signInError.code) {\r\n       case 'invalid_credentials':\r\n       case 'email_not_confirmed':\r\n         errorMessage = 'Incorrect current password.';\r\n         fieldErrors.currentPassword = ['Incorrect current password.'];\r\n         break;\r\n       case 'too_many_requests':\r\n         errorMessage = 'Too many failed attempts. Please wait before trying again.';\r\n         break;\r\n       case 'user_not_found':\r\n         errorMessage = 'Account not found. Please contact support.';\r\n         break;\r\n       default:\r\n         errorMessage = 'Unable to verify current password. Please try again.';\r\n     }\r\n\r\n     return {\r\n       message: errorMessage,\r\n       errors: fieldErrors,\r\n       success: false\r\n     };\r\n  }\r\n\r\n\r\n  // If sign-in was successful (current password is correct), proceed to update\r\n  try {\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      password: newPassword,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes for password update\r\n      let errorMessage = 'Failed to update password.';\r\n\r\n      switch (updateError.code) {\r\n        case 'weak_password':\r\n          errorMessage = 'Password is too weak. Please choose a stronger password.';\r\n          break;\r\n        case 'same_password':\r\n          errorMessage = 'New password must be different from your current password.';\r\n          break;\r\n        case 'password_too_short':\r\n          errorMessage = 'Password must be at least 6 characters long.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many password change requests. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update password. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Password updated successfully\r\n    return { message: 'Password updated successfully!', success: true };\r\n\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating password.', success: false };\r\n  }\r\n}\r\n\r\n\r\n// --- Enhanced Delete Account Security Actions ---\r\n\r\n// Check user's email and phone availability for delete account verification\r\nexport async function checkDeleteAccountVerificationOptions(): Promise<{\r\n  success: boolean;\r\n  hasEmail: boolean;\r\n  hasPhone: boolean;\r\n  message?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      hasEmail: false,\r\n      hasPhone: false,\r\n      message: \"Authentication required.\"\r\n    };\r\n  }\r\n\r\n  const hasEmail = !!(user.email && user.email.trim() !== '');\r\n  const hasPhone = !!(user.phone && user.phone.trim() !== '');\r\n\r\n  return {\r\n    success: true,\r\n    hasEmail,\r\n    hasPhone,\r\n  };\r\n}\r\n\r\n// Send OTP to email for delete account verification\r\nexport async function sendDeleteAccountOTP(): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n  email?: string;\r\n  isConfigurationError?: boolean;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no email found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: user.email,\r\n      options: {\r\n        shouldCreateUser: false, // Don't create new user\r\n      },\r\n    });\r\n\r\n    if (error) {\r\n      // Handle rate limit errors specifically\r\n      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {\r\n        return {\r\n          success: false,\r\n          message: \"Email rate limit exceeded. Please try again later.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: error.message || \"Failed to send verification code.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Verification code sent to your email address.\",\r\n      email: user.email,\r\n    };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred while sending verification code.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for delete account\r\nexport async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (error) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (error.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { success: false, message: errorMessage };\r\n    }\r\n\r\n    return { success: true, message: \"Verification successful.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify password for delete account (for phone users)\r\nexport async function verifyDeleteAccountPassword(password: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.phone) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no phone found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Verify current password by attempting to sign in\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      phone: user.phone,\r\n      password: password,\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        message: \"Invalid password. Please try again.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, message: \"Password verified successfully.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during password verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// --- Delete Account ---\r\n\r\nexport type DeleteAccountFormState = {\r\n  message: string | null;\r\n  success: boolean;\r\n};\r\n\r\nexport async function deleteCustomerAccount(\r\n  // No prevState or formData needed for this action\r\n): Promise<DeleteAccountFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  // Check for and clean up any storage data using hash-based structure\r\n  try {\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n    const bucketName = \"customers\"; // Correct bucket name (plural)\r\n    const userStoragePath = getScalableUserPath(user.id);\r\n\r\n    // Recursive function to delete all files and folders\r\n    const deleteRecursively = async (path: string): Promise<void> => {\r\n      const { data: items, error: listError } = await adminSupabase.storage\r\n        .from(bucketName)\r\n        .list(path);\r\n\r\n      if (listError) {\r\n        console.error(`Error listing files in ${path}:`, listError);\r\n        return;\r\n      }\r\n\r\n      if (!items || items.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Separate files and folders\r\n      const files: string[] = [];\r\n      const folders: string[] = [];\r\n\r\n      for (const item of items) {\r\n        const fullPath = path ? `${path}/${item.name}` : item.name;\r\n\r\n        if (item.metadata === null) {\r\n          // This is a folder\r\n          folders.push(fullPath);\r\n        } else {\r\n          // This is a file\r\n          files.push(fullPath);\r\n        }\r\n      }\r\n\r\n      // Delete all files in the current directory\r\n      if (files.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(bucketName)\r\n          .remove(files);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(`Error deleting files in ${path}:`, deleteError);\r\n        } else {\r\n          console.log(`Successfully deleted ${files.length} files in ${path}`);\r\n        }\r\n      }\r\n\r\n      // Recursively delete folders\r\n      for (const folder of folders) {\r\n        await deleteRecursively(folder);\r\n      }\r\n    };\r\n\r\n    // Start the recursive deletion from the user's root folder\r\n    await deleteRecursively(userStoragePath);\r\n\r\n    console.log('Successfully cleaned up customer storage data');\r\n  } catch (storageError) {\r\n    // Log but continue with deletion\r\n    console.error('Error checking/cleaning customer storage:', storageError);\r\n  }\r\n\r\n  // Use the admin client to delete the user and profile\r\n  try {\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Delete from customer_profiles table (CASCADE will handle related data)\r\n    console.log('Deleting customer profile...');\r\n    const { error: deleteProfileError } = await supabaseAdmin\r\n      .from('customer_profiles')\r\n      .delete()\r\n      .eq('id', user.id);\r\n\r\n    if (deleteProfileError) {\r\n      console.error('Error deleting customer profile:', deleteProfileError);\r\n      return { message: `Failed to delete customer profile: ${deleteProfileError.message}`, success: false };\r\n    }\r\n\r\n    console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');\r\n\r\n    // Sign out the user locally first (while the session is still valid)\r\n    await supabase.auth.signOut();\r\n\r\n    // Then delete the user using the admin client\r\n    // Using hard delete (shouldSoftDelete=false) to completely remove the user\r\n    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);\r\n\r\n    if (deleteUserError) {\r\n      console.error('Error deleting user account:', deleteUserError);\r\n      return { message: `Failed to delete account: ${deleteUserError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate paths if needed\r\n    revalidatePath(\"/\", \"layout\"); // Revalidate root layout\r\n\r\n    return { message: 'Account deleted successfully.', success: true };\r\n\r\n  } catch (error) {\r\n     console.error('Unexpected error during account deletion:', error);\r\n     return { message: 'An unexpected error occurred during account deletion.', success: false };\r\n  }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAiiBsB,yBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { getScalableUserPath } from '@/lib/utils/storage-paths';\r\nimport { z } from 'zod';\r\nimport { PasswordComplexitySchema } from '@/lib/schemas/authSchemas';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// --- Update Email ---\r\n\r\nconst UpdateEmailSchema = z.object({\r\n  newEmail: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type UpdateEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    newEmail?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerEmail(\r\n  _prevState: UpdateEmailFormState,\r\n  formData: FormData\r\n): Promise<UpdateEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdateEmailSchema.safeParse({\r\n    newEmail: formData.get('newEmail'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { newEmail } = validatedFields.data;\r\n\r\n  // Check if the new email is the same as the current one\r\n  if (newEmail === user.email) {\r\n    return { message: 'New email is the same as the current email.', success: false };\r\n  }\r\n\r\n  try {\r\n    // Update email in Supabase Auth\r\n    // This typically sends a confirmation email to both old and new addresses\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: newEmail,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes\r\n      let errorMessage = 'Failed to update email address.';\r\n\r\n      switch (updateError.code) {\r\n        case 'email_exists':\r\n          errorMessage = 'This email address is already registered with another account.';\r\n          break;\r\n        case 'invalid_email':\r\n          errorMessage = 'Please enter a valid email address.';\r\n          break;\r\n        case 'email_change_confirm_limit':\r\n          errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n          break;\r\n        case 'over_email_send_rate_limit':\r\n          errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n          break;\r\n        case 'email_not_confirmed':\r\n          errorMessage = 'Please confirm your current email address before changing it.';\r\n          break;\r\n        case 'same_email':\r\n          errorMessage = 'The new email address is the same as your current email.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update email address. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Revalidate relevant paths if needed, though email change might require user action (confirmation)\r\n    // revalidatePath('/dashboard/customer/settings');\r\n    // revalidatePath('/dashboard/customer/profile'); // Email is shown here\r\n\r\n    return {\r\n      message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Link Email ---\r\n\r\nconst LinkEmailSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type LinkEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function linkCustomerEmail(\r\n  _prevState: LinkEmailFormState,\r\n  formData: FormData\r\n): Promise<LinkEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = LinkEmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user already has an email (email update) or not (email linking)\r\n    const isEmailUpdate = !!user.email;\r\n\r\n    if (isEmailUpdate) {\r\n      // User already has email - use email change flow\r\n      const { error: authUpdateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (authUpdateError) {\r\n        // Handle specific Supabase auth error codes\r\n        let errorMessage = 'Failed to update email address.';\r\n\r\n        switch (authUpdateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n            break;\r\n          case 'email_not_confirmed':\r\n            errorMessage = 'Please confirm your current email address before changing it.';\r\n            break;\r\n          case 'same_email':\r\n            errorMessage = 'The new email address is the same as your current email.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to update email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n        success: true,\r\n      };\r\n    } else {\r\n      // User doesn't have email - directly link the email without OTP verification\r\n      // Supabase will automatically handle duplicate validation\r\n      const { error: updateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (updateError) {\r\n        let errorMessage = 'Failed to link email address.';\r\n\r\n        switch (updateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to link email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Email address linked successfully!',\r\n        success: true,\r\n      };\r\n    }\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while linking email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Verify Email OTP ---\r\n\r\nconst VerifyEmailOTPSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),\r\n});\r\n\r\nexport type VerifyEmailOTPFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n    otp?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function verifyEmailOTP(\r\n  _prevState: VerifyEmailOTPFormState,\r\n  formData: FormData\r\n): Promise<VerifyEmailOTPFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = VerifyEmailOTPSchema.safeParse({\r\n    email: formData.get('email'),\r\n    otp: formData.get('otp'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email, otp } = validatedFields.data;\r\n\r\n  try {\r\n    // Verify the OTP\r\n    const { error: verifyError } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (verifyError) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (verifyError.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // If OTP verification successful, update the user's email\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (updateError) {\r\n      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    return {\r\n      message: 'Email address linked successfully!',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while verifying code.', success: false };\r\n  }\r\n}\r\n\r\n// --- Update Password ---\r\n\r\nconst UpdatePasswordSchema = z.object({\r\n  currentPassword: z.string().min(1, 'Current password is required.'),\r\n  newPassword: PasswordComplexitySchema,\r\n});\r\n\r\nexport type UpdatePasswordFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    currentPassword?: string[];\r\n    newPassword?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerPassword(\r\n  _prevState: UpdatePasswordFormState,\r\n  formData: FormData\r\n): Promise<UpdatePasswordFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) { // Need email for re-authentication check\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdatePasswordSchema.safeParse({\r\n    currentPassword: formData.get('currentPassword'),\r\n    newPassword: formData.get('newPassword'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { currentPassword, newPassword } = validatedFields.data;\r\n\r\n  // IMPORTANT: Verify the current password first before attempting update.\r\n  // Supabase doesn't directly expose a \"verify password\" endpoint.\r\n  // The recommended way is to try signing in with the current password.\r\n  // This is a crucial security step.\r\n  const { error: signInError } = await supabase.auth.signInWithPassword({\r\n    email: user.email,\r\n    password: currentPassword,\r\n  });\r\n\r\n  if (signInError) {\r\n     // Handle specific Supabase auth error codes for sign-in\r\n     let errorMessage = 'Failed to verify current password.';\r\n     const fieldErrors: { currentPassword?: string[] } = {};\r\n\r\n     switch (signInError.code) {\r\n       case 'invalid_credentials':\r\n       case 'email_not_confirmed':\r\n         errorMessage = 'Incorrect current password.';\r\n         fieldErrors.currentPassword = ['Incorrect current password.'];\r\n         break;\r\n       case 'too_many_requests':\r\n         errorMessage = 'Too many failed attempts. Please wait before trying again.';\r\n         break;\r\n       case 'user_not_found':\r\n         errorMessage = 'Account not found. Please contact support.';\r\n         break;\r\n       default:\r\n         errorMessage = 'Unable to verify current password. Please try again.';\r\n     }\r\n\r\n     return {\r\n       message: errorMessage,\r\n       errors: fieldErrors,\r\n       success: false\r\n     };\r\n  }\r\n\r\n\r\n  // If sign-in was successful (current password is correct), proceed to update\r\n  try {\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      password: newPassword,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes for password update\r\n      let errorMessage = 'Failed to update password.';\r\n\r\n      switch (updateError.code) {\r\n        case 'weak_password':\r\n          errorMessage = 'Password is too weak. Please choose a stronger password.';\r\n          break;\r\n        case 'same_password':\r\n          errorMessage = 'New password must be different from your current password.';\r\n          break;\r\n        case 'password_too_short':\r\n          errorMessage = 'Password must be at least 6 characters long.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many password change requests. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update password. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Password updated successfully\r\n    return { message: 'Password updated successfully!', success: true };\r\n\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating password.', success: false };\r\n  }\r\n}\r\n\r\n\r\n// --- Enhanced Delete Account Security Actions ---\r\n\r\n// Check user's email and phone availability for delete account verification\r\nexport async function checkDeleteAccountVerificationOptions(): Promise<{\r\n  success: boolean;\r\n  hasEmail: boolean;\r\n  hasPhone: boolean;\r\n  message?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      hasEmail: false,\r\n      hasPhone: false,\r\n      message: \"Authentication required.\"\r\n    };\r\n  }\r\n\r\n  const hasEmail = !!(user.email && user.email.trim() !== '');\r\n  const hasPhone = !!(user.phone && user.phone.trim() !== '');\r\n\r\n  return {\r\n    success: true,\r\n    hasEmail,\r\n    hasPhone,\r\n  };\r\n}\r\n\r\n// Send OTP to email for delete account verification\r\nexport async function sendDeleteAccountOTP(): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n  email?: string;\r\n  isConfigurationError?: boolean;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no email found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: user.email,\r\n      options: {\r\n        shouldCreateUser: false, // Don't create new user\r\n      },\r\n    });\r\n\r\n    if (error) {\r\n      // Handle rate limit errors specifically\r\n      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {\r\n        return {\r\n          success: false,\r\n          message: \"Email rate limit exceeded. Please try again later.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: error.message || \"Failed to send verification code.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Verification code sent to your email address.\",\r\n      email: user.email,\r\n    };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred while sending verification code.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for delete account\r\nexport async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (error) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (error.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { success: false, message: errorMessage };\r\n    }\r\n\r\n    return { success: true, message: \"Verification successful.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify password for delete account (for phone users)\r\nexport async function verifyDeleteAccountPassword(password: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.phone) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no phone found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Verify current password by attempting to sign in\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      phone: user.phone,\r\n      password: password,\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        message: \"Invalid password. Please try again.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, message: \"Password verified successfully.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during password verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// --- Delete Account ---\r\n\r\nexport type DeleteAccountFormState = {\r\n  message: string | null;\r\n  success: boolean;\r\n};\r\n\r\nexport async function deleteCustomerAccount(\r\n  // No prevState or formData needed for this action\r\n): Promise<DeleteAccountFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  // Check for and clean up any storage data using hash-based structure\r\n  try {\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n    const bucketName = \"customers\"; // Correct bucket name (plural)\r\n    const userStoragePath = getScalableUserPath(user.id);\r\n\r\n    // Recursive function to delete all files and folders\r\n    const deleteRecursively = async (path: string): Promise<void> => {\r\n      const { data: items, error: listError } = await adminSupabase.storage\r\n        .from(bucketName)\r\n        .list(path);\r\n\r\n      if (listError) {\r\n        console.error(`Error listing files in ${path}:`, listError);\r\n        return;\r\n      }\r\n\r\n      if (!items || items.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Separate files and folders\r\n      const files: string[] = [];\r\n      const folders: string[] = [];\r\n\r\n      for (const item of items) {\r\n        const fullPath = path ? `${path}/${item.name}` : item.name;\r\n\r\n        if (item.metadata === null) {\r\n          // This is a folder\r\n          folders.push(fullPath);\r\n        } else {\r\n          // This is a file\r\n          files.push(fullPath);\r\n        }\r\n      }\r\n\r\n      // Delete all files in the current directory\r\n      if (files.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(bucketName)\r\n          .remove(files);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(`Error deleting files in ${path}:`, deleteError);\r\n        } else {\r\n          console.log(`Successfully deleted ${files.length} files in ${path}`);\r\n        }\r\n      }\r\n\r\n      // Recursively delete folders\r\n      for (const folder of folders) {\r\n        await deleteRecursively(folder);\r\n      }\r\n    };\r\n\r\n    // Start the recursive deletion from the user's root folder\r\n    await deleteRecursively(userStoragePath);\r\n\r\n    console.log('Successfully cleaned up customer storage data');\r\n  } catch (storageError) {\r\n    // Log but continue with deletion\r\n    console.error('Error checking/cleaning customer storage:', storageError);\r\n  }\r\n\r\n  // Use the admin client to delete the user and profile\r\n  try {\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Delete from customer_profiles table (CASCADE will handle related data)\r\n    console.log('Deleting customer profile...');\r\n    const { error: deleteProfileError } = await supabaseAdmin\r\n      .from('customer_profiles')\r\n      .delete()\r\n      .eq('id', user.id);\r\n\r\n    if (deleteProfileError) {\r\n      console.error('Error deleting customer profile:', deleteProfileError);\r\n      return { message: `Failed to delete customer profile: ${deleteProfileError.message}`, success: false };\r\n    }\r\n\r\n    console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');\r\n\r\n    // Sign out the user locally first (while the session is still valid)\r\n    await supabase.auth.signOut();\r\n\r\n    // Then delete the user using the admin client\r\n    // Using hard delete (shouldSoftDelete=false) to completely remove the user\r\n    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);\r\n\r\n    if (deleteUserError) {\r\n      console.error('Error deleting user account:', deleteUserError);\r\n      return { message: `Failed to delete account: ${deleteUserError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate paths if needed\r\n    revalidatePath(\"/\", \"layout\"); // Revalidate root layout\r\n\r\n    return { message: 'Account deleted successfully.', success: true };\r\n\r\n  } catch (error) {\r\n     console.error('Unexpected error during account deletion:', error);\r\n     return { message: 'An unexpected error occurred during account deletion.', success: false };\r\n  }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0kBsB,8BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\ninterface DialogContentProps extends React.ComponentProps<typeof DialogPrimitive.Content> {\r\n  hideClose?: boolean;\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  hideClose = false,\r\n  ...props\r\n}: DialogContentProps) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {!hideClose && (\r\n          <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer\">\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAMA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,YAAY,KAAK,EACjB,GAAG,OACgB;IACnB,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,CAAC,2BACA,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/input-otp.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { OTPInput, OTPInputContext } from \"input-otp\"\r\nimport { MinusIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction InputOTP({\r\n  className,\r\n  containerClassName,\r\n  ...props\r\n}: React.ComponentProps<typeof OTPInput> & {\r\n  containerClassName?: string\r\n}) {\r\n  return (\r\n    <OTPInput\r\n      data-slot=\"input-otp\"\r\n      containerClassName={cn(\r\n        \"flex items-center gap-2 has-disabled:opacity-50\",\r\n        containerClassName\r\n      )}\r\n      className={cn(\"disabled:cursor-not-allowed\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction InputOTPGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"input-otp-group\"\r\n      className={cn(\"flex items-center\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction InputOTPSlot({\r\n  index,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  index: number\r\n}) {\r\n  const inputOTPContext = React.useContext(OTPInputContext)\r\n  const { char, hasFakeCaret, isActive } = inputOTPContext?.slots[index] ?? {}\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"input-otp-slot\"\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {char}\r\n      {hasFakeCaret && (\r\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\r\n          <div className=\"animate-caret-blink bg-foreground h-4 w-px duration-1000\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction InputOTPSeparator({ ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div data-slot=\"input-otp-separator\" role=\"separator\" {...props}>\r\n      <MinusIcon />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,kBAAkB,EAClB,GAAG,OAGJ;IACC,qBACE,8OAAC,8IAAA,CAAA,WAAQ;QACP,aAAU;QACV,oBAAoB,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACnB,mDACA;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;QAClC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,KAAK,EACL,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,8IAAA,CAAA,kBAAe;IACxD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,iBAAiB,KAAK,CAAC,MAAM,IAAI,CAAC;IAE3E,qBACE,8OAAC;QACC,aAAU;QACV,eAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4fACA;QAED,GAAG,KAAK;;YAER;YACA,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;AAEA,SAAS,kBAAkB,EAAE,GAAG,OAAoC;IAClE,qBACE,8OAAC;QAAI,aAAU;QAAsB,MAAK;QAAa,GAAG,KAAK;kBAC7D,cAAA,8OAAC,wMAAA,CAAA,YAAS;;;;;;;;;;AAGhB", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/DeleteAccountSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useTransition, useEffect } from 'react';\r\nimport {\r\n  deleteCustomerAccount,\r\n  checkDeleteAccountVerificationOptions,\r\n  sendDeleteAccountOTP,\r\n  verifyDeleteAccountOTP,\r\n  verifyDeleteAccountPassword\r\n} from './actions';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n  InputOTP,\r\n  InputOTPGroup,\r\n  InputOTPSlot,\r\n} from '@/components/ui/input-otp';\r\nimport { toast } from 'sonner';\r\nimport { useRouter } from 'next/navigation';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Loader2, Trash2, AlertTriangle, Mail, Smartphone, XCircle } from 'lucide-react';\r\n\r\ntype VerificationStep = 'initial' | 'choose-method' | 'email-otp' | 'password' | 'final-confirm';\r\n\r\nexport function DeleteAccountSection() {\r\n  const router = useRouter();\r\n  const [_isPending, startTransition] = useTransition();\r\n  const [deleteConfirmText, setDeleteConfirmText] = useState(\"\");\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\r\n\r\n  // Enhanced security state\r\n  const [verificationStep, setVerificationStep] = useState<VerificationStep>('initial');\r\n  const [hasEmail, setHasEmail] = useState(false);\r\n  const [hasPhone, setHasPhone] = useState(false);\r\n  const [userEmail, setUserEmail] = useState('');\r\n  const [_selectedMethod, setSelectedMethod] = useState<'email' | 'password' | null>(null);\r\n  const [otp, setOtp] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [isVerifying, setIsVerifying] = useState(false);\r\n  const [isSendingOTP, setIsSendingOTP] = useState(false);\r\n  const [isVerified, setIsVerified] = useState(false);\r\n  const [isCheckingOptions, setIsCheckingOptions] = useState(false);\r\n\r\n  // Check verification options when dialog opens\r\n  useEffect(() => {\r\n    if (isDialogOpen && verificationStep === 'initial') {\r\n      checkVerificationOptions();\r\n    }\r\n  }, [isDialogOpen, verificationStep]);\r\n\r\n  const checkVerificationOptions = async () => {\r\n    setIsCheckingOptions(true);\r\n    try {\r\n      const result = await checkDeleteAccountVerificationOptions();\r\n      if (result.success) {\r\n        setHasEmail(result.hasEmail);\r\n        setHasPhone(result.hasPhone);\r\n\r\n        // Determine next step based on available options\r\n        if (result.hasEmail && result.hasPhone) {\r\n          setVerificationStep('choose-method');\r\n        } else if (result.hasEmail) {\r\n          setSelectedMethod('email');\r\n          setVerificationStep('email-otp');\r\n        } else if (result.hasPhone) {\r\n          setSelectedMethod('password');\r\n          setVerificationStep('password');\r\n        } else {\r\n          // No email or phone - proceed with just DELETE confirmation\r\n          setVerificationStep('final-confirm');\r\n        }\r\n      } else {\r\n        toast.error(result.message || 'Failed to check verification options');\r\n      }\r\n    } catch (_error) {\r\n      toast.error('An error occurred while checking verification options');\r\n    } finally {\r\n      setIsCheckingOptions(false);\r\n    }\r\n  };\r\n\r\n  const handleMethodSelection = (method: 'email' | 'password') => {\r\n    setSelectedMethod(method);\r\n    if (method === 'email') {\r\n      setVerificationStep('email-otp');\r\n    } else {\r\n      setVerificationStep('password');\r\n    }\r\n  };\r\n\r\n  const handleSendOTP = async () => {\r\n    setIsSendingOTP(true);\r\n    try {\r\n      const result = await sendDeleteAccountOTP();\r\n      if (result.success) {\r\n        toast.success(result.message);\r\n        if (result.email) {\r\n          setUserEmail(result.email);\r\n        }\r\n      } else {\r\n        toast.error(result.message);\r\n        if (result.isConfigurationError) {\r\n          // Don't proceed if configuration error\r\n          return;\r\n        }\r\n      }\r\n    } catch (_error) {\r\n      toast.error('Failed to send verification code');\r\n    } finally {\r\n      setIsSendingOTP(false);\r\n    }\r\n  };\r\n\r\n  const handleVerifyOTP = async () => {\r\n    if (otp.length !== 6) {\r\n      toast.error('Please enter a valid 6-digit code');\r\n      return;\r\n    }\r\n\r\n    setIsVerifying(true);\r\n    try {\r\n      const result = await verifyDeleteAccountOTP(userEmail, otp);\r\n      if (result.success) {\r\n        toast.success(result.message);\r\n        setIsVerified(true);\r\n        setVerificationStep('final-confirm');\r\n      } else {\r\n        toast.error(result.message);\r\n      }\r\n    } catch (_error) {\r\n      toast.error('Failed to verify code');\r\n    } finally {\r\n      setIsVerifying(false);\r\n    }\r\n  };\r\n\r\n  const handleVerifyPassword = async () => {\r\n    if (!password.trim()) {\r\n      toast.error('Please enter your password');\r\n      return;\r\n    }\r\n\r\n    setIsVerifying(true);\r\n    try {\r\n      const result = await verifyDeleteAccountPassword(password);\r\n      if (result.success) {\r\n        toast.success(result.message);\r\n        setIsVerified(true);\r\n        setVerificationStep('final-confirm');\r\n      } else {\r\n        toast.error(result.message);\r\n      }\r\n    } catch (_error) {\r\n      toast.error('Failed to verify password');\r\n    } finally {\r\n      setIsVerifying(false);\r\n    }\r\n  };\r\n\r\n  // Handle final account deletion\r\n  const handleDeleteAccount = (e: React.MouseEvent) => {\r\n    // Prevent the default action which would close the dialog\r\n    e.preventDefault();\r\n\r\n    if (deleteConfirmText !== \"DELETE\") {\r\n      toast.error('Please type \"DELETE\" to confirm');\r\n      return;\r\n    }\r\n\r\n    // Check if verification is required and completed\r\n    if ((hasEmail || hasPhone) && !isVerified) {\r\n      toast.error('Please complete verification first');\r\n      return;\r\n    }\r\n\r\n    setIsDeleting(true);\r\n    startTransition(async () => {\r\n      try {\r\n        // Show a toast before making the request\r\n        toast.success(\"Processing account deletion...\");\r\n\r\n        // Wrap the deletion in a try-catch to handle potential errors\r\n        try {\r\n          const _result = await deleteCustomerAccount();\r\n\r\n          // If we get here, the account was successfully deleted\r\n          // The server action already signs out the user, so we just need to redirect\r\n          resetDialogState();\r\n          router.push(\"/\");\r\n        } catch (error: unknown) {\r\n          // Check if the error is because the response is undefined (which happens after successful deletion)\r\n          if (error instanceof Error && error.message.includes(\"Cannot read properties of undefined\")) {\r\n            // This likely means the account was deleted successfully but we lost the connection\r\n            // because the user was signed out\r\n            resetDialogState();\r\n            router.push(\"/\");\r\n          } else {\r\n            // This is a real error\r\n            console.error(\"Error in account deletion:\", error);\r\n            toast.error(\"Failed to delete account\");\r\n            setIsDeleting(false);\r\n          }\r\n        }\r\n      } catch (outerError) {\r\n        // Handle any other errors that might occur\r\n        console.error(\"Unexpected error during account deletion:\", outerError);\r\n        toast.error(\"An unexpected error occurred\");\r\n        setIsDeleting(false);\r\n      }\r\n    });\r\n  };\r\n\r\n  const resetDialogState = () => {\r\n    setDeleteConfirmText(\"\");\r\n    setIsDialogOpen(false);\r\n    setVerificationStep('initial');\r\n    setSelectedMethod(null);\r\n    setOtp('');\r\n    setPassword('');\r\n    setIsVerified(false);\r\n    setUserEmail('');\r\n    setIsCheckingOptions(false);\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.4, delay: 0.2 }}\r\n      className=\"rounded-lg border border-red-200 dark:border-red-800/30 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 transition-all duration-300 hover:shadow-lg\"\r\n    >\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-red-100 dark:border-red-800/30\">\r\n        <div className=\"p-2 rounded-lg bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 self-start\">\r\n          <Trash2 className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-red-600 dark:text-red-400\">\r\n            Delete Account\r\n          </h3>\r\n          <p className=\"text-xs text-red-500/70 dark:text-red-400/70 mt-0.5\">\r\n            Permanently delete your account and all associated data\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30 mb-4\">\r\n        <div className=\"flex items-start gap-2\">\r\n          <AlertTriangle className=\"w-5 h-5 text-red-500 mt-0.5\" />\r\n          <div>\r\n            <p className=\"text-sm font-medium text-red-700 dark:text-red-400\">\r\n              Warning: This action cannot be undone\r\n            </p>\r\n            <p className=\"text-xs text-red-600/80 dark:text-red-400/80 mt-1\">\r\n              Deleting your account will permanently remove all your data, including your saved cards,\r\n              likes, reviews, and subscriptions. You will not be able to recover this information.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <motion.div\r\n        whileHover={{ scale: 1.02 }}\r\n        whileTap={{ scale: 0.98 }}\r\n        className=\"w-auto\"\r\n      >\r\n        <Button\r\n          variant=\"destructive\"\r\n          disabled={isDeleting}\r\n          onClick={() => setIsDialogOpen(true)}\r\n          className={`\r\n            relative overflow-hidden\r\n            bg-gradient-to-r from-red-500 to-red-600\r\n            hover:from-red-600 hover:to-red-700\r\n            text-white font-medium\r\n            shadow-lg hover:shadow-xl\r\n            transition-all duration-300\r\n            before:absolute before:inset-0\r\n            before:bg-gradient-to-r before:from-red-400 before:to-red-500\r\n            before:opacity-0 hover:before:opacity-20\r\n            before:transition-opacity before:duration-300\r\n            ${isDeleting ? 'cursor-not-allowed opacity-80' : ''}\r\n          `}\r\n          style={{\r\n            boxShadow: isDeleting\r\n              ? '0 4px 20px rgba(239, 68, 68, 0.3)'\r\n              : '0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)'\r\n          }}\r\n          type=\"button\"\r\n        >\r\n          <Trash2 className=\"h-4 w-4 mr-2\" />\r\n          Delete Account\r\n        </Button>\r\n      </motion.div>\r\n\r\n      <Dialog open={isDialogOpen} onOpenChange={(_open) => {\r\n        // Only allow closing the dialog if we're not in the deleting state\r\n        if (!isDeleting && !isVerifying && !isSendingOTP) {\r\n          resetDialogState();\r\n        }\r\n      }}>\r\n        <DialogContent className=\"sm:max-w-md\">\r\n          <DialogHeader className=\"text-center pb-2\">\r\n            <div className=\"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20\">\r\n              <motion.div\r\n                initial={{ scale: 0 }}\r\n                animate={{ scale: 1 }}\r\n                transition={{ delay: 0.1, type: \"spring\", stiffness: 200 }}\r\n              >\r\n                <Trash2 className=\"h-8 w-8 text-red-500\" />\r\n              </motion.div>\r\n            </div>\r\n            <DialogTitle className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n              {verificationStep === 'choose-method' ? 'Verify Your Identity' :\r\n               verificationStep === 'email-otp' ? 'Email Verification' :\r\n               verificationStep === 'password' ? 'Password Verification' :\r\n               'Delete your account?'}\r\n            </DialogTitle>\r\n            <DialogDescription className=\"text-gray-500 dark:text-gray-400 mt-2\">\r\n              {verificationStep === 'choose-method' ? 'Choose how you want to verify your identity before deleting your account.' :\r\n               verificationStep === 'email-otp' ? 'We\\'ve sent a verification code to your email address.' :\r\n               verificationStep === 'password' ? 'Please enter your current password to verify your identity.' :\r\n               'This action cannot be undone. All your data will be permanently removed.'}\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Loading state while checking verification options */}\r\n            {isCheckingOptions && (\r\n              <div className=\"flex flex-col items-center justify-center py-8 space-y-4\">\r\n                <Loader2 className=\"h-8 w-8 animate-spin text-neutral-500\" />\r\n                <p className=\"text-sm text-neutral-600 dark:text-neutral-400\">\r\n                  Checking verification options...\r\n                </p>\r\n              </div>\r\n            )}\r\n\r\n            {/* Choose verification method */}\r\n            {!isCheckingOptions && verificationStep === 'choose-method' && (\r\n              <div className=\"space-y-4\">\r\n                <p className=\"text-sm text-neutral-600 dark:text-neutral-400 mb-4\">\r\n                  For security, please verify your identity before proceeding:\r\n                </p>\r\n\r\n                <div className=\"space-y-3\">\r\n                  <Button\r\n                    onClick={() => handleMethodSelection('email')}\r\n                    variant=\"outline\"\r\n                    className=\"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800\"\r\n                  >\r\n                    <Mail className=\"h-5 w-5 text-blue-500\" />\r\n                    <div className=\"text-left\">\r\n                      <div className=\"font-medium\">Email Verification</div>\r\n                      <div className=\"text-xs text-neutral-500\">Send OTP to your email</div>\r\n                    </div>\r\n                  </Button>\r\n\r\n                  <Button\r\n                    onClick={() => handleMethodSelection('password')}\r\n                    variant=\"outline\"\r\n                    className=\"w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800\"\r\n                  >\r\n                    <Smartphone className=\"h-5 w-5 text-green-500\" />\r\n                    <div className=\"text-left\">\r\n                      <div className=\"font-medium\">Password Verification</div>\r\n                      <div className=\"text-xs text-neutral-500\">Enter your current password</div>\r\n                    </div>\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Email OTP verification */}\r\n            {!isCheckingOptions && verificationStep === 'email-otp' && (\r\n              <div className=\"space-y-4\">\r\n                <div className=\"text-center\">\r\n                  <Button\r\n                    onClick={handleSendOTP}\r\n                    disabled={isSendingOTP}\r\n                    variant=\"outline\"\r\n                    className=\"mb-4\"\r\n                  >\r\n                    {isSendingOTP ? (\r\n                      <>\r\n                        <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                        Sending...\r\n                      </>\r\n                    ) : (\r\n                      'Send Verification Code'\r\n                    )}\r\n                  </Button>\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-neutral-700 dark:text-neutral-300\">\r\n                    Enter 6-digit verification code:\r\n                  </label>\r\n                  <div className=\"flex justify-center\">\r\n                    <InputOTP\r\n                      maxLength={6}\r\n                      value={otp}\r\n                      onChange={setOtp}\r\n                      className=\"gap-2\"\r\n                    >\r\n                      <InputOTPGroup>\r\n                        <InputOTPSlot index={0} className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500\" />\r\n                        <InputOTPSlot index={1} className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500\" />\r\n                        <InputOTPSlot index={2} className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500\" />\r\n                        <InputOTPSlot index={3} className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500\" />\r\n                        <InputOTPSlot index={4} className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500\" />\r\n                        <InputOTPSlot index={5} className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500\" />\r\n                      </InputOTPGroup>\r\n                    </InputOTP>\r\n                  </div>\r\n                </div>\r\n\r\n                <Button\r\n                  onClick={handleVerifyOTP}\r\n                  disabled={otp.length !== 6 || isVerifying}\r\n                  className=\"w-full\"\r\n                >\r\n                  {isVerifying ? (\r\n                    <>\r\n                      <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                      Verifying...\r\n                    </>\r\n                  ) : (\r\n                    'Verify Code'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            )}\r\n\r\n            {/* Password verification */}\r\n            {!isCheckingOptions && verificationStep === 'password' && (\r\n              <div className=\"space-y-4\">\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-neutral-700 dark:text-neutral-300\">\r\n                    Enter your current password:\r\n                  </label>\r\n                  <Input\r\n                    type=\"password\"\r\n                    value={password}\r\n                    onChange={(e) => setPassword(e.target.value)}\r\n                    placeholder=\"Current password\"\r\n                    className=\"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700\"\r\n                  />\r\n                </div>\r\n\r\n                <Button\r\n                  onClick={handleVerifyPassword}\r\n                  disabled={!password.trim() || isVerifying}\r\n                  className=\"w-full\"\r\n                >\r\n                  {isVerifying ? (\r\n                    <>\r\n                      <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                      Verifying...\r\n                    </>\r\n                  ) : (\r\n                    'Verify Password'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            )}\r\n\r\n            {/* Final confirmation */}\r\n            {!isCheckingOptions && verificationStep === 'final-confirm' && (\r\n              <>\r\n                <div className=\"bg-red-50 dark:bg-red-950/20 rounded-lg p-4 border border-red-100 dark:border-red-800/30\">\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <AlertTriangle className=\"h-5 w-5 text-red-500 mt-0.5 flex-shrink-0\" />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-red-700 dark:text-red-400\">\r\n                        This will permanently delete:\r\n                      </p>\r\n                      <ul className=\"text-xs text-red-600/80 dark:text-red-400/80 mt-1 space-y-0.5\">\r\n                        <li>• Your saved business cards</li>\r\n                        <li>• Your likes and subscriptions</li>\r\n                        <li>• Your reviews and ratings</li>\r\n                        <li>• Your account information</li>\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2\">\r\n                    Type <span className=\"font-bold text-red-500 dark:text-red-400\">DELETE</span> to confirm:\r\n                  </p>\r\n                  <Input\r\n                    value={deleteConfirmText}\r\n                    onChange={(e) => setDeleteConfirmText(e.target.value)}\r\n                    placeholder=\"DELETE\"\r\n                    className=\"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 focus-visible:ring-red-500/30\"\r\n                    disabled={isDeleting}\r\n                  />\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          <DialogFooter className=\"flex flex-col-reverse sm:flex-row gap-3 pt-4\">\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              onClick={() => {\r\n                resetDialogState();\r\n              }}\r\n              disabled={isDeleting || isVerifying || isSendingOTP || isCheckingOptions}\r\n              className=\"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200\"\r\n            >\r\n              <XCircle className=\"mr-2 h-4 w-4\" />\r\n              Cancel\r\n            </Button>\r\n\r\n            {/* Only show delete button on final confirmation step */}\r\n            {!isCheckingOptions && verificationStep === 'final-confirm' && (\r\n              <motion.div\r\n                whileHover={{ scale: 1.02 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                className=\"flex-1\"\r\n              >\r\n                <Button\r\n                  type=\"button\"\r\n                  onClick={handleDeleteAccount}\r\n                  disabled={deleteConfirmText !== \"DELETE\" || isDeleting}\r\n                  className={`\r\n                    w-full relative overflow-hidden\r\n                    bg-gradient-to-r from-red-500 to-red-600\r\n                    hover:from-red-600 hover:to-red-700\r\n                    text-white font-medium\r\n                    shadow-lg hover:shadow-xl\r\n                    transition-all duration-300\r\n                    before:absolute before:inset-0\r\n                    before:bg-gradient-to-r before:from-red-400 before:to-red-500\r\n                    before:opacity-0 hover:before:opacity-20\r\n                    before:transition-opacity before:duration-300\r\n                    ${deleteConfirmText !== \"DELETE\" || isDeleting ? 'cursor-not-allowed opacity-80' : ''}\r\n                  `}\r\n                  style={{\r\n                    boxShadow: (deleteConfirmText !== \"DELETE\" || isDeleting)\r\n                      ? '0 4px 20px rgba(239, 68, 68, 0.3)'\r\n                      : '0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)'\r\n                  }}\r\n                >\r\n                  <AnimatePresence mode=\"wait\">\r\n                    {isDeleting ? (\r\n                      <motion.div\r\n                        key=\"deleting\"\r\n                        initial={{ opacity: 0, x: -10 }}\r\n                        animate={{ opacity: 1, x: 0 }}\r\n                        exit={{ opacity: 0, x: 10 }}\r\n                        className=\"flex items-center justify-center\"\r\n                      >\r\n                        <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                        Deleting...\r\n                      </motion.div>\r\n                    ) : (\r\n                      <motion.div\r\n                        key=\"delete\"\r\n                        initial={{ opacity: 0, x: -10 }}\r\n                        animate={{ opacity: 1, x: 0 }}\r\n                        exit={{ opacity: 0, x: 10 }}\r\n                        className=\"flex items-center justify-center\"\r\n                      >\r\n                        <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                        Delete Account\r\n                      </motion.div>\r\n                    )}\r\n                  </AnimatePresence>\r\n                </Button>\r\n              </motion.div>\r\n            )}\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AAQA;AACA;AAKA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AA5BA;;;;;;;;;;;;AAgCO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IACnF,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,qBAAqB,WAAW;YAClD;QACF;IACF,GAAG;QAAC;QAAc;KAAiB;IAEnC,MAAM,2BAA2B;QAC/B,qBAAqB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iMAAA,CAAA,wCAAqC,AAAD;YACzD,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,OAAO,QAAQ;gBAC3B,YAAY,OAAO,QAAQ;gBAE3B,iDAAiD;gBACjD,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,EAAE;oBACtC,oBAAoB;gBACtB,OAAO,IAAI,OAAO,QAAQ,EAAE;oBAC1B,kBAAkB;oBAClB,oBAAoB;gBACtB,OAAO,IAAI,OAAO,QAAQ,EAAE;oBAC1B,kBAAkB;oBAClB,oBAAoB;gBACtB,OAAO;oBACL,4DAA4D;oBAC5D,oBAAoB;gBACtB;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;YAChC;QACF,EAAE,OAAO,QAAQ;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,kBAAkB;QAClB,IAAI,WAAW,SAAS;YACtB,oBAAoB;QACtB,OAAO;YACL,oBAAoB;QACtB;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iMAAA,CAAA,uBAAoB,AAAD;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B,IAAI,OAAO,KAAK,EAAE;oBAChB,aAAa,OAAO,KAAK;gBAC3B;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO;gBAC1B,IAAI,OAAO,oBAAoB,EAAE;oBAC/B,uCAAuC;oBACvC;gBACF;YACF;QACF,EAAE,OAAO,QAAQ;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,IAAI,MAAM,KAAK,GAAG;YACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iMAAA,CAAA,yBAAsB,AAAD,EAAE,WAAW;YACvD,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B,cAAc;gBACd,oBAAoB;YACtB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO;YAC5B;QACF,EAAE,OAAO,QAAQ;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iMAAA,CAAA,8BAA2B,AAAD,EAAE;YACjD,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B,cAAc;gBACd,oBAAoB;YACtB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO;YAC5B;QACF,EAAE,OAAO,QAAQ;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,gCAAgC;IAChC,MAAM,sBAAsB,CAAC;QAC3B,0DAA0D;QAC1D,EAAE,cAAc;QAEhB,IAAI,sBAAsB,UAAU;YAClC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,kDAAkD;QAClD,IAAI,CAAC,YAAY,QAAQ,KAAK,CAAC,YAAY;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,cAAc;QACd,gBAAgB;YACd,IAAI;gBACF,yCAAyC;gBACzC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,8DAA8D;gBAC9D,IAAI;oBACF,MAAM,UAAU,MAAM,CAAA,GAAA,iMAAA,CAAA,wBAAqB,AAAD;oBAE1C,uDAAuD;oBACvD,4EAA4E;oBAC5E;oBACA,OAAO,IAAI,CAAC;gBACd,EAAE,OAAO,OAAgB;oBACvB,oGAAoG;oBACpG,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,wCAAwC;wBAC3F,oFAAoF;wBACpF,kCAAkC;wBAClC;wBACA,OAAO,IAAI,CAAC;oBACd,OAAO;wBACL,uBAAuB;wBACvB,QAAQ,KAAK,CAAC,8BAA8B;wBAC5C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,cAAc;oBAChB;gBACF;YACF,EAAE,OAAO,YAAY;gBACnB,2CAA2C;gBAC3C,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,cAAc;YAChB;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,qBAAqB;QACrB,gBAAgB;QAChB,oBAAoB;QACpB,kBAAkB;QAClB,OAAO;QACP,YAAY;QACZ,cAAc;QACd,aAAa;QACb,qBAAqB;IACvB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;QACxC,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,8OAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;0BAMvE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAoD;;;;;;;;;;;;;;;;;;;;;;;0BAQvE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;gBACxB,WAAU;0BAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,UAAU;oBACV,SAAS,IAAM,gBAAgB;oBAC/B,WAAW,CAAC;;;;;;;;;;;YAWV,EAAE,aAAa,kCAAkC,GAAG;UACtD,CAAC;oBACD,OAAO;wBACL,WAAW,aACP,sCACA;oBACN;oBACA,MAAK;;sCAEL,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAKvC,8OAAC,2HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAc,cAAc,CAAC;oBACzC,mEAAmE;oBACnE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,cAAc;wBAChD;oBACF;gBACF;0BACE,cAAA,8OAAC,2HAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,2HAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS;4CAAE,OAAO;wCAAE;wCACpB,YAAY;4CAAE,OAAO;4CAAK,MAAM;4CAAU,WAAW;wCAAI;kDAEzD,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGtB,8OAAC,2HAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,qBAAqB,kBAAkB,yBACvC,qBAAqB,cAAc,uBACnC,qBAAqB,aAAa,0BAClC;;;;;;8CAEH,8OAAC,2HAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAC1B,qBAAqB,kBAAkB,8EACvC,qBAAqB,cAAc,2DACnC,qBAAqB,aAAa,gEAClC;;;;;;;;;;;;sCAIL,8OAAC;4BAAI,WAAU;;gCAEZ,mCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAE,WAAU;sDAAiD;;;;;;;;;;;;gCAOjE,CAAC,qBAAqB,qBAAqB,iCAC1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAsD;;;;;;sDAInE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAS,IAAM,sBAAsB;oDACrC,SAAQ;oDACR,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,8OAAC;oEAAI,WAAU;8EAA2B;;;;;;;;;;;;;;;;;;8DAI9C,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAS,IAAM,sBAAsB;oDACrC,SAAQ;oDACR,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,8OAAC;oEAAI,WAAU;8EAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQnD,CAAC,qBAAqB,qBAAqB,6BAC1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU;gDACV,SAAQ;gDACR,WAAU;0DAET,6BACC;;sEACE,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA8B;;mEAInD;;;;;;;;;;;sDAKN,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA6D;;;;;;8DAG9E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,WAAQ;wDACP,WAAW;wDACX,OAAO;wDACP,UAAU;wDACV,WAAU;kEAEV,cAAA,8OAAC,iIAAA,CAAA,gBAAa;;8EACZ,8OAAC,iIAAA,CAAA,eAAY;oEAAC,OAAO;oEAAG,WAAU;;;;;;8EAClC,8OAAC,iIAAA,CAAA,eAAY;oEAAC,OAAO;oEAAG,WAAU;;;;;;8EAClC,8OAAC,iIAAA,CAAA,eAAY;oEAAC,OAAO;oEAAG,WAAU;;;;;;8EAClC,8OAAC,iIAAA,CAAA,eAAY;oEAAC,OAAO;oEAAG,WAAU;;;;;;8EAClC,8OAAC,iIAAA,CAAA,eAAY;oEAAC,OAAO;oEAAG,WAAU;;;;;;8EAClC,8OAAC,iIAAA,CAAA,eAAY;oEAAC,OAAO;oEAAG,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAM1C,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,IAAI,MAAM,KAAK,KAAK;4CAC9B,WAAU;sDAET,4BACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;+DAInD;;;;;;;;;;;;gCAOP,CAAC,qBAAqB,qBAAqB,4BAC1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA6D;;;;;;8DAG9E,8OAAC,0HAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,SAAS,IAAI,MAAM;4CAC9B,WAAU;sDAET,4BACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;+DAInD;;;;;;;;;;;;gCAOP,CAAC,qBAAqB,qBAAqB,iCAC1C;;sDACE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAqD;;;;;;0EAGlE,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAMZ,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;;wDAAkE;sEACxE,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;wDAAa;;;;;;;8DAE/E,8OAAC,0HAAA,CAAA,QAAK;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACpD,aAAY;oDACZ,WAAU;oDACV,UAAU;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC,2HAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,2HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;wCACP;oCACF;oCACA,UAAU,cAAc,eAAe,gBAAgB;oCACvD,WAAU;;sDAEV,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;gCAKrC,CAAC,qBAAqB,qBAAqB,iCAC1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS;wCACT,UAAU,sBAAsB,YAAY;wCAC5C,WAAW,CAAC;;;;;;;;;;;oBAWV,EAAE,sBAAsB,YAAY,aAAa,kCAAkC,GAAG;kBACxF,CAAC;wCACD,OAAO;4CACL,WAAW,AAAC,sBAAsB,YAAY,aAC1C,sCACA;wCACN;kDAEA,cAAA,8OAAC,yLAAA,CAAA,kBAAe;4CAAC,MAAK;sDACnB,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC1B,WAAU;;kEAEV,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;+CAN7C;;;;qEAUN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC1B,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;+CAN/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmB5B", "debugId": null}}, {"offset": {"line": 2067, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2164, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { getScalableUserPath } from '@/lib/utils/storage-paths';\r\nimport { z } from 'zod';\r\nimport { PasswordComplexitySchema } from '@/lib/schemas/authSchemas';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// --- Update Email ---\r\n\r\nconst UpdateEmailSchema = z.object({\r\n  newEmail: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type UpdateEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    newEmail?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerEmail(\r\n  _prevState: UpdateEmailFormState,\r\n  formData: FormData\r\n): Promise<UpdateEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdateEmailSchema.safeParse({\r\n    newEmail: formData.get('newEmail'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { newEmail } = validatedFields.data;\r\n\r\n  // Check if the new email is the same as the current one\r\n  if (newEmail === user.email) {\r\n    return { message: 'New email is the same as the current email.', success: false };\r\n  }\r\n\r\n  try {\r\n    // Update email in Supabase Auth\r\n    // This typically sends a confirmation email to both old and new addresses\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: newEmail,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes\r\n      let errorMessage = 'Failed to update email address.';\r\n\r\n      switch (updateError.code) {\r\n        case 'email_exists':\r\n          errorMessage = 'This email address is already registered with another account.';\r\n          break;\r\n        case 'invalid_email':\r\n          errorMessage = 'Please enter a valid email address.';\r\n          break;\r\n        case 'email_change_confirm_limit':\r\n          errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n          break;\r\n        case 'over_email_send_rate_limit':\r\n          errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n          break;\r\n        case 'email_not_confirmed':\r\n          errorMessage = 'Please confirm your current email address before changing it.';\r\n          break;\r\n        case 'same_email':\r\n          errorMessage = 'The new email address is the same as your current email.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update email address. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Revalidate relevant paths if needed, though email change might require user action (confirmation)\r\n    // revalidatePath('/dashboard/customer/settings');\r\n    // revalidatePath('/dashboard/customer/profile'); // Email is shown here\r\n\r\n    return {\r\n      message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Link Email ---\r\n\r\nconst LinkEmailSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type LinkEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function linkCustomerEmail(\r\n  _prevState: LinkEmailFormState,\r\n  formData: FormData\r\n): Promise<LinkEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = LinkEmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user already has an email (email update) or not (email linking)\r\n    const isEmailUpdate = !!user.email;\r\n\r\n    if (isEmailUpdate) {\r\n      // User already has email - use email change flow\r\n      const { error: authUpdateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (authUpdateError) {\r\n        // Handle specific Supabase auth error codes\r\n        let errorMessage = 'Failed to update email address.';\r\n\r\n        switch (authUpdateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n            break;\r\n          case 'email_not_confirmed':\r\n            errorMessage = 'Please confirm your current email address before changing it.';\r\n            break;\r\n          case 'same_email':\r\n            errorMessage = 'The new email address is the same as your current email.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to update email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n        success: true,\r\n      };\r\n    } else {\r\n      // User doesn't have email - directly link the email without OTP verification\r\n      // Supabase will automatically handle duplicate validation\r\n      const { error: updateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (updateError) {\r\n        let errorMessage = 'Failed to link email address.';\r\n\r\n        switch (updateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to link email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Email address linked successfully!',\r\n        success: true,\r\n      };\r\n    }\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while linking email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Verify Email OTP ---\r\n\r\nconst VerifyEmailOTPSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),\r\n});\r\n\r\nexport type VerifyEmailOTPFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n    otp?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function verifyEmailOTP(\r\n  _prevState: VerifyEmailOTPFormState,\r\n  formData: FormData\r\n): Promise<VerifyEmailOTPFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = VerifyEmailOTPSchema.safeParse({\r\n    email: formData.get('email'),\r\n    otp: formData.get('otp'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email, otp } = validatedFields.data;\r\n\r\n  try {\r\n    // Verify the OTP\r\n    const { error: verifyError } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (verifyError) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (verifyError.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // If OTP verification successful, update the user's email\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (updateError) {\r\n      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    return {\r\n      message: 'Email address linked successfully!',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while verifying code.', success: false };\r\n  }\r\n}\r\n\r\n// --- Update Password ---\r\n\r\nconst UpdatePasswordSchema = z.object({\r\n  currentPassword: z.string().min(1, 'Current password is required.'),\r\n  newPassword: PasswordComplexitySchema,\r\n});\r\n\r\nexport type UpdatePasswordFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    currentPassword?: string[];\r\n    newPassword?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerPassword(\r\n  _prevState: UpdatePasswordFormState,\r\n  formData: FormData\r\n): Promise<UpdatePasswordFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) { // Need email for re-authentication check\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdatePasswordSchema.safeParse({\r\n    currentPassword: formData.get('currentPassword'),\r\n    newPassword: formData.get('newPassword'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { currentPassword, newPassword } = validatedFields.data;\r\n\r\n  // IMPORTANT: Verify the current password first before attempting update.\r\n  // Supabase doesn't directly expose a \"verify password\" endpoint.\r\n  // The recommended way is to try signing in with the current password.\r\n  // This is a crucial security step.\r\n  const { error: signInError } = await supabase.auth.signInWithPassword({\r\n    email: user.email,\r\n    password: currentPassword,\r\n  });\r\n\r\n  if (signInError) {\r\n     // Handle specific Supabase auth error codes for sign-in\r\n     let errorMessage = 'Failed to verify current password.';\r\n     const fieldErrors: { currentPassword?: string[] } = {};\r\n\r\n     switch (signInError.code) {\r\n       case 'invalid_credentials':\r\n       case 'email_not_confirmed':\r\n         errorMessage = 'Incorrect current password.';\r\n         fieldErrors.currentPassword = ['Incorrect current password.'];\r\n         break;\r\n       case 'too_many_requests':\r\n         errorMessage = 'Too many failed attempts. Please wait before trying again.';\r\n         break;\r\n       case 'user_not_found':\r\n         errorMessage = 'Account not found. Please contact support.';\r\n         break;\r\n       default:\r\n         errorMessage = 'Unable to verify current password. Please try again.';\r\n     }\r\n\r\n     return {\r\n       message: errorMessage,\r\n       errors: fieldErrors,\r\n       success: false\r\n     };\r\n  }\r\n\r\n\r\n  // If sign-in was successful (current password is correct), proceed to update\r\n  try {\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      password: newPassword,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes for password update\r\n      let errorMessage = 'Failed to update password.';\r\n\r\n      switch (updateError.code) {\r\n        case 'weak_password':\r\n          errorMessage = 'Password is too weak. Please choose a stronger password.';\r\n          break;\r\n        case 'same_password':\r\n          errorMessage = 'New password must be different from your current password.';\r\n          break;\r\n        case 'password_too_short':\r\n          errorMessage = 'Password must be at least 6 characters long.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many password change requests. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update password. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Password updated successfully\r\n    return { message: 'Password updated successfully!', success: true };\r\n\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating password.', success: false };\r\n  }\r\n}\r\n\r\n\r\n// --- Enhanced Delete Account Security Actions ---\r\n\r\n// Check user's email and phone availability for delete account verification\r\nexport async function checkDeleteAccountVerificationOptions(): Promise<{\r\n  success: boolean;\r\n  hasEmail: boolean;\r\n  hasPhone: boolean;\r\n  message?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      hasEmail: false,\r\n      hasPhone: false,\r\n      message: \"Authentication required.\"\r\n    };\r\n  }\r\n\r\n  const hasEmail = !!(user.email && user.email.trim() !== '');\r\n  const hasPhone = !!(user.phone && user.phone.trim() !== '');\r\n\r\n  return {\r\n    success: true,\r\n    hasEmail,\r\n    hasPhone,\r\n  };\r\n}\r\n\r\n// Send OTP to email for delete account verification\r\nexport async function sendDeleteAccountOTP(): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n  email?: string;\r\n  isConfigurationError?: boolean;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no email found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: user.email,\r\n      options: {\r\n        shouldCreateUser: false, // Don't create new user\r\n      },\r\n    });\r\n\r\n    if (error) {\r\n      // Handle rate limit errors specifically\r\n      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {\r\n        return {\r\n          success: false,\r\n          message: \"Email rate limit exceeded. Please try again later.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: error.message || \"Failed to send verification code.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Verification code sent to your email address.\",\r\n      email: user.email,\r\n    };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred while sending verification code.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for delete account\r\nexport async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (error) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (error.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { success: false, message: errorMessage };\r\n    }\r\n\r\n    return { success: true, message: \"Verification successful.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify password for delete account (for phone users)\r\nexport async function verifyDeleteAccountPassword(password: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.phone) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no phone found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Verify current password by attempting to sign in\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      phone: user.phone,\r\n      password: password,\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        message: \"Invalid password. Please try again.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, message: \"Password verified successfully.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during password verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// --- Delete Account ---\r\n\r\nexport type DeleteAccountFormState = {\r\n  message: string | null;\r\n  success: boolean;\r\n};\r\n\r\nexport async function deleteCustomerAccount(\r\n  // No prevState or formData needed for this action\r\n): Promise<DeleteAccountFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  // Check for and clean up any storage data using hash-based structure\r\n  try {\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n    const bucketName = \"customers\"; // Correct bucket name (plural)\r\n    const userStoragePath = getScalableUserPath(user.id);\r\n\r\n    // Recursive function to delete all files and folders\r\n    const deleteRecursively = async (path: string): Promise<void> => {\r\n      const { data: items, error: listError } = await adminSupabase.storage\r\n        .from(bucketName)\r\n        .list(path);\r\n\r\n      if (listError) {\r\n        console.error(`Error listing files in ${path}:`, listError);\r\n        return;\r\n      }\r\n\r\n      if (!items || items.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Separate files and folders\r\n      const files: string[] = [];\r\n      const folders: string[] = [];\r\n\r\n      for (const item of items) {\r\n        const fullPath = path ? `${path}/${item.name}` : item.name;\r\n\r\n        if (item.metadata === null) {\r\n          // This is a folder\r\n          folders.push(fullPath);\r\n        } else {\r\n          // This is a file\r\n          files.push(fullPath);\r\n        }\r\n      }\r\n\r\n      // Delete all files in the current directory\r\n      if (files.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(bucketName)\r\n          .remove(files);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(`Error deleting files in ${path}:`, deleteError);\r\n        } else {\r\n          console.log(`Successfully deleted ${files.length} files in ${path}`);\r\n        }\r\n      }\r\n\r\n      // Recursively delete folders\r\n      for (const folder of folders) {\r\n        await deleteRecursively(folder);\r\n      }\r\n    };\r\n\r\n    // Start the recursive deletion from the user's root folder\r\n    await deleteRecursively(userStoragePath);\r\n\r\n    console.log('Successfully cleaned up customer storage data');\r\n  } catch (storageError) {\r\n    // Log but continue with deletion\r\n    console.error('Error checking/cleaning customer storage:', storageError);\r\n  }\r\n\r\n  // Use the admin client to delete the user and profile\r\n  try {\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Delete from customer_profiles table (CASCADE will handle related data)\r\n    console.log('Deleting customer profile...');\r\n    const { error: deleteProfileError } = await supabaseAdmin\r\n      .from('customer_profiles')\r\n      .delete()\r\n      .eq('id', user.id);\r\n\r\n    if (deleteProfileError) {\r\n      console.error('Error deleting customer profile:', deleteProfileError);\r\n      return { message: `Failed to delete customer profile: ${deleteProfileError.message}`, success: false };\r\n    }\r\n\r\n    console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');\r\n\r\n    // Sign out the user locally first (while the session is still valid)\r\n    await supabase.auth.signOut();\r\n\r\n    // Then delete the user using the admin client\r\n    // Using hard delete (shouldSoftDelete=false) to completely remove the user\r\n    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);\r\n\r\n    if (deleteUserError) {\r\n      console.error('Error deleting user account:', deleteUserError);\r\n      return { message: `Failed to delete account: ${deleteUserError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate paths if needed\r\n    revalidatePath(\"/\", \"layout\"); // Revalidate root layout\r\n\r\n    return { message: 'Account deleted successfully.', success: true };\r\n\r\n  } catch (error) {\r\n     console.error('Unexpected error during account deletion:', error);\r\n     return { message: 'An unexpected error occurred during account deletion.', success: false };\r\n  }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAyHsB,oBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { getScalableUserPath } from '@/lib/utils/storage-paths';\r\nimport { z } from 'zod';\r\nimport { PasswordComplexitySchema } from '@/lib/schemas/authSchemas';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\n// --- Update Email ---\r\n\r\nconst UpdateEmailSchema = z.object({\r\n  newEmail: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type UpdateEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    newEmail?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerEmail(\r\n  _prevState: UpdateEmailFormState,\r\n  formData: FormData\r\n): Promise<UpdateEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdateEmailSchema.safeParse({\r\n    newEmail: formData.get('newEmail'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { newEmail } = validatedFields.data;\r\n\r\n  // Check if the new email is the same as the current one\r\n  if (newEmail === user.email) {\r\n    return { message: 'New email is the same as the current email.', success: false };\r\n  }\r\n\r\n  try {\r\n    // Update email in Supabase Auth\r\n    // This typically sends a confirmation email to both old and new addresses\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: newEmail,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes\r\n      let errorMessage = 'Failed to update email address.';\r\n\r\n      switch (updateError.code) {\r\n        case 'email_exists':\r\n          errorMessage = 'This email address is already registered with another account.';\r\n          break;\r\n        case 'invalid_email':\r\n          errorMessage = 'Please enter a valid email address.';\r\n          break;\r\n        case 'email_change_confirm_limit':\r\n          errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n          break;\r\n        case 'over_email_send_rate_limit':\r\n          errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n          break;\r\n        case 'email_not_confirmed':\r\n          errorMessage = 'Please confirm your current email address before changing it.';\r\n          break;\r\n        case 'same_email':\r\n          errorMessage = 'The new email address is the same as your current email.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update email address. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Revalidate relevant paths if needed, though email change might require user action (confirmation)\r\n    // revalidatePath('/dashboard/customer/settings');\r\n    // revalidatePath('/dashboard/customer/profile'); // Email is shown here\r\n\r\n    return {\r\n      message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Link Email ---\r\n\r\nconst LinkEmailSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n});\r\n\r\nexport type LinkEmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function linkCustomerEmail(\r\n  _prevState: LinkEmailFormState,\r\n  formData: FormData\r\n): Promise<LinkEmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = LinkEmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user already has an email (email update) or not (email linking)\r\n    const isEmailUpdate = !!user.email;\r\n\r\n    if (isEmailUpdate) {\r\n      // User already has email - use email change flow\r\n      const { error: authUpdateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (authUpdateError) {\r\n        // Handle specific Supabase auth error codes\r\n        let errorMessage = 'Failed to update email address.';\r\n\r\n        switch (authUpdateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email change requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';\r\n            break;\r\n          case 'email_not_confirmed':\r\n            errorMessage = 'Please confirm your current email address before changing it.';\r\n            break;\r\n          case 'same_email':\r\n            errorMessage = 'The new email address is the same as your current email.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to update email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',\r\n        success: true,\r\n      };\r\n    } else {\r\n      // User doesn't have email - directly link the email without OTP verification\r\n      // Supabase will automatically handle duplicate validation\r\n      const { error: updateError } = await supabase.auth.updateUser({\r\n        email: email,\r\n      });\r\n\r\n      if (updateError) {\r\n        let errorMessage = 'Failed to link email address.';\r\n\r\n        switch (updateError.code) {\r\n          case 'email_exists':\r\n            errorMessage = 'This email address is already registered with another account.';\r\n            break;\r\n          case 'invalid_email':\r\n            errorMessage = 'Please enter a valid email address.';\r\n            break;\r\n          case 'email_change_confirm_limit':\r\n            errorMessage = 'Too many email requests. Please wait before trying again.';\r\n            break;\r\n          case 'over_email_send_rate_limit':\r\n            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';\r\n            break;\r\n          default:\r\n            errorMessage = 'Unable to link email address. Please try again later.';\r\n        }\r\n\r\n        return { message: errorMessage, success: false };\r\n      }\r\n\r\n      // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n      return {\r\n        message: 'Email address linked successfully!',\r\n        success: true,\r\n      };\r\n    }\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while linking email.', success: false };\r\n  }\r\n}\r\n\r\n// --- Verify Email OTP ---\r\n\r\nconst VerifyEmailOTPSchema = z.object({\r\n  email: z.string().email('Invalid email address.'),\r\n  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),\r\n});\r\n\r\nexport type VerifyEmailOTPFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n    otp?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function verifyEmailOTP(\r\n  _prevState: VerifyEmailOTPFormState,\r\n  formData: FormData\r\n): Promise<VerifyEmailOTPFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = VerifyEmailOTPSchema.safeParse({\r\n    email: formData.get('email'),\r\n    otp: formData.get('otp'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email, otp } = validatedFields.data;\r\n\r\n  try {\r\n    // Verify the OTP\r\n    const { error: verifyError } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (verifyError) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (verifyError.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // If OTP verification successful, update the user's email\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (updateError) {\r\n      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    return {\r\n      message: 'Email address linked successfully!',\r\n      success: true,\r\n    };\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while verifying code.', success: false };\r\n  }\r\n}\r\n\r\n// --- Update Password ---\r\n\r\nconst UpdatePasswordSchema = z.object({\r\n  currentPassword: z.string().min(1, 'Current password is required.'),\r\n  newPassword: PasswordComplexitySchema,\r\n});\r\n\r\nexport type UpdatePasswordFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    currentPassword?: string[];\r\n    newPassword?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerPassword(\r\n  _prevState: UpdatePasswordFormState,\r\n  formData: FormData\r\n): Promise<UpdatePasswordFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) { // Need email for re-authentication check\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UpdatePasswordSchema.safeParse({\r\n    currentPassword: formData.get('currentPassword'),\r\n    newPassword: formData.get('newPassword'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { currentPassword, newPassword } = validatedFields.data;\r\n\r\n  // IMPORTANT: Verify the current password first before attempting update.\r\n  // Supabase doesn't directly expose a \"verify password\" endpoint.\r\n  // The recommended way is to try signing in with the current password.\r\n  // This is a crucial security step.\r\n  const { error: signInError } = await supabase.auth.signInWithPassword({\r\n    email: user.email,\r\n    password: currentPassword,\r\n  });\r\n\r\n  if (signInError) {\r\n     // Handle specific Supabase auth error codes for sign-in\r\n     let errorMessage = 'Failed to verify current password.';\r\n     const fieldErrors: { currentPassword?: string[] } = {};\r\n\r\n     switch (signInError.code) {\r\n       case 'invalid_credentials':\r\n       case 'email_not_confirmed':\r\n         errorMessage = 'Incorrect current password.';\r\n         fieldErrors.currentPassword = ['Incorrect current password.'];\r\n         break;\r\n       case 'too_many_requests':\r\n         errorMessage = 'Too many failed attempts. Please wait before trying again.';\r\n         break;\r\n       case 'user_not_found':\r\n         errorMessage = 'Account not found. Please contact support.';\r\n         break;\r\n       default:\r\n         errorMessage = 'Unable to verify current password. Please try again.';\r\n     }\r\n\r\n     return {\r\n       message: errorMessage,\r\n       errors: fieldErrors,\r\n       success: false\r\n     };\r\n  }\r\n\r\n\r\n  // If sign-in was successful (current password is correct), proceed to update\r\n  try {\r\n    const { error: updateError } = await supabase.auth.updateUser({\r\n      password: newPassword,\r\n    });\r\n\r\n    if (updateError) {\r\n      // Handle specific Supabase auth error codes for password update\r\n      let errorMessage = 'Failed to update password.';\r\n\r\n      switch (updateError.code) {\r\n        case 'weak_password':\r\n          errorMessage = 'Password is too weak. Please choose a stronger password.';\r\n          break;\r\n        case 'same_password':\r\n          errorMessage = 'New password must be different from your current password.';\r\n          break;\r\n        case 'password_too_short':\r\n          errorMessage = 'Password must be at least 6 characters long.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many password change requests. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to update password. Please try again later.';\r\n      }\r\n\r\n      return { message: errorMessage, success: false };\r\n    }\r\n\r\n    // Password updated successfully\r\n    return { message: 'Password updated successfully!', success: true };\r\n\r\n  } catch (_error) {\r\n    return { message: 'An unexpected error occurred while updating password.', success: false };\r\n  }\r\n}\r\n\r\n\r\n// --- Enhanced Delete Account Security Actions ---\r\n\r\n// Check user's email and phone availability for delete account verification\r\nexport async function checkDeleteAccountVerificationOptions(): Promise<{\r\n  success: boolean;\r\n  hasEmail: boolean;\r\n  hasPhone: boolean;\r\n  message?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      hasEmail: false,\r\n      hasPhone: false,\r\n      message: \"Authentication required.\"\r\n    };\r\n  }\r\n\r\n  const hasEmail = !!(user.email && user.email.trim() !== '');\r\n  const hasPhone = !!(user.phone && user.phone.trim() !== '');\r\n\r\n  return {\r\n    success: true,\r\n    hasEmail,\r\n    hasPhone,\r\n  };\r\n}\r\n\r\n// Send OTP to email for delete account verification\r\nexport async function sendDeleteAccountOTP(): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n  email?: string;\r\n  isConfigurationError?: boolean;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.email) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no email found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: user.email,\r\n      options: {\r\n        shouldCreateUser: false, // Don't create new user\r\n      },\r\n    });\r\n\r\n    if (error) {\r\n      // Handle rate limit errors specifically\r\n      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {\r\n        return {\r\n          success: false,\r\n          message: \"Email rate limit exceeded. Please try again later.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: error.message || \"Failed to send verification code.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Verification code sent to your email address.\",\r\n      email: user.email,\r\n    };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred while sending verification code.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for delete account\r\nexport async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (error) {\r\n      let errorMessage = 'Failed to verify code.';\r\n\r\n      switch (error.code) {\r\n        case 'invalid_otp':\r\n        case 'expired_otp':\r\n          errorMessage = 'Invalid or expired verification code. Please try again.';\r\n          break;\r\n        case 'too_many_requests':\r\n          errorMessage = 'Too many verification attempts. Please wait before trying again.';\r\n          break;\r\n        default:\r\n          errorMessage = 'Unable to verify code. Please try again.';\r\n      }\r\n\r\n      return { success: false, message: errorMessage };\r\n    }\r\n\r\n    return { success: true, message: \"Verification successful.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify password for delete account (for phone users)\r\nexport async function verifyDeleteAccountPassword(password: string): Promise<{\r\n  success: boolean;\r\n  message: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user || !user.phone) {\r\n    return {\r\n      success: false,\r\n      message: \"Authentication required or no phone found.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Verify current password by attempting to sign in\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      phone: user.phone,\r\n      password: password,\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        message: \"Invalid password. Please try again.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, message: \"Password verified successfully.\" };\r\n  } catch (_error) {\r\n    return {\r\n      success: false,\r\n      message: \"An unexpected error occurred during password verification.\",\r\n    };\r\n  }\r\n}\r\n\r\n// --- Delete Account ---\r\n\r\nexport type DeleteAccountFormState = {\r\n  message: string | null;\r\n  success: boolean;\r\n};\r\n\r\nexport async function deleteCustomerAccount(\r\n  // No prevState or formData needed for this action\r\n): Promise<DeleteAccountFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  // Check for and clean up any storage data using hash-based structure\r\n  try {\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n    const bucketName = \"customers\"; // Correct bucket name (plural)\r\n    const userStoragePath = getScalableUserPath(user.id);\r\n\r\n    // Recursive function to delete all files and folders\r\n    const deleteRecursively = async (path: string): Promise<void> => {\r\n      const { data: items, error: listError } = await adminSupabase.storage\r\n        .from(bucketName)\r\n        .list(path);\r\n\r\n      if (listError) {\r\n        console.error(`Error listing files in ${path}:`, listError);\r\n        return;\r\n      }\r\n\r\n      if (!items || items.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Separate files and folders\r\n      const files: string[] = [];\r\n      const folders: string[] = [];\r\n\r\n      for (const item of items) {\r\n        const fullPath = path ? `${path}/${item.name}` : item.name;\r\n\r\n        if (item.metadata === null) {\r\n          // This is a folder\r\n          folders.push(fullPath);\r\n        } else {\r\n          // This is a file\r\n          files.push(fullPath);\r\n        }\r\n      }\r\n\r\n      // Delete all files in the current directory\r\n      if (files.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(bucketName)\r\n          .remove(files);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(`Error deleting files in ${path}:`, deleteError);\r\n        } else {\r\n          console.log(`Successfully deleted ${files.length} files in ${path}`);\r\n        }\r\n      }\r\n\r\n      // Recursively delete folders\r\n      for (const folder of folders) {\r\n        await deleteRecursively(folder);\r\n      }\r\n    };\r\n\r\n    // Start the recursive deletion from the user's root folder\r\n    await deleteRecursively(userStoragePath);\r\n\r\n    console.log('Successfully cleaned up customer storage data');\r\n  } catch (storageError) {\r\n    // Log but continue with deletion\r\n    console.error('Error checking/cleaning customer storage:', storageError);\r\n  }\r\n\r\n  // Use the admin client to delete the user and profile\r\n  try {\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Delete from customer_profiles table (CASCADE will handle related data)\r\n    console.log('Deleting customer profile...');\r\n    const { error: deleteProfileError } = await supabaseAdmin\r\n      .from('customer_profiles')\r\n      .delete()\r\n      .eq('id', user.id);\r\n\r\n    if (deleteProfileError) {\r\n      console.error('Error deleting customer profile:', deleteProfileError);\r\n      return { message: `Failed to delete customer profile: ${deleteProfileError.message}`, success: false };\r\n    }\r\n\r\n    console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');\r\n\r\n    // Sign out the user locally first (while the session is still valid)\r\n    await supabase.auth.signOut();\r\n\r\n    // Then delete the user using the admin client\r\n    // Using hard delete (shouldSoftDelete=false) to completely remove the user\r\n    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);\r\n\r\n    if (deleteUserError) {\r\n      console.error('Error deleting user account:', deleteUserError);\r\n      return { message: `Failed to delete account: ${deleteUserError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate paths if needed\r\n    revalidatePath(\"/\", \"layout\"); // Revalidate root layout\r\n\r\n    return { message: 'Account deleted successfully.', success: true };\r\n\r\n  } catch (error) {\r\n     console.error('Unexpected error during account deletion:', error);\r\n     return { message: 'An unexpected error occurred during account deletion.', success: false };\r\n  }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8PsB,iBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2190, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/components/LinkEmailSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useTransition } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { z } from \"zod\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n  FormDescription,\r\n} from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Mail, Loader2, Link, AlertCircle } from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\nimport { linkCustomerEmail, verifyEmailOTP } from \"../actions\";\r\nimport {\r\n  InputOTP,\r\n  InputOTPGroup,\r\n  InputOTPSlot,\r\n} from \"@/components/ui/input-otp\";\r\n\r\n// Email form schema\r\nconst EmailSchema = z.object({\r\n  email: z.string().email({ message: \"Please enter a valid email address\" }),\r\n});\r\n\r\n// OTP form schema\r\nconst OTPSchema = z.object({\r\n  otp: z.string().min(6, { message: \"OTP must be 6 digits\" }).max(6, { message: \"OTP must be 6 digits\" }),\r\n});\r\n\r\ninterface LinkEmailSectionProps {\r\n  currentEmail?: string | null | undefined;\r\n  currentPhone?: string | null | undefined;\r\n  registrationType: 'google' | 'email' | 'phone';\r\n}\r\n\r\nexport default function LinkEmailSection({\r\n  currentEmail,\r\n  currentPhone: _currentPhone,\r\n  registrationType\r\n}: LinkEmailSectionProps) {\r\n  const [isPending, startTransition] = useTransition();\r\n  const [message, setMessage] = useState<string | null>(null);\r\n  const [step, setStep] = useState<'email' | 'otp'>('email');\r\n  const [emailForOTP, setEmailForOTP] = useState<string>('');\r\n\r\n  const emailForm = useForm<z.infer<typeof EmailSchema>>({\r\n    resolver: zodResolver(EmailSchema),\r\n    defaultValues: {\r\n      email: registrationType === 'email' ? (currentEmail || \"\") : \"\",\r\n    },\r\n  });\r\n\r\n  const otpForm = useForm<z.infer<typeof OTPSchema>>({\r\n    resolver: zodResolver(OTPSchema),\r\n    defaultValues: {\r\n      otp: \"\",\r\n    },\r\n  });\r\n\r\n  // Handle email linking\r\n  const onEmailSubmit = (data: z.infer<typeof EmailSchema>) => {\r\n    startTransition(async () => {\r\n      try {\r\n        // Create FormData object for the server action\r\n        const formData = new FormData();\r\n        formData.append('email', data.email);\r\n\r\n        // Call the server action\r\n        const result = await linkCustomerEmail(\r\n          { message: null, success: false }, // Initial state\r\n          formData\r\n        );\r\n\r\n        if (result.success) {\r\n          if (result.message === 'otp_sent') {\r\n            // Phone user with existing email - show OTP step\r\n            setEmailForOTP(data.email);\r\n            setStep('otp');\r\n            setMessage(\"We've sent a 6-digit verification code to your email address.\");\r\n            toast.success(\"Verification code sent!\");\r\n          } else if (result.message === 'Email address linked successfully!') {\r\n            // User with no email - direct linking successful\r\n            toast.success(\"Email linked successfully!\");\r\n            setMessage(\"Email address has been linked to your account.\");\r\n            emailForm.reset();\r\n            // Optionally refresh the page or update parent component state\r\n            window.location.reload();\r\n          } else {\r\n            // Email user - email change flow\r\n            toast.success(\"Verification email sent!\");\r\n            setMessage(result.message || \"Please check your email for the verification link.\");\r\n            emailForm.reset();\r\n          }\r\n        } else {\r\n          toast.error(result.message || \"Failed to send verification email\");\r\n          setMessage(result.message || \"Failed to send verification email\");\r\n        }\r\n      } catch (error) {\r\n        toast.error(\"An unexpected error occurred\");\r\n        console.error(error);\r\n      }\r\n    });\r\n  };\r\n\r\n  // Handle OTP verification\r\n  const onOTPSubmit = (data: z.infer<typeof OTPSchema>) => {\r\n    startTransition(async () => {\r\n      try {\r\n        // Create FormData object for the server action\r\n        const formData = new FormData();\r\n        formData.append('email', emailForOTP);\r\n        formData.append('otp', data.otp);\r\n\r\n        // Call the server action\r\n        const result = await verifyEmailOTP(\r\n          { message: null, success: false }, // Initial state\r\n          formData\r\n        );\r\n\r\n        if (result.success) {\r\n          toast.success(\"Email linked successfully!\");\r\n          setMessage(\"Email address has been linked to your account.\");\r\n          setStep('email');\r\n          setEmailForOTP('');\r\n          emailForm.reset();\r\n          otpForm.reset();\r\n        } else {\r\n          toast.error(result.message || \"Failed to verify code\");\r\n          setMessage(result.message || \"Failed to verify code\");\r\n        }\r\n      } catch (error) {\r\n        toast.error(\"An unexpected error occurred\");\r\n        console.error(error);\r\n      }\r\n    });\r\n  };\r\n\r\n  // Handle back to email step\r\n  const onBackToEmail = () => {\r\n    setStep('email');\r\n    setEmailForOTP('');\r\n    setMessage(null);\r\n    otpForm.reset();\r\n  };\r\n\r\n  // Determine the behavior based on registration type\r\n  const isGoogleUser = registrationType === 'google';\r\n  const isEmailUser = registrationType === 'email';\r\n  const _isPhoneUser = registrationType === 'phone';\r\n\r\n  return (\r\n    <Card className=\"border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black\">\r\n      <CardHeader className=\"pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <CardTitle className=\"flex items-center gap-2 text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n          <div className=\"p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400\">\r\n            <Mail className=\"w-4 h-4\" />\r\n          </div>\r\n          {isGoogleUser ? \"Email Address (Google)\" : isEmailUser ? \"Update Email Address\" : \"Link Email Address\"}\r\n        </CardTitle>\r\n        <p className=\"text-sm text-neutral-600 dark:text-neutral-400 mt-2\">\r\n          {isGoogleUser\r\n            ? \"Your email is linked to your Google account and cannot be changed here.\"\r\n            : isEmailUser\r\n            ? \"Update your email address. We'll send OTP verification to both old and new email addresses.\"\r\n            : \"Add an email address to your account for additional login options and notifications.\"\r\n          }\r\n        </p>\r\n      </CardHeader>\r\n      <CardContent className=\"pt-4\">\r\n        {message && (\r\n          <div className=\"mb-4 p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800\">\r\n            <div className=\"flex items-center gap-2 text-blue-700 dark:text-blue-300\">\r\n              <AlertCircle className=\"w-4 h-4\" />\r\n              <span className=\"text-sm\">{message}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {isGoogleUser ? (\r\n          // Google users - show email as read-only\r\n          <div className=\"space-y-4\">\r\n            <div>\r\n              <label className=\"text-sm font-medium text-neutral-700 dark:text-neutral-300\">\r\n                Email Address\r\n              </label>\r\n              <div className=\"mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md\">\r\n                <span className=\"text-sm text-neutral-600 dark:text-neutral-400\">{currentEmail}</span>\r\n              </div>\r\n              <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-1\">\r\n                This email is managed by your Google account.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : step === 'email' ? (\r\n          // Email step - show email form\r\n          <Form {...emailForm}>\r\n            <form onSubmit={emailForm.handleSubmit(onEmailSubmit)}>\r\n              <div className=\"space-y-4\">\r\n                <FormField\r\n                  control={emailForm.control}\r\n                  name=\"email\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel className=\"text-sm text-neutral-700 dark:text-neutral-300\">\r\n                        Email Address\r\n                      </FormLabel>\r\n                      <FormControl>\r\n                        <Input\r\n                          placeholder=\"<EMAIL>\"\r\n                          {...field}\r\n                          disabled={isPending}\r\n                          className=\"bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700\"\r\n                        />\r\n                      </FormControl>\r\n                      <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400\">\r\n                        {isEmailUser\r\n                          ? \"We'll send verification codes to both your current and new email addresses.\"\r\n                          : \"We'll send a verification code to this email address.\"\r\n                        }\r\n                      </FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <div className=\"flex justify-end\">\r\n                    <Button\r\n                      type=\"submit\"\r\n                      disabled={isPending || !emailForm.formState.isValid}\r\n                      className=\"bg-primary hover:bg-primary/90 text-primary-foreground\"\r\n                    >\r\n                      {isPending ? (\r\n                        <>\r\n                          <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\r\n                          Sending Verification...\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <Link className=\"w-4 h-4 mr-2\" />\r\n                          {isEmailUser ? \"Update Email\" : \"Link Email Address\"}\r\n                        </>\r\n                      )}\r\n                    </Button>\r\n                </div>\r\n              </div>\r\n            </form>\r\n          </Form>\r\n        ) : (\r\n          // OTP step - show OTP form\r\n          <Form {...otpForm}>\r\n            <form onSubmit={otpForm.handleSubmit(onOTPSubmit)}>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"text-center mb-4\">\r\n                  <p className=\"text-sm text-neutral-600 dark:text-neutral-400\">\r\n                    We&apos;ve sent a 6-digit code to\r\n                  </p>\r\n                  <p className=\"text-sm font-medium text-neutral-800 dark:text-neutral-200\">{emailForOTP}</p>\r\n                  <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-2\">\r\n                    Code expires in 24 hours\r\n                  </p>\r\n                </div>\r\n\r\n                <FormField\r\n                  control={otpForm.control}\r\n                  name=\"otp\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel className=\"text-sm text-neutral-700 dark:text-neutral-300 text-center block\">\r\n                        Enter Verification Code\r\n                      </FormLabel>\r\n                      <FormControl>\r\n                        <div className=\"flex justify-center\">\r\n                          <InputOTP\r\n                            maxLength={6}\r\n                            {...field}\r\n                            className=\"gap-2\"\r\n                          >\r\n                            <InputOTPGroup>\r\n                              <InputOTPSlot\r\n                                index={0}\r\n                                className=\"w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400\"\r\n                              />\r\n                              <InputOTPSlot\r\n                                index={1}\r\n                                className=\"w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400\"\r\n                              />\r\n                              <InputOTPSlot\r\n                                index={2}\r\n                                className=\"w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400\"\r\n                              />\r\n                              <InputOTPSlot\r\n                                index={3}\r\n                                className=\"w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400\"\r\n                              />\r\n                              <InputOTPSlot\r\n                                index={4}\r\n                                className=\"w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400\"\r\n                              />\r\n                              <InputOTPSlot\r\n                                index={5}\r\n                                className=\"w-12 h-12 text-lg font-semibold border-2 border-neutral-200 dark:border-neutral-700 focus:border-purple-500 dark:focus:border-purple-400\"\r\n                              />\r\n                            </InputOTPGroup>\r\n                          </InputOTP>\r\n                        </div>\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <div className=\"flex justify-between\">\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"ghost\"\r\n                    onClick={onBackToEmail}\r\n                    disabled={isPending}\r\n                    className=\"text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200\"\r\n                  >\r\n                    ← Back to Email\r\n                  </Button>\r\n\r\n                  <Button\r\n                      type=\"submit\"\r\n                      disabled={isPending || !otpForm.formState.isValid}\r\n                      className=\"bg-primary hover:bg-primary/90 text-primary-foreground\"\r\n                    >\r\n                      {isPending ? (\r\n                        <>\r\n                          <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\r\n                          Verifying...\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <Link className=\"w-4 h-4 mr-2\" />\r\n                          Verify & Link Email\r\n                        </>\r\n                      )}\r\n                    </Button>\r\n                </div>\r\n              </div>\r\n            </form>\r\n          </Form>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AArBA;;;;;;;;;;;;;;AA2BA,oBAAoB;AACpB,MAAM,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC1E;AAEA,kBAAkB;AAClB,MAAM,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,KAAK,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB;AACvG;AAQe,SAAS,iBAAiB,EACvC,YAAY,EACZ,cAAc,aAAa,EAC3B,gBAAgB,EACM;IACtB,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA+B;QACrD,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO,qBAAqB,UAAW,gBAAgB,KAAM;QAC/D;IACF;IAEA,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA6B;QACjD,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,KAAK;QACP;IACF;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;YACd,IAAI;gBACF,+CAA+C;gBAC/C,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;gBAEnC,yBAAyB;gBACzB,MAAM,SAAS,MAAM,CAAA,GAAA,iMAAA,CAAA,oBAAiB,AAAD,EACnC;oBAAE,SAAS;oBAAM,SAAS;gBAAM,GAChC;gBAGF,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI,OAAO,OAAO,KAAK,YAAY;wBACjC,iDAAiD;wBACjD,eAAe,KAAK,KAAK;wBACzB,QAAQ;wBACR,WAAW;wBACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO,IAAI,OAAO,OAAO,KAAK,sCAAsC;wBAClE,iDAAiD;wBACjD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd,WAAW;wBACX,UAAU,KAAK;wBACf,+DAA+D;wBAC/D,OAAO,QAAQ,CAAC,MAAM;oBACxB,OAAO;wBACL,iCAAiC;wBACjC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd,WAAW,OAAO,OAAO,IAAI;wBAC7B,UAAU,KAAK;oBACjB;gBACF,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;oBAC9B,WAAW,OAAO,OAAO,IAAI;gBAC/B;YACF,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,QAAQ,KAAK,CAAC;YAChB;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,cAAc,CAAC;QACnB,gBAAgB;YACd,IAAI;gBACF,+CAA+C;gBAC/C,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,SAAS;gBACzB,SAAS,MAAM,CAAC,OAAO,KAAK,GAAG;gBAE/B,yBAAyB;gBACzB,MAAM,SAAS,MAAM,CAAA,GAAA,iMAAA,CAAA,iBAAc,AAAD,EAChC;oBAAE,SAAS;oBAAM,SAAS;gBAAM,GAChC;gBAGF,IAAI,OAAO,OAAO,EAAE;oBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,WAAW;oBACX,QAAQ;oBACR,eAAe;oBACf,UAAU,KAAK;oBACf,QAAQ,KAAK;gBACf,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;oBAC9B,WAAW,OAAO,OAAO,IAAI;gBAC/B;YACF,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,QAAQ,KAAK,CAAC;YAChB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB;QACpB,QAAQ;QACR,eAAe;QACf,WAAW;QACX,QAAQ,KAAK;IACf;IAEA,oDAAoD;IACpD,MAAM,eAAe,qBAAqB;IAC1C,MAAM,cAAc,qBAAqB;IACzC,MAAM,eAAe,qBAAqB;IAE1C,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,yHAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;4BAEjB,eAAe,2BAA2B,cAAc,yBAAyB;;;;;;;kCAEpF,8OAAC;wBAAE,WAAU;kCACV,eACG,4EACA,cACA,gGACA;;;;;;;;;;;;0BAIR,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;;oBAKhC,eACC,yCAAyC;kCACzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA6D;;;;;;8CAG9E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAkD;;;;;;;;;;;8CAEpE,8OAAC;oCAAE,WAAU;8CAAsD;;;;;;;;;;;;;;;;+BAKrE,SAAS,UACX,+BAA+B;kCAC/B,8OAAC,yHAAA,CAAA,OAAI;wBAAE,GAAG,SAAS;kCACjB,cAAA,8OAAC;4BAAK,UAAU,UAAU,YAAY,CAAC;sCACrC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,yHAAA,CAAA,YAAS;wCACR,SAAS,UAAU,OAAO;wCAC1B,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kEACP,8OAAC,yHAAA,CAAA,YAAS;wDAAC,WAAU;kEAAiD;;;;;;kEAGtE,8OAAC,yHAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4DACJ,aAAY;4DACX,GAAG,KAAK;4DACT,UAAU;4DACV,WAAU;;;;;;;;;;;kEAGd,8OAAC,yHAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,cACG,gFACA;;;;;;kEAGN,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;kDACX,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,aAAa,CAAC,UAAU,SAAS,CAAC,OAAO;4CACnD,WAAU;sDAET,0BACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAShD,2BAA2B;kCAC3B,8OAAC,yHAAA,CAAA,OAAI;wBAAE,GAAG,OAAO;kCACf,cAAA,8OAAC;4BAAK,UAAU,QAAQ,YAAY,CAAC;sCACnC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAiD;;;;;;0DAG9D,8OAAC;gDAAE,WAAU;0DAA8D;;;;;;0DAC3E,8OAAC;gDAAE,WAAU;0DAAsD;;;;;;;;;;;;kDAKrE,8OAAC,yHAAA,CAAA,YAAS;wCACR,SAAS,QAAQ,OAAO;wCACxB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kEACP,8OAAC,yHAAA,CAAA,YAAS;wDAAC,WAAU;kEAAmE;;;;;;kEAGxF,8OAAC,yHAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,iIAAA,CAAA,WAAQ;gEACP,WAAW;gEACV,GAAG,KAAK;gEACT,WAAU;0EAEV,cAAA,8OAAC,iIAAA,CAAA,gBAAa;;sFACZ,8OAAC,iIAAA,CAAA,eAAY;4EACX,OAAO;4EACP,WAAU;;;;;;sFAEZ,8OAAC,iIAAA,CAAA,eAAY;4EACX,OAAO;4EACP,WAAU;;;;;;sFAEZ,8OAAC,iIAAA,CAAA,eAAY;4EACX,OAAO;4EACP,WAAU;;;;;;sFAEZ,8OAAC,iIAAA,CAAA,eAAY;4EACX,OAAO;4EACP,WAAU;;;;;;sFAEZ,8OAAC,iIAAA,CAAA,eAAY;4EACX,OAAO;4EACP,WAAU;;;;;;sFAEZ,8OAAC,iIAAA,CAAA,eAAY;4EACX,OAAO;4EACP,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAMpB,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;gDACT,UAAU;gDACV,WAAU;0DACX;;;;;;0DAID,8OAAC,2HAAA,CAAA,SAAM;gDACH,MAAK;gDACL,UAAU,aAAa,CAAC,QAAQ,SAAS,CAAC,OAAO;gDACjD,WAAU;0DAET,0BACC;;sEACE,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA8B;;iFAInD;;sEACE,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa3D", "debugId": null}}, {"offset": {"line": 2813, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/components/LinkPhoneSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from \"@/components/ui/card\";\r\nimport { Phone } from \"lucide-react\";\r\n\r\ninterface LinkPhoneSectionProps {\r\n  currentEmail?: string | null;\r\n  currentPhone?: string | null;\r\n  registrationType: 'google' | 'email' | 'phone';\r\n}\r\n\r\nexport default function LinkPhoneSection({\r\n  currentPhone,\r\n}: LinkPhoneSectionProps) {\r\n\r\n  return (\r\n    <Card className=\"border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black\">\r\n      <CardHeader className=\"pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <CardTitle className=\"flex items-center gap-2 text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n          <div className=\"p-2 rounded-lg bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400\">\r\n            <Phone className=\"w-4 h-4\" />\r\n          </div>\r\n          Phone Number\r\n        </CardTitle>\r\n        <p className=\"text-sm text-neutral-600 dark:text-neutral-400 mt-2\">\r\n          {current<PERSON><PERSON>\r\n            ? \"Your current phone number linked to this account.\"\r\n            : \"No phone number is currently linked to your account.\"\r\n          }\r\n        </p>\r\n      </CardHeader>\r\n      <CardContent className=\"pt-4\">\r\n        {currentPhone ? (\r\n          // Show current phone number (read-only)\r\n          <div className=\"space-y-4\">\r\n            <div>\r\n              <label className=\"text-sm font-medium text-neutral-700 dark:text-neutral-300\">\r\n                Current Phone Number\r\n              </label>\r\n              <div className=\"mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md\">\r\n                <span className=\"text-sm text-neutral-600 dark:text-neutral-400\">{currentPhone}</span>\r\n              </div>\r\n              <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-1\">\r\n                Phone number changes are not currently supported. Contact support if you need to update your number.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          // No phone number linked\r\n          <div className=\"text-center p-6 rounded-lg bg-neutral-50 dark:bg-neutral-900/50 border border-neutral-200 dark:border-neutral-700\">\r\n            <div className=\"p-3 rounded-full bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400 mx-auto mb-3 w-fit\">\r\n              <Phone className=\"w-6 h-6\" />\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-2\">\r\n              No Phone Number\r\n            </h3>\r\n            <p className=\"text-sm text-neutral-600 dark:text-neutral-400 max-w-sm mx-auto\">\r\n              No phone number is currently linked to your account. Phone number linking is not available at this time.\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWe,SAAS,iBAAiB,EACvC,YAAY,EACU;IAEtB,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,yHAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;4BACb;;;;;;;kCAGR,8OAAC;wBAAE,WAAU;kCACV,eACG,sDACA;;;;;;;;;;;;0BAIR,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB,eACC,wCAAwC;8BACxC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA6D;;;;;;0CAG9E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;0CAEpE,8OAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;;;;;2BAMvE,yBAAyB;8BACzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,8OAAC;4BAAE,WAAU;sCAAkE;;;;;;;;;;;;;;;;;;;;;;;AAQ3F", "debugId": null}}, {"offset": {"line": 2974, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/settings/components/SettingsPageClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Settings } from \"lucide-react\";\r\n\r\nimport PasswordUpdateSection from \"./PasswordUpdateSection\";\r\nimport { DeleteAccountSection } from \"../DeleteAccountSection\";\r\nimport LinkEmailSection from \"./LinkEmailSection\";\r\nimport LinkPhoneSection from \"./LinkPhoneSection\";\r\n\r\ninterface SettingsPageClientProps {\r\n  currentEmail: string | undefined;\r\n  currentPhone: string | null | undefined;\r\n  registrationType: 'google' | 'email' | 'phone';\r\n}\r\n\r\nexport default function SettingsPageClient({\r\n  currentEmail,\r\n  currentPhone,\r\n  registrationType,\r\n}: SettingsPageClientProps) {\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Header Section */}\r\n      <div className=\"flex items-center gap-4\">\r\n        <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n          <Settings className=\"w-6 h-6 text-foreground\" />\r\n        </div>\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-foreground\">\r\n            Account Settings\r\n          </h1>\r\n          <p className=\"text-muted-foreground mt-1\">\r\n            Manage your account security and preferences\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Settings Sections */}\r\n      <div className=\"space-y-8\">\r\n        {/* Link Email - Show for all users with different behaviors */}\r\n        <LinkEmailSection\r\n          currentEmail={currentEmail}\r\n          currentPhone={currentPhone}\r\n          registrationType={registrationType}\r\n        />\r\n\r\n        {/* Link Phone - Show for all users with different behaviors */}\r\n        <LinkPhoneSection\r\n          currentEmail={currentEmail}\r\n          currentPhone={currentPhone}\r\n          registrationType={registrationType}\r\n        />\r\n\r\n        {/* Password Management - Not for Google users */}\r\n        {registrationType !== 'google' && (\r\n          <PasswordUpdateSection\r\n            registrationType={registrationType}\r\n          />\r\n        )}\r\n\r\n        {/* Delete Account Section */}\r\n        <DeleteAccountSection />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AAPA;;;;;;;AAee,SAAS,mBAAmB,EACzC,YAAY,EACZ,YAAY,EACZ,gBAAgB,EACQ;IACxB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAGnD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;0BAO9C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4LAAA,CAAA,UAAgB;wBACf,cAAc;wBACd,cAAc;wBACd,kBAAkB;;;;;;kCAIpB,8OAAC,4LAAA,CAAA,UAAgB;wBACf,cAAc;wBACd,cAAc;wBACd,kBAAkB;;;;;;oBAInB,qBAAqB,0BACpB,8OAAC,iMAAA,CAAA,UAAqB;wBACpB,kBAAkB;;;;;;kCAKtB,8OAAC,kLAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}]}