'use client';

import { createClient } from '@/utils/supabase/client';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

export interface RealtimeSubscription {
  unsubscribe: () => void; 
  channel: RealtimeChannel;
}

export type RealtimeCallback<T extends { [key: string]: any } = any> = (_payload: RealtimePostgresChangesPayload<T>) => void;

export interface SubscriptionOptions {
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string;
  schema?: string;
}

class RealtimeService {
  private subscriptions = new Map<string, RealtimeChannel>();
  private supabase = createClient();

  /**
   * Subscribe to table changes with automatic cleanup and unique channel names
   */
  subscribeToTable<T extends { [key: string]: any } = any>(
    tableName: string,
    callback: RealtimeCallback<T>,
    options: SubscriptionOptions = {},
    customChannelId?: string
  ): RealtimeSubscription {
    const {
      event = '*',
      filter,
      schema = 'public'
    } = options;

    // Create unique channel name
    const channelId = customChannelId || `${tableName}-${event}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Clean up existing subscription with same ID if exists
    if (this.subscriptions.has(channelId)) {
      this.unsubscribe(channelId);
    }

    const channel = this.supabase
      .channel(channelId)
      .on(
        'postgres_changes' as any,
        {
          event,
          schema,
          table: tableName,
          ...(filter && { filter })
        },
        callback
      )
      .subscribe();

    this.subscriptions.set(channelId, channel);

    return {
      unsubscribe: () => this.unsubscribe(channelId),
      channel
    };
  }

  /**
   * Subscribe to business activities for a specific business
   */
  subscribeToBusinessActivities(
    businessProfileId: string,
    callback: RealtimeCallback,
    channelSuffix?: string
  ): RealtimeSubscription {
    const channelId = `business-activities-${businessProfileId}${channelSuffix ? `-${channelSuffix}` : ''}`;
    
    return this.subscribeToTable(
      'business_activities',
      callback,
      {
        event: 'INSERT',
        filter: `business_profile_id=eq.${businessProfileId}`
      },
      channelId
    );
  }

  /**
   * Subscribe to business profile changes
   */
  subscribeToBusinessProfile(
    businessProfileId: string,
    callback: RealtimeCallback,
    channelSuffix?: string
  ): RealtimeSubscription {
    const channelId = `business-profile-${businessProfileId}${channelSuffix ? `-${channelSuffix}` : ''}`;
    
    return this.subscribeToTable(
      'business_profiles',
      callback,
      {
        event: '*',
        filter: `id=eq.${businessProfileId}`
      },
      channelId
    );
  }

  /**
   * Subscribe to monthly visit metrics
   */
  subscribeToMonthlyMetrics(
    businessProfileId: string,
    callback: RealtimeCallback,
    channelSuffix?: string
  ): RealtimeSubscription {
    const channelId = `monthly-metrics-${businessProfileId}${channelSuffix ? `-${channelSuffix}` : ''}`;
    
    return this.subscribeToTable(
      'monthly_visit_metrics',
      callback,
      {
        event: '*',
        filter: `business_profile_id=eq.${businessProfileId}`
      },
      channelId
    );
  }

  /**
   * Subscribe to payment subscriptions
   */
  subscribeToPaymentSubscriptions(
    businessProfileId: string,
    callback: RealtimeCallback,
    channelSuffix?: string
  ): RealtimeSubscription {
    const channelId = `payment-subscriptions-${businessProfileId}${channelSuffix ? `-${channelSuffix}` : ''}`;
    
    return this.subscribeToTable(
      'payment_subscriptions',
      callback,
      {
        event: '*',
        filter: `business_profile_id=eq.${businessProfileId}`
      },
      channelId
    );
  }

  /**
   * Unsubscribe from a specific channel
   */
  private unsubscribe(channelId: string): void {
    const channel = this.subscriptions.get(channelId);
    if (channel) {
      this.supabase.removeChannel(channel);
      this.subscriptions.delete(channelId);
    }
  }

  /**
   * Unsubscribe from all channels
   */
  unsubscribeAll(): void {
    this.subscriptions.forEach((channel, channelId) => {
      this.supabase.removeChannel(channel);
    });
    this.subscriptions.clear();
  }

  /**
   * Get active subscription count (for debugging)
   */
  getActiveSubscriptionCount(): number {
    return this.subscriptions.size;
  }

  /**
   * Get active subscription channel IDs (for debugging)
   */
  getActiveChannelIds(): string[] {
    return Array.from(this.subscriptions.keys());
  }
}

// Export singleton instance
export const realtimeService = new RealtimeService();
