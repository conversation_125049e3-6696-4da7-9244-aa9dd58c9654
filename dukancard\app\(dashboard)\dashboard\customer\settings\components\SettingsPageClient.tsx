"use client";

import { Settings } from "lucide-react";

import PasswordUpdateSection from "./PasswordUpdateSection";
import { DeleteAccountSection } from "../DeleteAccountSection";
import LinkEmailSection from "./LinkEmailSection";
import LinkPhoneSection from "./LinkPhoneSection";

interface SettingsPageClientProps {
  currentEmail: string | undefined;
  currentPhone: string | null | undefined;
  registrationType: 'google' | 'email' | 'phone';
}

export default function SettingsPageClient({
  currentEmail,
  currentPhone,
  registrationType,
}: SettingsPageClientProps) {
  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex items-center gap-4">
        <div className="p-3 rounded-xl bg-muted hidden sm:block">
          <Settings className="w-6 h-6 text-foreground" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-foreground">
            Account Settings
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your account security and preferences
          </p>
        </div>
      </div>

      {/* Settings Sections */}
      <div className="space-y-8">
        {/* Link Email - Show for all users with different behaviors */}
        <LinkEmailSection
          currentEmail={currentEmail}
          currentPhone={currentPhone}
          registrationType={registrationType}
        />

        {/* Link Phone - Show for all users with different behaviors */}
        <LinkPhoneSection
          currentEmail={currentEmail}
          currentPhone={currentPhone}
          registrationType={registrationType}
        />

        {/* Password Management - Not for Google users */}
        {registrationType !== 'google' && (
          <PasswordUpdateSection
            registrationType={registrationType}
          />
        )}

        {/* Delete Account Section */}
        <DeleteAccountSection />
      </div>
    </div>
  );
}
