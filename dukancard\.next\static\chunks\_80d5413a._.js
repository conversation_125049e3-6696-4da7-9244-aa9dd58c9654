(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/ui/alert.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Alert": (()=>Alert),
    "AlertDescription": (()=>AlertDescription),
    "AlertTitle": (()=>AlertTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const alertVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current", {
    variants: {
        variant: {
            default: "bg-card text-card-foreground",
            destructive: "text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"
        }
    },
    defaultVariants: {
        variant: "default"
    }
});
function Alert({ className, variant, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert",
        role: "alert",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(alertVariants({
            variant
        }), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/alert.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
}
_c = Alert;
function AlertTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/alert.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
}
_c1 = AlertTitle;
function AlertDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/alert.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
}
_c2 = AlertDescription;
;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "Alert");
__turbopack_context__.k.register(_c1, "AlertTitle");
__turbopack_context__.k.register(_c2, "AlertDescription");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/shared/reviews/ReviewSortDropdown.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ReviewSortDropdown)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$narrow$2d$wide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SortAsc$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js [app-client] (ecmascript) <export default as SortAsc>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const sortOptions = [
    {
        value: "newest",
        label: "Newest First"
    },
    {
        value: "oldest",
        label: "Oldest First"
    },
    {
        value: "highest_rating",
        label: "High to Low Ratings"
    },
    {
        value: "lowest_rating",
        label: "Low to High Ratings"
    }
];
function ReviewSortDropdown({ sortBy, onSortChange, className = "" }) {
    const currentSortLabel = sortOptions.find((option)=>option.value === sortBy)?.label || "Newest First";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 10
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.4,
            delay: 0.2
        },
        className: className,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                    asChild: true,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: "outline",
                        size: "sm",
                        className: "gap-1.5 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$narrow$2d$wide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SortAsc$3e$__["SortAsc"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/app/components/shared/reviews/ReviewSortDropdown.tsx",
                                lineNumber: 54,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Sort: "
                            }, void 0, false, {
                                fileName: "[project]/app/components/shared/reviews/ReviewSortDropdown.tsx",
                                lineNumber: 55,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium text-[var(--brand-gold)]",
                                children: currentSortLabel
                            }, void 0, false, {
                                fileName: "[project]/app/components/shared/reviews/ReviewSortDropdown.tsx",
                                lineNumber: 56,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/shared/reviews/ReviewSortDropdown.tsx",
                        lineNumber: 49,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/shared/reviews/ReviewSortDropdown.tsx",
                    lineNumber: 48,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                    align: "end",
                    className: "w-48 p-1",
                    children: sortOptions.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                            onClick: ()=>onSortChange(option.value),
                            className: `cursor-pointer transition-colors duration-200 ${sortBy === option.value ? "bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] font-medium" : "hover:bg-neutral-100 dark:hover:bg-neutral-800"}`,
                            children: option.label
                        }, option.value, false, {
                            fileName: "[project]/app/components/shared/reviews/ReviewSortDropdown.tsx",
                            lineNumber: 63,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/app/components/shared/reviews/ReviewSortDropdown.tsx",
                    lineNumber: 61,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/shared/reviews/ReviewSortDropdown.tsx",
            lineNumber: 47,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/shared/reviews/ReviewSortDropdown.tsx",
        lineNumber: 41,
        columnNumber: 5
    }, this);
}
_c = ReviewSortDropdown;
var _c;
__turbopack_context__.k.register(_c, "ReviewSortDropdown");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/pagination.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Pagination": (()=>Pagination),
    "PaginationContent": (()=>PaginationContent),
    "PaginationEllipsis": (()=>PaginationEllipsis),
    "PaginationItem": (()=>PaginationItem),
    "PaginationLink": (()=>PaginationLink),
    "PaginationNext": (()=>PaginationNext),
    "PaginationPrevious": (()=>PaginationPrevious)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-client] (ecmascript) <export default as ChevronLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-client] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreHorizontal$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/ellipsis.js [app-client] (ecmascript) <export default as MoreHorizontal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const Pagination = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
        role: "navigation",
        "aria-label": "pagination",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("mx-auto flex w-full justify-center", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 15,
        columnNumber: 3
    }, this);
_c = Pagination;
const PaginationContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c1 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-row items-center gap-1", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 27,
        columnNumber: 3
    }, this));
_c2 = PaginationContent;
PaginationContent.displayName = "PaginationContent";
const PaginationItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c3 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 39,
        columnNumber: 3
    }, this));
_c4 = PaginationItem;
PaginationItem.displayName = "PaginationItem";
const PaginationLink = ({ className, isActive, size = "icon", ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
        "aria-current": isActive ? "page" : undefined,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buttonVariants"])({
            variant: isActive ? "outline" : "ghost",
            size
        }), className, isActive && "bg-muted hover:bg-muted pointer-events-none"),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 54,
        columnNumber: 3
    }, this);
_c5 = PaginationLink;
PaginationLink.displayName = "PaginationLink";
const PaginationPrevious = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PaginationLink, {
        "aria-label": "Go to previous page",
        size: "default",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("gap-1 pl-2.5", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__["ChevronLeft"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 79,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: "Previous"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 80,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 73,
        columnNumber: 3
    }, this);
_c6 = PaginationPrevious;
PaginationPrevious.displayName = "PaginationPrevious";
const PaginationNext = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PaginationLink, {
        "aria-label": "Go to next page",
        size: "default",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("gap-1 pr-2.5", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: "Next"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 95,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 96,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 89,
        columnNumber: 3
    }, this);
_c7 = PaginationNext;
PaginationNext.displayName = "PaginationNext";
const PaginationEllipsis = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        "aria-hidden": true,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex h-9 w-9 items-center justify-center", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreHorizontal$3e$__["MoreHorizontal"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 110,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "sr-only",
                children: "More pages"
            }, void 0, false, {
                fileName: "[project]/components/ui/pagination.tsx",
                lineNumber: 111,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/pagination.tsx",
        lineNumber: 105,
        columnNumber: 3
    }, this);
_c8 = PaginationEllipsis;
PaginationEllipsis.displayName = "PaginationEllipsis";
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;
__turbopack_context__.k.register(_c, "Pagination");
__turbopack_context__.k.register(_c1, "PaginationContent$React.forwardRef");
__turbopack_context__.k.register(_c2, "PaginationContent");
__turbopack_context__.k.register(_c3, "PaginationItem$React.forwardRef");
__turbopack_context__.k.register(_c4, "PaginationItem");
__turbopack_context__.k.register(_c5, "PaginationLink");
__turbopack_context__.k.register(_c6, "PaginationPrevious");
__turbopack_context__.k.register(_c7, "PaginationNext");
__turbopack_context__.k.register(_c8, "PaginationEllipsis");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/actions/data:b2b732 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40b93613196b00eb1d0d8a6194d02d9df73c399df0":"deleteReview"},"lib/actions/interactions.ts",""] */ __turbopack_context__.s({
    "deleteReview": (()=>deleteReview)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var deleteReview = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("40b93613196b00eb1d0d8a6194d02d9df73c399df0", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "deleteReview"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
;
;
function Textarea({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        "data-slot": "textarea",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/textarea.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Textarea;
;
var _c;
__turbopack_context__.k.register(_c, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/shared/reviews/ReviewCard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ReviewCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-client] (ecmascript) <export default as Star>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js [app-client] (ecmascript) <export default as Edit>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-client] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-client] (ecmascript) <export default as ExternalLink>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/building-2.js [app-client] (ecmascript) <export default as Building2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$data$3a$b2b732__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/lib/actions/data:b2b732 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$formatDistanceToNow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/formatDistanceToNow.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/textarea.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
function ReviewCard({ review, onDeleteSuccess, isReviewsReceivedTab = false }) {
    _s();
    const [isDeleting, setIsDeleting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isEditing, setIsEditing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isUpdating, setIsUpdating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [editedRating, setEditedRating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(review.rating);
    const [editedReviewText, setEditedReviewText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(review.review_text || "");
    // Access business_profiles directly from the review object
    const business = review.business_profiles;
    // Handle potential issues with updated_at
    let timeAgo;
    try {
        timeAgo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$formatDistanceToNow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDistanceToNow"])(new Date(review.updated_at || review.created_at), {
            addSuffix: true
        });
    } catch (_error) {
        timeAgo = 'recently';
    }
    // Handle delete
    const handleDelete = async ()=>{
        if (!review.business_profile_id) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Cannot delete review: Missing business information');
            return;
        }
        const confirmation = confirm("Are you sure you want to delete this review?");
        if (!confirmation) return;
        try {
            setIsDeleting(true);
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$data$3a$b2b732__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["deleteReview"])(review.business_profile_id);
            if (result.success) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Review for ${business?.business_name || 'business'} deleted.`);
                if (onDeleteSuccess) {
                    onDeleteSuccess(review.id);
                }
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(`Failed to delete review: ${result.error || 'Unknown error'}`);
            }
        } catch (_error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('An error occurred while deleting the review');
        } finally{
            setIsDeleting(false);
        }
    };
    // Handle edit mode toggle
    const handleEdit = ()=>{
        setIsEditing(true);
    };
    // Handle cancel edit
    const handleCancelEdit = ()=>{
        setIsEditing(false);
        setEditedRating(review.rating);
        setEditedReviewText(review.review_text || "");
    };
    // Handle save edit
    const handleSaveEdit = async ()=>{
        setIsUpdating(true);
        try {
            const response = await fetch("/api/customer/reviews/update", {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    reviewId: review.id,
                    rating: editedRating,
                    reviewText: editedReviewText
                })
            });
            const data = await response.json();
            if (response.ok) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Review updated successfully");
                setIsEditing(false);
                // Update the local review data
                review.rating = editedRating;
                review.review_text = editedReviewText;
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(data.error || "Failed to update review");
            }
        } catch (error) {
            console.error("Error updating review:", error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("An unexpected error occurred");
        } finally{
            setIsUpdating(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.4
        },
        whileHover: !isEditing ? {
            y: -5,
            transition: {
                duration: 0.2
            }
        } : {},
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("rounded-xl border border-neutral-200 dark:border-neutral-800", "bg-white dark:bg-black", "shadow-sm p-4 transition-all duration-300 hover:shadow-md", "relative overflow-hidden group", isEditing && "ring-2 ring-amber-400 dark:ring-amber-600"),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 pointer-events-none opacity-5 dark:opacity-10",
                style: {
                    backgroundImage: `url("/decorative/card-texture.svg")`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundRepeat: "no-repeat"
                }
            }, void 0, false, {
                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                lineNumber: 154,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-10",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start mb-3",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3",
                            children: [
                                review.reviewer_type === 'business' && review.reviewer_slug ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: `/${review.reviewer_slug}`,
                                    target: "_blank",
                                    rel: "noopener noreferrer",
                                    className: "hover:opacity-80 transition-opacity",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                        className: "h-10 w-10 border border-neutral-200 dark:border-neutral-800",
                                        children: [
                                            business?.logo_url ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                                src: business.logo_url,
                                                alt: business?.business_name || "Business"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 178,
                                                columnNumber: 21
                                            }, this) : null,
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                className: "bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300",
                                                children: business?.business_name?.[0]?.toUpperCase() || "B"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 183,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                        lineNumber: 176,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                    lineNumber: 170,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                    className: "h-10 w-10 border border-neutral-200 dark:border-neutral-800",
                                    children: [
                                        business?.logo_url ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                            src: business.logo_url,
                                            alt: business?.business_name || "Business"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                            lineNumber: 191,
                                            columnNumber: 19
                                        }, this) : null,
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                            className: "bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300",
                                            children: business?.business_name?.[0]?.toUpperCase() || "B"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                            lineNumber: 196,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                    lineNumber: 189,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-1",
                                            children: review.reviewer_type === 'business' && review.reviewer_slug ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: `/${review.reviewer_slug}`,
                                                target: "_blank",
                                                rel: "noopener noreferrer",
                                                className: "hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "font-medium text-neutral-800 dark:text-neutral-200",
                                                        children: business?.business_name || "Unknown Business"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                        lineNumber: 212,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__["ExternalLink"], {
                                                        className: "h-3.5 w-3.5 opacity-70"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                        lineNumber: 215,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 206,
                                                columnNumber: 19
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "font-medium text-neutral-800 dark:text-neutral-200",
                                                children: business?.business_name || "Unknown User"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 218,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                            lineNumber: 204,
                                            columnNumber: 15
                                        }, this),
                                        isReviewsReceivedTab && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-1 mt-1",
                                            children: review.reviewer_type === 'business' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "inline-flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__["Building2"], {
                                                        className: "h-3 w-3"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                        lineNumber: 229,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Business"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 228,
                                                columnNumber: 21
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "inline-flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                        className: "h-3 w-3"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                        lineNumber: 234,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Customer"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 233,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                            lineNumber: 226,
                                            columnNumber: 17
                                        }, this),
                                        !isEditing && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-1 mt-1",
                                            children: [
                                                Array.from({
                                                    length: 5
                                                }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
                                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("h-4 w-4", i < review.rating ? "text-amber-500 fill-amber-500" : "text-neutral-300 dark:text-neutral-700")
                                                    }, i, false, {
                                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                        lineNumber: 245,
                                                        columnNumber: 21
                                                    }, this)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-xs text-neutral-500 dark:text-neutral-400 ml-1",
                                                    children: timeAgo
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                    lineNumber: 255,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                            lineNumber: 243,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                    lineNumber: 202,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                            lineNumber: 167,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                        lineNumber: 166,
                        columnNumber: 9
                    }, this),
                    isEditing ? // Edit mode content
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-1",
                                children: Array.from({
                                    length: 5
                                }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>setEditedRating(i + 1),
                                        className: "focus:outline-none",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("h-6 w-6 transition-colors", i < editedRating ? "text-amber-500 fill-amber-500" : "text-neutral-300 dark:text-neutral-700 hover:text-amber-400")
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                            lineNumber: 277,
                                            columnNumber: 19
                                        }, this)
                                    }, i, false, {
                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                        lineNumber: 271,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                lineNumber: 269,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                                value: editedReviewText,
                                onChange: (e)=>setEditedReviewText(e.target.value),
                                placeholder: "What did you like or dislike? What stood out about your experience?",
                                className: "w-full bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 rounded-lg focus:ring-amber-400 focus:border-amber-400 transition-all duration-200 resize-none",
                                maxLength: 500,
                                rows: 4
                            }, void 0, false, {
                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                lineNumber: 290,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-neutral-500 dark:text-neutral-400 mt-1 text-right",
                                children: [
                                    editedReviewText.length,
                                    "/500"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                lineNumber: 298,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-end gap-2 mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        className: "text-xs h-8 gap-1",
                                        onClick: handleCancelEdit,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                                className: "h-3.5 w-3.5"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 310,
                                                columnNumber: 17
                                            }, this),
                                            "Cancel"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                        lineNumber: 304,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "default",
                                        size: "sm",
                                        className: "text-xs h-8 gap-1 bg-amber-500 hover:bg-amber-600 text-white",
                                        onClick: handleSaveEdit,
                                        disabled: isUpdating,
                                        children: [
                                            isUpdating ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                className: "h-3.5 w-3.5 animate-spin"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 322,
                                                columnNumber: 19
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                className: "h-3.5 w-3.5"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 324,
                                                columnNumber: 19
                                            }, this),
                                            "Save"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                        lineNumber: 314,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                lineNumber: 303,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                        lineNumber: 267,
                        columnNumber: 11
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            review.review_text && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-neutral-700 dark:text-neutral-300 text-sm leading-relaxed",
                                    children: review.review_text
                                }, void 0, false, {
                                    fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                    lineNumber: 335,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                lineNumber: 334,
                                columnNumber: 15
                            }, this),
                            !isReviewsReceivedTab && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50",
                                children: [
                                    review.reviewer_type === 'business' && business?.business_slug ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        asChild: true,
                                        variant: "outline",
                                        size: "sm",
                                        className: "text-xs h-8 gap-1",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: `/${business.business_slug}`,
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__["ExternalLink"], {
                                                    className: "h-3.5 w-3.5"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                    lineNumber: 357,
                                                    columnNumber: 23
                                                }, this),
                                                "View Business"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                            lineNumber: 352,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                        lineNumber: 346,
                                        columnNumber: 19
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {}, void 0, false, {
                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                        lineNumber: 362,
                                        columnNumber: 19
                                    }, this) // Empty div to maintain layout when no View Business button
                                    ,
                                    onDeleteSuccess && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                variant: "outline",
                                                size: "sm",
                                                className: "text-xs h-8 gap-1 text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300 dark:text-blue-400 dark:border-blue-900 dark:hover:bg-blue-950/30",
                                                onClick: handleEdit,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__["Edit"], {
                                                        className: "h-3.5 w-3.5"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                        lineNumber: 374,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Edit"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 368,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                variant: "outline",
                                                size: "sm",
                                                className: "text-xs h-8 gap-1 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-900 dark:hover:bg-red-950/30",
                                                onClick: handleDelete,
                                                disabled: isDeleting,
                                                children: [
                                                    isDeleting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "h-3.5 w-3.5 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                        lineNumber: 386,
                                                        columnNumber: 25
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                        className: "h-3.5 w-3.5"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                        lineNumber: 388,
                                                        columnNumber: 25
                                                    }, this),
                                                    "Delete"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                                lineNumber: 378,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                        lineNumber: 367,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                                lineNumber: 343,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
                lineNumber: 164,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/shared/reviews/ReviewCard.tsx",
        lineNumber: 140,
        columnNumber: 5
    }, this);
}
_s(ReviewCard, "i6SI37KiCl3SHxwC8B1s7URqHvA=");
_c = ReviewCard;
var _c;
__turbopack_context__.k.register(_c, "ReviewCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ReviewCardSkeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/skeleton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
function ReviewCardSkeleton({ index = 0 }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.4,
            delay: index * 0.05
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("rounded-xl border border-neutral-200 dark:border-neutral-800", "bg-white dark:bg-black", "shadow-sm p-4 transition-all duration-300", "relative overflow-hidden"),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 pointer-events-none opacity-5 dark:opacity-10",
                style: {
                    backgroundImage: `url("/decorative/card-texture.svg")`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundRepeat: "no-repeat"
                }
            }, void 0, false, {
                fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-10",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start mb-3",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "h-10 w-10 rounded-full"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                    lineNumber: 39,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                                            className: "h-5 w-32 mb-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                            lineNumber: 41,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-1 mt-1",
                                            children: Array.from({
                                                length: 5
                                            }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                                                    className: "h-4 w-4"
                                                }, i, false, {
                                                    fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                                    lineNumber: 45,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                            lineNumber: 43,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                                            className: "h-3 w-24 mt-1"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                            lineNumber: 49,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                    lineNumber: 40,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                            lineNumber: 38,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                            className: "h-20 w-full rounded-lg"
                        }, void 0, false, {
                            fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                            lineNumber: 56,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                        lineNumber: 55,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                                className: "h-8 w-28"
                            }, void 0, false, {
                                fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                lineNumber: 61,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                                        className: "h-8 w-16"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                        lineNumber: 63,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                                        className: "h-8 w-20"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                        lineNumber: 64,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                                lineNumber: 62,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = ReviewCardSkeleton;
var _c;
__turbopack_context__.k.register(_c, "ReviewCardSkeleton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>EnhancedReviewListClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-client] (ecmascript) <export default as Star>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/alert.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$reviews$2f$ReviewSortDropdown$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/reviews/ReviewSortDropdown.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/pagination.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$reviews$2f$ReviewCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/reviews/ReviewCard.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$reviews$2f$ReviewCardSkeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/shared/reviews/ReviewCardSkeleton.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
function EnhancedReviewListClient() {
    _s();
    const [reviews, setReviews] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [sortBy, setSortBy] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("newest");
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [pagination, setPagination] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        perPage: 10
    });
    // Animation variants
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };
    // Fetch reviews from the API
    const fetchReviews = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "EnhancedReviewListClient.useCallback[fetchReviews]": async (page, sort)=>{
            setIsLoading(true);
            setError(null);
            try {
                const queryParams = new URLSearchParams({
                    page: page.toString(),
                    sort: sort
                });
                const response = await fetch(`/api/customer/reviews?${queryParams.toString()}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch reviews');
                }
                const data = await response.json();
                setReviews(data.reviews);
                setPagination(data.pagination);
            } catch (_err) {
                setError('Failed to load reviews. Please try again.');
                setReviews([]);
            } finally{
                setIsLoading(false);
            }
        }
    }["EnhancedReviewListClient.useCallback[fetchReviews]"], []);
    // Fetch reviews when page or sort changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EnhancedReviewListClient.useEffect": ()=>{
            fetchReviews(pagination.currentPage, sortBy);
        }
    }["EnhancedReviewListClient.useEffect"], [
        pagination.currentPage,
        sortBy,
        fetchReviews
    ]);
    // Handle page change
    const handlePageChange = (page)=>{
        setPagination((prev)=>({
                ...prev,
                currentPage: page
            }));
    };
    // Handle review deletion
    const handleDeleteSuccess = (reviewId)=>{
        setReviews((prevReviews)=>prevReviews.filter((r)=>r.id !== reviewId));
        // If we deleted the last review on the page, go to previous page
        if (reviews.length === 1 && pagination.currentPage > 1) {
            handlePageChange(pagination.currentPage - 1);
        } else {
            // Refresh the current page
            fetchReviews(pagination.currentPage, sortBy);
        }
    };
    // Handle sort change
    const handleSortChange = (newSortBy)=>{
        setSortBy(newSortBy);
        // Reset to first page when sorting changes
        setPagination((prev)=>({
                ...prev,
                currentPage: 1
            }));
    };
    // Generate pagination items
    const generatePaginationItems = ()=>{
        const items = [];
        const { currentPage, totalPages } = pagination;
        // Always show first page
        items.push(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationItem"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationLink"], {
                href: "#",
                onClick: (e)=>{
                    e.preventDefault();
                    handlePageChange(1);
                },
                isActive: currentPage === 1,
                children: "1"
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                lineNumber: 139,
                columnNumber: 9
            }, this)
        }, "page-1", false, {
            fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
            lineNumber: 138,
            columnNumber: 7
        }, this));
        // Show ellipsis if needed
        if (currentPage > 3) {
            items.push(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationItem"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationEllipsis"], {}, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                    lineNumber: 156,
                    columnNumber: 11
                }, this)
            }, "ellipsis-1", false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                lineNumber: 155,
                columnNumber: 9
            }, this));
        }
        // Show pages around current page
        for(let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++){
            if (i <= 1 || i >= totalPages) continue; // Skip first and last pages as they're always shown
            items.push(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationItem"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationLink"], {
                    href: "#",
                    onClick: (e)=>{
                        e.preventDefault();
                        handlePageChange(i);
                    },
                    isActive: currentPage === i,
                    children: i
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                    lineNumber: 166,
                    columnNumber: 11
                }, this)
            }, `page-${i}`, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                lineNumber: 165,
                columnNumber: 9
            }, this));
        }
        // Show ellipsis if needed
        if (currentPage < totalPages - 2) {
            items.push(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationItem"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationEllipsis"], {}, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                    lineNumber: 184,
                    columnNumber: 11
                }, this)
            }, "ellipsis-2", false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                lineNumber: 183,
                columnNumber: 9
            }, this));
        }
        // Always show last page if there's more than one page
        if (totalPages > 1) {
            items.push(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationItem"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationLink"], {
                    href: "#",
                    onClick: (e)=>{
                        e.preventDefault();
                        handlePageChange(totalPages);
                    },
                    isActive: currentPage === totalPages,
                    children: totalPages
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                    lineNumber: 193,
                    columnNumber: 11
                }, this)
            }, `page-${totalPages}`, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                lineNumber: 192,
                columnNumber: 9
            }, this));
        }
        return items;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-end mb-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$reviews$2f$ReviewSortDropdown$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    sortBy: sortBy,
                    onSortChange: handleSortChange,
                    className: "sm:w-auto"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                    lineNumber: 215,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                lineNumber: 213,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alert"], {
                variant: "destructive",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                        className: "h-4 w-4"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                        lineNumber: 227,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertTitle"], {
                        children: "Error"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                        lineNumber: 228,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDescription"], {
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                        lineNumber: 229,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                lineNumber: 226,
                columnNumber: 9
            }, this),
            isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                children: Array.from({
                    length: 4
                }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$reviews$2f$ReviewCardSkeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        index: index
                    }, index, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                        lineNumber: 237,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                lineNumber: 235,
                columnNumber: 9
            }, this) : reviews.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        variants: containerVariants,
                        initial: "hidden",
                        animate: "visible",
                        className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                        children: reviews.map((review)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$shared$2f$reviews$2f$ReviewCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                review: review,
                                onDeleteSuccess: handleDeleteSuccess
                            }, review.id, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                lineNumber: 249,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                        lineNumber: 242,
                        columnNumber: 11
                    }, this),
                    pagination.totalPages > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Pagination"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationContent"], {
                                children: [
                                    pagination.currentPage > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationItem"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                            whileHover: {
                                                x: -2
                                            },
                                            whileTap: {
                                                scale: 0.95
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationPrevious"], {
                                                href: "#",
                                                onClick: (e)=>{
                                                    e.preventDefault();
                                                    handlePageChange(pagination.currentPage - 1);
                                                },
                                                className: "border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                                lineNumber: 268,
                                                columnNumber: 25
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                            lineNumber: 264,
                                            columnNumber: 23
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                        lineNumber: 263,
                                        columnNumber: 21
                                    }, this),
                                    generatePaginationItems().map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                            initial: {
                                                opacity: 0,
                                                scale: 0.8
                                            },
                                            animate: {
                                                opacity: 1,
                                                scale: 1
                                            },
                                            transition: {
                                                duration: 0.3,
                                                delay: 0.1 + index * 0.05
                                            },
                                            children: item
                                        }, `pagination-${index}`, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                            lineNumber: 281,
                                            columnNumber: 21
                                        }, this)),
                                    pagination.currentPage < pagination.totalPages && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationItem"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                            whileHover: {
                                                x: 2
                                            },
                                            whileTap: {
                                                scale: 0.95
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PaginationNext"], {
                                                href: "#",
                                                onClick: (e)=>{
                                                    e.preventDefault();
                                                    handlePageChange(pagination.currentPage + 1);
                                                },
                                                className: "border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                                lineNumber: 297,
                                                columnNumber: 25
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                            lineNumber: 293,
                                            columnNumber: 23
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                        lineNumber: 292,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                lineNumber: 261,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                            lineNumber: 260,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                        lineNumber: 259,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center py-12",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md mx-auto",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-16 h-16 mx-auto bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
                                    className: "w-6 h-6 text-amber-600 dark:text-amber-400"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                    lineNumber: 318,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                                lineNumber: 317,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                            lineNumber: 316,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg font-medium text-neutral-800 dark:text-neutral-100 mb-2",
                            children: "No reviews found"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                            lineNumber: 321,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-neutral-500 dark:text-neutral-400 mb-6",
                            children: "You haven't written any reviews yet."
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                            lineNumber: 324,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                    lineNumber: 315,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
                lineNumber: 314,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx",
        lineNumber: 211,
        columnNumber: 5
    }, this);
}
_s(EnhancedReviewListClient, "Jafg1kMMaN3WlA9CfmGAXntjNBI=");
_c = EnhancedReviewListClient;
var _c;
__turbopack_context__.k.register(_c, "EnhancedReviewListClient");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ReviewsPageClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-client] (ecmascript) <export default as Star>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$reviews$2f$components$2f$EnhancedReviewListClient$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
function ReviewsPageClient({ reviewsCount }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-3 rounded-xl bg-muted hidden sm:block",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
                            className: "w-6 h-6 text-amber-600 dark:text-amber-400"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx",
                            lineNumber: 17,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx",
                        lineNumber: 16,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-foreground",
                                children: "Your Reviews"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx",
                                lineNumber: 20,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground mt-1",
                                children: [
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatIndianNumberShort"])(reviewsCount),
                                    " ",
                                    reviewsCount === 1 ? 'review' : 'reviews',
                                    " you've written for businesses"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx",
                                lineNumber: 23,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx",
                        lineNumber: 19,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx",
                lineNumber: 15,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$reviews$2f$components$2f$EnhancedReviewListClient$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx",
                    lineNumber: 32,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx",
                lineNumber: 30,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = ReviewsPageClient;
var _c;
__turbopack_context__.k.register(_c, "ReviewsPageClient");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_80d5413a._.js.map