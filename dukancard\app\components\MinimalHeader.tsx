"use client";

import React from "react";
import { usePathname } from "next/navigation";
// Removed unused imports: useRout<PERSON>, createClient
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";
// Removed unused ThemeToggle import
import { signOutUser } from "@/app/auth/actions"; // Import the server action
// Removed unused Sheet import

interface MinimalHeaderProps {
  children?: React.ReactNode;
  // Updated props
  businessName?: string | null; // For business context or fallback
  logoUrl?: string | null;
  userName?: string | null; // Added for user's actual name
}

// Helper to get initials - prioritize userName if available, else businessName
const getInitials = (userName?: string | null, businessName?: string | null): string => {
  const nameToUse = userName || businessName; // Use user name first for avatar
  if (!nameToUse) return "?";
  const names = nameToUse.trim().split(/\s+/);
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return (
    names[0].charAt(0).toUpperCase() +
    names[names.length - 1].charAt(0).toUpperCase()
  );
};

// Helper to get current page name from pathname
const getPageName = (pathname: string): string => {
  const pathSegments = pathname.split('/').filter(Boolean);

  // Handle customer dashboard routes
  if (pathSegments.includes('customer')) {
    const lastSegment = pathSegments[pathSegments.length - 1];

    switch (lastSegment) {
      case 'customer':
        return 'Feed';
      case 'overview':
        return 'Overview';
      case 'likes':
        return 'My Likes';
      case 'subscriptions':
        return 'Subscriptions';
      case 'reviews':
        return 'My Reviews';
      case 'profile':
        return 'Profile';
      case 'settings':
        return 'Settings';
      default:
        return 'Dashboard';
    }
  }

  // Handle business dashboard routes
  if (pathSegments.includes('business')) {
    return 'Business Dashboard';
  }

  // Default fallback
  return 'Dashboard';
};

const MinimalHeader: React.FC<MinimalHeaderProps> = ({
  children,
  businessName: propBusinessName,
  logoUrl: propLogoUrl,
  userName: propUserName, // Added prop
}) => {
  // Use props directly for now - context can be added back later if needed
  const businessName = propBusinessName;
  const logoUrl = propLogoUrl;
  const userName = propUserName;

  // Get current pathname and page name
  const pathname = usePathname();
  const currentPageName = getPageName(pathname);

  // Initials logic updated to prioritize userName
  const initials = getInitials(userName, businessName);

  // Determine display name for the dropdown
  let displayName = "User"; // Default fallback
  if (userName && businessName) {
    displayName = `${businessName} (${userName})`; // Business context
  } else if (userName) {
    displayName = userName; // Customer context
  } else if (businessName) {
    displayName = businessName; // Fallback if only business name exists
  }


  // Access children assuming specific order from layout: [Sheet, Button, ThemeToggle]
  const childArray = React.Children.toArray(children);
  const mobileSheetTrigger = childArray[0]; // Assumes Sheet is the first child
  const desktopToggleButton = childArray[1]; // Assumes Button is the second child
  const themeToggleElement = childArray[2]; // Assumes ThemeToggle is the third child

  return (
    <header className="sticky top-0 z-40 w-full border-b border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80">
      {/* Enhanced container with better spacing */}
      <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6 lg:px-8">
        {/* Left Section: Mobile Toggle -> Logo -> Desktop Toggle */}
        <div className="flex items-center space-x-2 md:space-x-4">
          {/* Render Mobile Sheet Trigger First */}
          {mobileSheetTrigger}

          {/* Current page name instead of logo */}
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-foreground">
              {currentPageName}
            </h1>
          </div>

          {/* Render Desktop Toggle Button after logo */}
          {desktopToggleButton}
        </div>

        {/* Right Section: User Menu + Theme Toggle */}
        <div className="flex items-center space-x-2 md:space-x-4">
          {/* Enhanced User Profile with Name Display */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="cursor-pointer flex items-center gap-3 h-10 px-3 rounded-lg hover:bg-accent/50 transition-all duration-200 focus-visible:ring-0 focus-visible:ring-offset-0"
              >
                <Avatar className="h-8 w-8 border-2 border-border hover:border-primary/50 transition-all duration-200">
                  {/* Use logoUrl for avatar image only if it exists */}
                  {logoUrl ? (
                    <AvatarImage
                      src={logoUrl}
                      alt={userName || businessName || "User"}
                    />
                  ) : null}
                  <AvatarFallback className="bg-gradient-to-br from-primary/10 to-primary/5 text-primary font-semibold text-sm">
                    {initials}
                  </AvatarFallback>
                </Avatar>
                {/* Display user name on larger screens */}
                <span className="hidden sm:block text-sm font-medium text-foreground max-w-32 truncate">
                  {userName || businessName || "User"}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-64" align="end" forceMount> {/* Increased width slightly */}
              {/* Updated Dropdown Label */}
              <DropdownMenuLabel className="font-normal">
                <p className="text-sm font-medium leading-none truncate py-2">
                  {displayName} {/* Use the combined/correct display name */}
                </p>
                {/* Optionally add email back if needed, maybe only for customers? */}
                {/* {userName && !businessName && userEmail && (
                  <p className="text-xs leading-none text-muted-foreground truncate pt-1">
                    {userEmail}
                  </p>
                )} */}
              </DropdownMenuLabel>
              {/* Removed email display and extra separator */}
              {/* <DropdownMenuSeparator /> */}
              {/* Add links to profile/settings if needed */}
              {/* <DropdownMenuItem asChild>
                <Link href="/dashboard/profile">
                  <UserIcon className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
              </DropdownMenuItem> */}
              <DropdownMenuSeparator />
              {/* Logout Button using Server Action */}
              <form action={signOutUser} className="w-full px-2 py-1.5">
                <Button
                  type="submit"
                  variant="ghost"
                  className="w-full justify-start font-normal text-sm h-auto py-1 cursor-pointer"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </Button>
              </form>
            </DropdownMenuContent>
          </DropdownMenu>
          {themeToggleElement} {/* Render theme toggle last */}
        </div>
      </div>
    </header>
  );
};

export default MinimalHeader;
